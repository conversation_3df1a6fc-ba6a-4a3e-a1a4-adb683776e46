package db

import (
	"bufio"
	"context"
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"embed"
	"fmt"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/migrate"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

var Connection *bun.DB

var MainServerDBConnString string

const maxRetries = 10

//go:embed migrations/*.sql
var migrationFiles embed.FS

// Migrations collection for Bun migration system
var Migrations = migrate.NewMigrations()

func init() {
	// Discover SQL migration files
	if err := Migrations.Discover(migrationFiles); err != nil {
		// Log error but don't panic during init
		// The error will be handled when migrations are actually run
	}
}

type dbLogger struct{}

func (q *dbLogger) BeforeQuery(ctx context.Context, _ *bun.QueryEvent) context.Context {
	return ctx
}

func (q *dbLogger) AfterQuery(_ context.Context, event *bun.QueryEvent) {
	if event.Err == nil {
		logger.DBLogger.Info("query: ", event.Query)
	}
}

func Connect() (*bun.DB, error) {
	currentDir := common.CurrentWorkingDir()
	dbHost := common.GetEnv("DB_HOST", "localhost")
	dbPort := common.GetEnv("DB_PORT", strconv.Itoa(5432))
	dbUser := common.GetEnv("DB_USER", "postgres")
	dbPass := common.GetEnv("DB_PASS", "Zirozen@ziro123987")
	dbName := common.GetEnv("DB_NAME", "manager")
	poolSize, _ := strconv.Atoi(common.GetEnv("DB_POOL_SIZE", "10"))
	maxIdleConnection, _ := strconv.Atoi(common.GetEnv("DB_MAX_IDLE_CONNECTION", "5"))
	idleConnectionTimeout, _ := strconv.Atoi(common.GetEnv("DB_IDLE_CONNECTION_TIMEOUT", "5"))
	recreateDB := common.GetEnv("RECREATE_DB", "false")
	logQuery := common.GetEnv("ENABLE_DB_QUERY_LOGGER", "false")

	serverDbHost := common.GetEnv("SERVER_DB_HOST", "localhost")
	serverDbPort := common.GetEnv("SERVER_DB_PORT", strconv.Itoa(5432))
	serverDbUser := common.GetEnv("SERVER_DB_USER", "postgres")
	serverDbPass := common.GetEnv("SERVER_DB_PASS", "Zirozen@ziro123987")
	serverDbName := common.GetEnv("SERVER_DB_NAME", "manager")

	MainServerDBConnString = fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", serverDbUser, serverDbPass, serverDbHost, serverDbPort, serverDbName)
	dbConnString := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", dbUser, dbPass, dbHost, dbPort, dbName)
	var pool *pgxpool.Pool
	var err error
	baseRetryInterval := 5 * time.Second
	for i := 0; i < maxRetries; i++ {
		logger.ServiceLogger.Info(fmt.Sprintf("Attempting DB connection (%d/%d)...", i+1, maxRetries))

		config, configErr := pgxpool.ParseConfig(dbConnString)
		if configErr != nil {
			logger.ServiceLogger.Info(fmt.Sprintf("Error parsing config: %v", configErr))
			time.Sleep(baseRetryInterval)
			continue
		}

		config.MaxConns = int32(poolSize)
		config.MinConns = int32(maxIdleConnection)
		config.MaxConnIdleTime = time.Duration(idleConnectionTimeout) * time.Minute

		pool, err = pgxpool.NewWithConfig(context.Background(), config)
		if err != nil {
			logger.ServiceLogger.Info(fmt.Sprintf("Connection pool creation failed: %v", err))
			time.Sleep(baseRetryInterval)
			continue
		}

		// 🔥 Force actual DB connection
		if err = pool.Ping(context.Background()); err != nil {
			logger.ServiceLogger.Info(fmt.Sprintf("Ping failed: %v", err))
			pool.Close() // prevent resource leak
			// Exponential backoff: increase delay
			sleep := time.Duration(i+1) * baseRetryInterval
			logger.ServiceLogger.Info(fmt.Sprintf("Retrying after %s...", sleep))
			time.Sleep(sleep)
			continue
		}

		logger.ServiceLogger.Info("Successfully connected to database.")
		break
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to DB after %d retries: %w", maxRetries, err)
	}

	if pool != nil {
		sqlDB := stdlib.OpenDBFromPool(pool)
		Connection = bun.NewDB(sqlDB, pgdialect.New())
		logDbQuery, _ := strconv.ParseBool(logQuery)
		if logDbQuery {
			Connection.AddQueryHook(&dbLogger{})
		}
		reCreateDb, _ := strconv.ParseBool(recreateDB)
		if reCreateDb {
			err := dropAndCreateSchema(Connection, dbUser)
			if err != nil {
				return nil, err
			}
			err = createSchemaTable(Connection)
			if err != nil {
				return nil, err
			}
			ExecuteCommand(currentDir)
			ExecuteOnBoardQueryFile(err, dbConnString, currentDir)
		} else {
			// Run migrations when not recreating database
			err := runBunMigrations(Connection)
			if err != nil {
				logger.ServiceLogger.Error("Migration failed: ", err)
				return nil, fmt.Errorf("migration failed: %w", err)
			}
		}
	}
	return Connection, nil
}

func ExecuteOnBoardQueryFile(err error, dbConnString string, currentDir string) {
	conn, err := pgx.Connect(context.Background(), dbConnString)
	if err != nil {
		logger.ServiceLogger.Error("Unable to connect to database: %v\n", err)
	}
	defer func(conn *pgx.Conn, ctx context.Context) {
		err = conn.Close(ctx)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(conn, context.Background())

	file, err := os.Open(filepath.Join(currentDir, "db", "fresh", "on_board_query.sql"))
	if err != nil {
		logger.ServiceLogger.Error("Unable to open SQL file: %v\n", err)
	}
	defer func(file *os.File) {
		err = file.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(file)

	scanner := bufio.NewScanner(file)

	tx, err := conn.Begin(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("Unable to start transaction: %v\n", err)
		os.Exit(1)
	}
	defer func(tx pgx.Tx, ctx context.Context) {
		err = tx.Rollback(ctx)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(tx, context.Background())

	for scanner.Scan() {
		_, err = tx.Exec(context.Background(), scanner.Text())
		if err != nil {
			logger.ServiceLogger.Error("Error executing SQL statement: %v\n", err)
		}
	}

	err = tx.Commit(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("Error committing transaction: %v\n", err)
	}
}

func ExecuteCommand(currentDir string) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("Error")
		}
	}()
	if strings.Contains(currentDir, "zirozen") {
		err := exec.Command("sed", "-i", `s/RECREATE_DB="true"/RECREATE_DB="false"/`, common.PrepareFilePath(currentDir, "app.config")).Run()
		if err != nil {
			logger.ServiceLogger.Error(err)
			return
		}
	} else {
		err := exec.Command("sed", "-i", `s/RECREATE_DB="true"/RECREATE_DB="false"/`, common.PrepareFilePath(currentDir, "app1.config")).Run()
		if err != nil {
			logger.ServiceLogger.Error(err)
			return
		}
	}
}

func dropAndCreateSchema(conn *bun.DB, dbUser string) error {
	ctx := context.Background()
	_, err := conn.NewSelect().ColumnExpr("schema_name").
		TableExpr("information_schema.schemata").
		Where("schema_name = 'deployment'").Exec(ctx)
	if err == nil {
		_, err = conn.Exec("DROP SCHEMA IF EXISTS deployment CASCADE")
		if err != nil {
			return err
		}
	}
	_, err = conn.Exec("CREATE SCHEMA deployment AUTHORIZATION " + dbUser)
	return err
}

func createSchemaTable(conn *bun.DB) error {
	ctx := context.Background()
	for _, tableModel := range models() {
		_, err := conn.NewCreateTable().Model(tableModel).IfNotExists().Exec(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

// runBunMigrations executes database migrations using Bun's migration system
func runBunMigrations(conn *bun.DB) error {
	ctx := context.Background()

	// Ensure deployment schema exists
	err := ensureDeploymentSchema(conn)
	if err != nil {
		return fmt.Errorf("failed to ensure deployment schema: %w", err)
	}

	// Create all model tables if they don't exist
	err = createSchemaTable(conn)
	if err != nil {
		return fmt.Errorf("failed to create schema tables: %w", err)
	}

	// Create migrator with custom table name in deployment schema
	migrator := migrate.NewMigrator(conn, Migrations,
		migrate.WithTableName("deployment.bun_migrations"),
		migrate.WithLocksTableName("deployment.bun_migration_locks"),
	)

	// Initialize migration tables
	err = migrator.Init(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize migrator: %w", err)
	}

	// Run pending migrations
	group, err := migrator.Migrate(ctx)
	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	if group.IsZero() {
		logger.ServiceLogger.Info("No new migrations to apply")
	} else {
		logger.ServiceLogger.Info("Applied migration group: ", group.String())
	}

	return nil
}

// ensureDeploymentSchema creates the deployment schema if it doesn't exist
func ensureDeploymentSchema(conn *bun.DB) error {
	ctx := context.Background()

	// Check if schema exists
	var schemaExists bool
	err := conn.NewSelect().
		ColumnExpr("EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'deployment')").
		Scan(ctx, &schemaExists)

	if err != nil {
		return err
	}

	// Create schema if it doesn't exist
	if !schemaExists {
		_, err = conn.Exec("CREATE SCHEMA deployment")
		if err != nil {
			return err
		}
		logger.ServiceLogger.Info("Created deployment schema")
	}

	return nil
}

func models() []interface{} {
	models := []interface{}{
		(*model.Package)(nil),
		(*model.FileData)(nil),
		(*model.Audit)(nil),
		(*model.DeploymentPolicy)(nil),
		(*model.Deployment)(nil),
		(*model.AgentTask)(nil),
		(*model.Configuration)(nil),
		(*model.DeploymentBundle)(nil),
		(*model.AgentTaskResult)(nil),
		(*model.Compliance)(nil),
		(*model.ComplianceTaskResult)(nil),
		(*model.Patch)(nil),
		(*model.PatchSetting)(nil),
		(*model.WindowsPatch)(nil),
		(*model.AssetPatchRelation)(nil),
		(*model.Language)(nil),
		(*model.PatchProduct)(nil),
		(*model.PatchOsApplication)(nil),
		(*model.PatchAssetApplication)(nil),
		(*model.Asset)(nil),
		(*model.SystemAction)(nil),
		(*model.UbuntuPatch)(nil),
		(*model.UbuntuReleasePackage)(nil),
		(*model.UbuntuNoticeData)(nil),
		(*model.LinuxPackage)(nil),
		(*model.PatchPreference)(nil),
		(*model.LinuxOsApplication)(nil),
		(*model.ComputerGroup)(nil),
		(*model.PatchDeclinePolicy)(nil),
		(*model.AutoPatchDeploy)(nil),
		(*model.AutoPatchTest)(nil),
		(*model.MacOsPatch)(nil),
		(*model.MsrcVulnerability)(nil),
		(*model.ThirdPartyPackage)(nil),
		(*model.FileServerConfig)(nil),
		(*model.CabSyncHistory)(nil),
	}
	return models
}
