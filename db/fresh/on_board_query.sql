CREATE OR REPLACE VIEW deployment.agent_task_details_view AS SELECT at.id, at.name, at.created_by_id AS created_by, at.created_time, at.updated_by_id AS updated_by, at.updated_time, at.oob, at.removed, at.ref_id, at.ref_model, at.agent_id, at.deployment_id, CASE at.task_type WHEN 1 THEN 'deployment'::text WHEN 2 THEN 'patch_scan'::text WHEN 3 THEN 'query_execution'::text WHEN 4 THEN 'system_action'::text ELSE 'unknown'::text END AS task_type, at.custom_task_details, CASE at.task_status WHEN 1 THEN 'waiting'::text WHEN 2 THEN 'ready_to_deploy'::text WHEN 3 THEN 'initiated'::text WHEN 4 THEN 'in_progress'::text WHEN 5 THEN 'success'::text WHEN 6 THEN 'failed'::text WHEN 7 THEN 'cancelled'::text WHEN 8 THEN 'reboot_required'::text ELSE 'unknown'::text END AS status, at.task_result, at.has_multiple_execution, p.id AS patch_id, p.name AS patch_name, p.title AS patch_title, p.description AS patch_description, p.download_status AS patch_download_status, pkg.id AS package_id, pkg.name AS package_name, pkg.display_name AS package_display_name, pkg.description AS package_description, pkg.version AS package_version, cfg.id AS configuration_id, cfg.display_name AS configuration_display_name, cfg.description AS configuration_description, cfg.configuration_type, sa.id AS system_action_id, sa.name AS system_action_name, sa.description AS system_action_description, sa.disabled AS system_action_disabled, sa.windows_command AS system_action_windows_command, sa.linux_command AS system_action_linux_command, sa.mac_command AS system_action_mac_command FROM deployment.agent_tasks at LEFT JOIN deployment.patch p ON at.ref_model = 'Patch'::text AND at.ref_id = p.id LEFT JOIN deployment.packages pkg ON at.ref_model = 'package'::text AND at.ref_id = pkg.id LEFT JOIN deployment.configurations cfg ON at.ref_model = 'configuration'::text AND at.ref_id = cfg.id LEFT JOIN deployment.system_actions sa ON at.ref_model = 'system_actions'::text AND at.ref_id = sa.id;
CREATE OR REPLACE VIEW deployment.asset_patch_cve AS SELECT apr.asset_id,pch.cve_number FROM deployment.asset_patch_relation apr LEFT JOIN deployment.patch pch ON apr.patch_id = pch.id;
CREATE OR REPLACE VIEW view_asset_vulnerability AS SELECT DISTINCT swc.cve,swc.software_id,cvd.severity,sw.name,sw.version,cvd.score,cvd.cvss3_base_score,cvd.cvss2_base_score,cvd.cve_context,cvd.mitre_threat,cvd.threat_intel_details,cvd.epss_probability,cvd.cisa_known_exploit,cvd.description,cvd.kb_article,cvd.kb_article_url,cvd.cvss40_base_score,cvd.published_date,cvd.cvss2_vector,cvd.cvss2_access_vector,cvd.cvss2_access_complexity,cvd.cvss2_authentication,cvd.cvss2_confidentiality_impact,cvd.cvss2_integrity_impact,cvd.cvss2_availability_impact,cvd.cvss2_severity,cvd.cvss2_exploitability_score,cvd.cvss2_impact_score,cvd.cvss3_vector_string,cvd.cvss3_attack_vector,cvd.cvss3_attack_complexity,cvd.cvss3_privileges_required,cvd.cvss3_scope,cvd.cvss3_confidentiality_impact,cvd.cvss3_integrity_impact,cvd.cvss3_availability_impact,cvd.cvss3_base_severity,cvd.cvss3_exploitability_score,cvd.cvss3_impact_score,cvd.cvss40_source,cvd.cvss40_type,cvd.cvss40_vector_string,cvd.cvss40_base_severity,cvd.cvss40_threat_score,cvd.cvss40_threat_severity,cvd.cvss40_environmental_score,cvd.cvss40_environmental_severity,asw.modified_time AS resolved_time,swc.discovered_time,swc.resolved_version,swc.zero_day,asw.asset_id,asw.archived,ap.asset_id IS NOT NULL AS has_patch FROM tbl_software_cves swc LEFT JOIN tbl_cve_details cvd ON swc.cve::text = cvd.cve::text LEFT JOIN tbl_asset_software asw ON swc.software_id = asw.software_id LEFT JOIN tbl_software sw ON sw.id = asw.software_id LEFT JOIN deployment.asset_patch_cve ap ON ap.asset_id = asw.asset_id AND ap.cve_number ~~ concat('%', swc.cve, '%');
CREATE OR REPLACE VIEW deployment.asset_patch AS SELECT apr.id, apr.name AS apr_name, apr.created_by_id, apr.created_time, apr.updated_by_id, apr.updated_time, apr.oob AS apr_oob, apr.removed, apr.patch_id, apr.asset_id, apr.patch_state, apr.is_old, apr.is_declined, ta.id AS ta_id, ta.created_time as create_time, ta.modified_time, ta.detail_update_time, ta.host_name, ta.uuid, ta.arch, ta.code_name, ta.major, ta.minor, ta.name, ta.patch, ta.platform, ta.platform_like, ta.os_version, ta.agent_version, ta.platform_vendor, ta.physical_memory, ta.cpu_type, ta.cpu_subtype, ta.cpu_brand, ta.cpu_physical_cores, ta.cpu_logical_cores, ta.hardware_vendor, ta.hardware_model, ta.hardware_version, ta.hardware_serial, ta.department, ta.user, ta.location, ta.build, ta.used_disk_space, ta.free_disk_space, ta.total_disk_space, ta.kernel_version, ta.uptime, ta.free_memory, ta.bssid, ta.used_memory, ta.risk_score, ta.archived, ta.platform_version, pch.name AS patch_name, pch.title as title,pch.cve_number, pch.kb_id FROM deployment.asset_patch_relation apr JOIN tbl_asset ta ON apr.asset_id = ta.id JOIN deployment.patch pch ON apr.patch_id = pch.id  where apr.is_old = false and ta.archived = 0;
CREATE OR REPLACE VIEW deployment.view_asset_patch_cve_relation AS WITH pch_cve_relation AS ( WITH split_cves AS (SELECT asset_patch.patch_name,asset_patch.asset_id,asset_patch.kb_id,unnest(string_to_array(asset_patch.cve_number, ', '::text)) AS cve_id FROM deployment.asset_patch) SELECT string_agg(s.patch_name::text, ', '::text) AS patch_name,s.kb_id,s.asset_id AS ast_id,s.cve_id FROM split_cves s GROUP BY s.asset_id, s.cve_id, s.kb_id) SELECT pcr.patch_name,pcr.kb_id,vav.cve,vav.software_id,vav.severity,vav.name,vav.version,vav.score,vav.cvss3_base_score,vav.cvss2_base_score,vav.cve_context,vav.mitre_threat,vav.threat_intel_details,vav.epss_probability,vav.cisa_known_exploit,vav.description,vav.kb_article,vav.kb_article_url,vav.cvss40_base_score,vav.resolved_time,vav.discovered_time,vav.resolved_version,vav.published_date,vav.zero_day,vav.asset_id,vav.archived,vav.has_patch,vav.cvss2_vector,vav.cvss2_access_vector,vav.cvss2_access_complexity,vav.cvss2_authentication,vav.cvss2_confidentiality_impact,vav.cvss2_integrity_impact,vav.cvss2_availability_impact,vav.cvss2_severity,vav.cvss2_exploitability_score,vav.cvss2_impact_score,vav.cvss3_vector_string,vav.cvss3_attack_vector,vav.cvss3_attack_complexity,vav.cvss3_privileges_required,vav.cvss3_scope,vav.cvss3_confidentiality_impact,vav.cvss3_integrity_impact,vav.cvss3_availability_impact,vav.cvss3_base_severity,vav.cvss3_exploitability_score,vav.cvss3_impact_score,vav.cvss40_source,vav.cvss40_type,vav.cvss40_vector_string,vav.cvss40_base_severity,vav.cvss40_threat_score,vav.cvss40_threat_severity,vav.cvss40_environmental_score,vav.cvss40_environmental_severity FROM pch_cve_relation pcr LEFT JOIN view_asset_vulnerability vav ON vav.cve::text = pcr.cve_id AND vav.asset_id = pcr.ast_id WHERE vav.score IS NOT NULL AND vav.archived = 0;
CREATE INDEX IF NOT EXISTS idx_1_asset_patch_relation ON deployment.asset_patch_relation (asset_id,patch_id);
CREATE INDEX IF NOT EXISTS idx_2_asset_patch_relation ON deployment.asset_patch_relation (asset_id,patch_state);
CREATE INDEX IF NOT EXISTS idx_3_asset_patch_relation ON deployment.asset_patch_relation (patch_id,patch_state);
CREATE INDEX IF NOT EXISTS idx_1_compliance ON deployment.compliances (display_name,name);
CREATE INDEX IF NOT EXISTS idx_1_deployment ON deployment.deployments (display_name,name);
CREATE INDEX IF NOT EXISTS idx_1_deployment_bundle ON deployment.deployment_bundles (name);
CREATE INDEX IF NOT EXISTS idx_1_deployment_policies ON deployment.deployment_policies (display_name,name);
CREATE INDEX IF NOT EXISTS idx_1_file_data ON deployment.file_data (ref_name);
CREATE INDEX IF NOT EXISTS idx_1_packages ON deployment.packages (display_name,name);
CREATE INDEX IF NOT EXISTS idx_1_windows_patches ON deployment.windows_patches (uuid);
CREATE INDEX IF NOT EXISTS idx_1_ubuntu_patches ON deployment.ubuntu_patches (uuid);
CREATE INDEX IF NOT EXISTS idx_id_patch ON deployment.patch USING btree(id);
CREATE INDEX IF NOT EXISTS idx_cve_msrc_vulnerabilities ON deployment.msrc_vulnerabilities USING btree (cve);
CREATE INDEX IF NOT EXISTS idx_ubuntu_release_packages_name_and_version ON deployment.ubuntu_release_packages USING btree (name_and_version ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_ubuntu_release_packages_name_and_version_unique_key ON deployment.ubuntu_release_packages USING btree (name_and_version_unique_key ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_linux_packages_name_with_distro ON deployment.linux_packages USING btree (name_with_distro ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_ubuntu_patches_package_name ON deployment.ubuntu_patches USING btree (package_name ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_ubuntu_patches_package_name_os_version_downloadable_arch ON deployment.ubuntu_patches USING btree (package_name ASC NULLS LAST, os_version ASC NULLS LAST, downloadable ASC NULLS LAST, arch ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_patch_products_uuid ON deployment.patch_products USING btree (uuid ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_ubuntu_notice_data_notice_id ON deployment.ubuntu_notice_data USING btree (notice_id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_mac_patches_product_key ON deployment.mac_patches USING btree (product_key ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_third_party_packages_uuid ON deployment.third_party_packages USING btree (uuid ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_task_status ON deployment.agent_tasks USING btree (task_status ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_task_type ON deployment.agent_tasks USING btree (task_type ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_agent_tasks_task_type_task_status ON deployment.agent_tasks USING btree (task_type ASC NULLS LAST, task_status ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_agent_task_results_id ON deployment.agent_task_results USING btree (id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_agent_task_results_task_status ON deployment.agent_task_results USING btree (task_status ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_audits_audit_event_model ON deployment.audits USING btree (audit_event_model ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_audits_audit_event_type ON deployment.audits USING btree (audit_event_type ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_audits_created_time ON deployment.audits USING btree (created_time DESC NULLS FIRST);
CREATE INDEX IF NOT EXISTS idx_configurations_created_time ON deployment.configurations USING btree (created_time DESC NULLS FIRST);
CREATE INDEX IF NOT EXISTS idx_configurations_id ON deployment.configurations USING btree (id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_linux_os_application_id ON deployment.linux_os_application USING btree (id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_linux_os_application_name_with_distro ON deployment.linux_os_application USING btree (name_with_distro ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_linux_os_application_name_with_version ON deployment.linux_os_application USING btree (name_with_version ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_system_actions_id ON deployment.system_actions USING btree (id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_compliance_task_results_agent_id ON deployment.compliance_task_results USING btree (agent_id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_compliance_task_results_compliance_id ON deployment.compliance_task_results USING btree (compliance_id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_compliance_task_results_created_time ON deployment.compliance_task_results USING btree (created_time DESC NULLS FIRST);
CREATE INDEX IF NOT EXISTS idx_compliance_task_results_deployment_id ON deployment.compliance_task_results USING btree (deployment_id ASC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_compliance_task_results_framework_id ON deployment.compliance_task_results USING btree (framework_id ASC NULLS LAST);

INSERT INTO deployment.compliances VALUES ('1', '59de2b01-3da6-46b0-b18b-644e1c930a52', '1', NULL, '1', NULL, 'false', 'false', '1.1.1 - (L1) Ensure ''Enforce password history'' is set to ''24 or more password(s)''', 'W2016MS', '1', 'This policy setting determines the number of renewed, unique passwords that have to be associated with a user account before you can reuse an old password. The value for this policy setting must be between 0 and 24 passwords. The default value for stand alone systems is 0 passwords, but the default setting when joined to a domain is 24 passwords. To maintain the effectiveness of this policy setting, use the Minimum password age setting to prevent users from repeatedly changing their password. The recommended state for this setting is: 24 or more password(s).', 'Navigate to the UI Path articulated in the Remediation section and confirm it is set as  prescribed.', 'To establish the recommended configuration via GP, set the following UI path to 24 or more password(s): Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Enforce password history', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''PasswordHistorySize =''| ForEach-Object { ($_.Line -replace \"PasswordHistorySize = \", \"\")};", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 6, "ConditionValue": "24"}]}]', '["CIS", "W2016MS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('2', '00e50847-e04c-4ceb-823b-6cd6acb4575c', '1', NULL, '1', NULL, 'false', 'false', 'CIS Control Master Config', 'W2016MS', '1', 'This is default master configuration which is mandatory for CIS W2016MS benchmark Rules.', 'Check security Profile in windows 2016 MS ( Member Server)', 'if security profile log is not created create using command.if (!(Test-Path -Path C:\temp )) { New-Item -ItemType directory -Path C:\temp };secedit /export /cfg C:\temp\secpol.cfg;', '1', NULL, '[{"Key": "", "Command": "if (!(Test-Path -Path C:\\temp )) { New-Item -ItemType directory -Path C:\\temp };secedit /export /cfg C:\\temp\\secpol.cfg;", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": null}]', '["W2016MS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('3', '392749ab-d1cd-4b40-8db3-4e7d52601dc4', '1', NULL, '1', NULL, 'false', 'false', '1.1.2 - (L1) Ensure ''Maximum password age'' is set to ''365 or fewer  days, but not 0'' (Automated)', 'W2016MS', '1', 'This policy setting defines how long a user can use their password before it expires.Values for this policy setting range from 0 to 999 days. If you set the value to 0, the password will never expire.Because attackers can crack passwords, the more frequently you change the password the less opportunity an attacker has to use a cracked password. However, the lower this value is set, the higher the potential for an increase in calls to help desk support due to users having to change their password or forgetting which password is current.The recommended state for this setting is 365 or fewer days, but not 0.', 'Navigate to the UI Path articulated in the Remediation section and confirm it is set as prescribed.', 'To establish the recommended configuration via GP, set the following UI path to 365 or fewer days, but not 0: Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Maximum password age', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''MaximumPasswordAge =''| ForEach-Object { ($_.Line -replace \"MaximumPasswordAge = \", \"\")};", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 3, "ConditionValue": "0"}, {"Condition": 1, "RuleCondition": 6, "ConditionValue": "365"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('4', '05037c2c-3082-48d4-bd90-2075d11b979e', '1', NULL, '1', NULL, 'false', 'false', '1.1.3 (L1) Ensure ''Minimum password age'' is set to ''1 or more  day(s)''', 'W2016MS', '1', 'This policy setting determines the number of days that you must use a password before you can change it. The range of values for this policy setting is between 1 and 999 days. (You may also set the value to 0 to allow immediate password changes.) The default value for this setting is 0 days.The recommended state for this setting is: 1 or more day(s).', 'Navigate to the UI Path articulated in the Remediation section and confirm it is set as prescribed.', 'To establish the recommended configuration via GP, set the following UI path to 1 or more day(s): Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Minimum password age', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''MinimumPasswordAge =''| ForEach-Object { ($_.Line -replace \"MinimumPasswordAge = \", \"\") };", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 1, "ConditionValue": "0"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('5', 'd91d5e96-2b37-4484-9de3-0a89f370ebf6', '1', NULL, '1', NULL, 'false', 'false', '1.1.4 (L1) Ensure ''Minimum password length'' is set to ''14 or more  character(s)''', 'W2016MS', '1', 'This policy setting determines the least number of characters that make up a password for a user account. There are many different theories about how to determine the best password length for an organization, but perhaps "passphrase" is a better term than "password." In Microsoft Windows 2000 or newer, passphrases can be quite long and can include spaces. Therefore, a phrase such as "I want to drink a $5 milkshake" is a valid passphrase; it is a considerably stronger password than an 8 or 10 character string of random numbers and letters, and yet is easier to remember. Users must be educated about the proper selection and maintenance of passwords, especially around password length. In enterprise environments, the ideal value for the Minimum password length setting is 14 characters, however you should adjust this value to meet your organization''s business requirements.The recommended state for this setting is: 14 or more character(s).', 'Navigate to the UI Path articulated in the Remediation section and confirm it is set as prescribed.', 'To establish the recommended configuration via GP, set the following UI path to 14 or more character(s): Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Minimum password length', '1', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''MinimumPasswordLength =''| ForEach-Object { ($_.Line -replace \"MinimumPasswordLength = \", \"\") };", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 4, "ConditionValue": "14"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('6', 'c46ff4d5-527f-423e-9b56-228bc2a8b453', '1', NULL, '1', NULL, 'false', 'false', '1.1.5 (L1) Ensure ''Password must meet complexity requirements''  is set to ''Enabled''', 'W2016MS', '1', 'This policy setting checks all new passwords to ensure that they meet basic requirements for strong passwords. When this policy is enabled, passwords must meet the following minimum requirements: • Not contain the user''s account name or parts of the user''s full name that exceed two consecutive characters • Be at least six characters in length • Contain characters from three of the following categories: o English uppercase characters (A through Z) o English lowercase characters (a through z) o Base 10 digits (0 through 9) o Non-alphabetic characters (for example, !, $, #, %!)(MISSING) o A catch-all category of any Unicode character that does not fall under the previous four categories. This fifth category can be regionally specific.', 'Navigate to the UI Path articulated in the Remediation section and confirm it is set as prescribed.', 'To establish the recommended configuration via GP, set the following UI path to Enabled: Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Password must meet complexity requirements', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''PasswordComplexity =''| ForEach-Object { ($_.Line -replace \"PasswordComplexity = \", \"\")};", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 3, "ConditionValue": "0"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('7', '270acb89-c9f6-4496-bc37-7e533a1954c4', '1', NULL, '1', NULL, 'false', 'false', '1.1.6 (L1) Ensure ''Store passwords using reversible encryption'' is  set to ''Disabled''', 'W2016MS', '1', 'This policy setting determines whether the operating system stores passwords in a way that uses reversible encryption, which provides support for application protocols that require knowledge of the user''s password for authentication purposes. Passwords that are stored with reversible encryption are essentially the same as plaintext versions of the passwords. The recommended state for this setting is: Disabled.', 'Impact - If your organization uses either the CHAP authentication protocol through remote access or IAS services or Digest Authentication in IIS, you must configure this policy setting to Enabled. This setting is extremely dangerous to apply through Group Policy on a user-by-user basis, because it requires the appropriate user account object to be opened in Active Directory Users and Computers.', 'To establish the recommended configuration via GP, set the following UI path to Disabled: Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Password Policy\Store passwords using reversible encryption', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''ClearTextPassword =''| ForEach-Object { ($_.Line -replace \"ClearTextPassword = \", \"\")};", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 3, "ConditionValue": "0"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('8', 'b8680026-786e-436d-91e9-aafeb17f0a03', '1', NULL, '1', NULL, 'false', 'false', '1.2.1 (L1) Ensure ''Account lockout duration'' is set to ''15 or more  minute(s)''', 'W2016MS', '1', 'This policy setting determines the length of time that must pass before a locked account is unlocked and a user can try to log on again. The setting does this by specifying the number of minutes a locked out account will remain unavailable. If the value for this policy setting is configured to 0, locked out accounts will remain locked out until an administrator manually unlocks them.Although it might seem like a good idea to configure the value for this policy setting to a high value, such a configuration will likely increase the number of calls that the help desk receives to unlock accounts locked by mistake. Users should be aware of the length of time a lock remains in place, so that they realize they only need to call the help desk if they have an extremely urgent need to regain access to their computer. The recommended state for this setting is: 15 or more minute(s).', 'Although it may seem like a good idea to configure this policy setting to never automatically unlock an account, such a configuration can increase the number of requests that your organization''s help desk receives to unlock accounts that were locked by mistake.', 'To establish the recommended configuration via GP, set the following UI path to 15 or more minute(s):Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Account Lockout Policy\Account lockout duration', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''LockoutDuration =''| ForEach-Object { ($_.Line -replace \"LockoutDuration = \", \"\")} ;", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 2, "RuleCondition": 4, "ConditionValue": "15"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('9', '330e55d0-3904-4641-9668-ba4aeeede867', '1', NULL, '1', NULL, 'false', 'false', '1.2.2 (L1) Ensure Account lockout threshold'' is set to greater than 0''', 'W2016MS', '1', 'This policy setting determines the number of failed logon attempts before the account is locked. Setting this policy to 0 does not conform to the benchmark as doing so disables the account lockout threshold.The recommended state for this setting is: 5 or fewer invalid logon attempt(s), but not 0.', 'If this policy setting is enabled, a locked-out account will not be usable until it is reset by an administrator or until the account lockout duration expires. This setting may generate additional help desk calls. If you enforce this setting an attacker could cause a denial of service condition by deliberately generating failed logons for multiple user, therefore you should also configure the Account Lockout Duration to a relatively low value.If you configure the Account Lockout Threshold to 0, there is a possibility that an attacker''s attempt to discover passwords with a brute force password attack might go undetected if a robust audit mechanism is not in place.', 'To establish the recommended configuration via GP, set the following UI path to 5 or fewer invalid login attempt(s), but not 0:Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Account Lockout Policy\Account lockout threshold', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''LockoutBadCount =''| ForEach-Object { $_.Line -replace \"LockoutBadCount = \", \"\" }", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 4, "ConditionValue": "5"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('10', 'ed0ebf8b-8b31-43ed-be77-2506039b806c', '1', NULL, NULL, NULL, 'false', 'false', '1.2.3 (L1) Ensure ''Allow Administrator account lockout'' is set to  ''Enabled''', 'W2016MS', '1', 'This policy setting determines whether the built-in Administrator account is subject to the following Account Lockout Policy settings: Account lockout duration, Account lockout threshold, and Reset account lockout counter. By default, this account is excluded from the account lockout controls and will never be locked out with repeated bad password attempts.The recommended state for this setting is: Enabled.', 'The built-in Administrator account will be subject to the policies in Section 1.2 Account Lockout Policy of this benchmark.', 'To establish the recommended configuration via GP, set the following UI path to Enabled: Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Account Lockout Policies\Allow Administrator account lockout', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''AllowAdministratorLockout =''| ForEach-Object { $_.Line -replace \"AllowAdministratorLockout = \", \"\" }", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 3, "ConditionValue": "0"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('11', '2898703b-df72-4832-876e-6731ff2ed589', '1', NULL, NULL, NULL, 'false', 'false', '1.2.4 (L1) Ensure ''Reset account lockout counter after'' is set to  ''15 or more minute(s)''', 'W2016MS', '1', 'This policy setting determines the length of time before the Account lockout threshold resets to zero. The default value for this policy setting is Not Defined. If the Account lockout threshold is defined, this reset time must be less than or equal to the value for the Account lockout duration setting.', 'If you do not configure this policy setting or if the value is configured to an interval that is too long, a DoS attack could occur. An attacker could maliciously attempt to log on to each user''s account numerous times and lock out their accounts as described in the preceding paragraphs. If you do not configure the Reset account lockout counter after setting, administrators would have to manually unlock all accounts. If you configure this policy setting to a reasonable value the users would be locked out for some period, after which their accounts would unlock automatically. Be sure that you notify users of the values used for this policy setting so that they will wait for the lockout timer to expire before they call the help desk about their inability to log on.', 'To establish the recommended configuration via GP, set the following UI path to 15 or more minute(s):Computer Configuration\Policies\Windows Settings\Security Settings\Account Policies\Account Lockout Policy\Reset account lockout counter after', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''ResetLockoutCount =''| ForEach-Object { $_.Line -replace \"ResetLockoutCount = \", \"\" }", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 4, "ConditionValue": "15"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('12', 'c036495c-b428-4955-8d72-b5bb56676746', '1', NULL, NULL, NULL, 'false', 'false', '2.2.1 (L1) Ensure ''Access Credential Manager as a trusted caller''  is set to ''No One''', 'W2016MS', '2', 'This security setting is used by Credential Manager during Backup and Restore. No accounts should have this user right, as it is only assigned to Winlogon. Users'' saved credentials might be compromised if this user right is assigned to other entities. The recommended state for this setting is: No One.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to No One: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Access Credential Manager as a trusted caller', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeTrustedCredManAccessPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "NULL"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('13', 'deab673c-72ed-4edc-9985-d14f2a594433', '1', NULL, '1', NULL, 'false', 'false', '2.2.3 (L1) Ensure ''Access this computer from the network''', 'W2016MS', '2', 'This policy setting allows other users on the network to connect to the computer and is required by various network protocols that include Server Message Block (SMB)-based protocols, NetBIOS, Common Internet File System (CIFS), and Component Object Model Plus (COM+).The recommended state for this setting is: Administrators, Authenticated Users.', 'Servers, users will not be able to connect to those servers through the network. Successful negotiation of IPsec  connections requires that the initiating machine has this right, therefore if using IPsec, it is recommended that it is assigned to the Authenticated Users group. If you have installed optional components such as ASP.NET or Internet Information Services (IIS), you may need to assign this user right to additional accounts that are required by those components. It is important to verify that authorized users are assigned this user right for the computers they need to access the network.', 'To establish the recommended configuration via GP, configure the following UI path: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Access this computer from the network', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeNetworkLogonRight'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1"}, {"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5"}]}]', '["W2016MS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('14', 'e5d37ea0-5eed-4c5b-96bb-aa0437ca2643', '1', NULL, NULL, NULL, 'false', 'false', '2.2.4 (L1) Ensure ''Act as part of the operating system'' is set to  ''No One''', 'W2016MS', '2', 'This policy setting allows a process to assume the identity of any user and thus gain access to the resources that the user is authorized to access.  The recommended state for this setting is: No One.  Note: This user right is considered a "sensitive privilege" for the purposes of auditing.', 'There should be little or no impact because the Act as part of the operating system  user right is rarely needed by any accounts other than the Local System account, which implicitly has this right.', 'To establish the recommended configuration via GP, set the following UI path to No One: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Act as part of the operating system', '2', NULL, '[{"Key": "", "Command": "22", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 3, "ComplianceRuleConditions": null}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('15', '268eff63-93dc-4000-a111-2d29d3e70a9f', '1', NULL, NULL, NULL, 'false', 'false', '2.2.6 (L1) Ensure ''Adjust memory quotas for a process'' is set to  ''Administrators, LOCAL SERVICE, NETWORK SERVICE''', 'W2016MS', '1', 'This policy setting allows a user to adjust the maximum amount of memory that is available to a process. The ability to adjust memory quotas is useful for system tuning, but it can be abused. In the wrong hands, it could be used to launch a denial of service (DoS) attack.  The recommended state for this setting is: Administrators, LOCAL SERVICE, NETWORK SERVICE.', 'Organizations that have not restricted users to roles with limited privileges will find it difficult to impose this countermeasure. Also, if you have installed optional components such as ASP.NET or IIS, you may need to assign the Adjust memory quotas for a process user right to additional accounts that are required by those components. Otherwise, this countermeasure should have no impact on most computers. If this user right is necessary for a user account, it can be assigned to a local computer account instead of a domain account.', 'To establish the recommended configuration via GP, set the following UI path to Administrators, LOCAL SERVICE, NETWORK SERVICE: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Adjust memory quotas for a process', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeIncreaseQuotaPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('16', 'dac640f8-5c54-43c3-923f-b716d8b1c5e0', '1', NULL, '1', NULL, 'false', 'false', '2.2.8 (L1) Ensure ''Allow log on locally'' is set to ''Administrators''  (MS only) (Automated)', 'W2016MS', '2', 'This policy setting determines which users can interactively log on to computers in your environment. Logons that are initiated by pressing the CTRL+ALT+DEL key sequence on the client computer keyboard require this user right. Users who attempt to log on through Terminal Services / Remote Desktop Services or IIS also require this user right. The recommended state for this setting is: Administrators.', 'If you remove these default groups, you could limit the abilities of users who are assigned to specific administrative roles in your environment. You should confirm that  delegated activities will not be adversely affected by any changes that you make to the Allow log on locally user right.', 'To establish the recommended configuration via GP, configure the following UI path: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Allow log on locally', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeInteractiveLogonRight'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5"}, {"Condition": 1, "RuleCondition": 8, "ConditionValue": "Guest"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('17', '62c7de44-5a9f-4c68-93eb-4f960830a964', '1', NULL, NULL, NULL, 'false', 'false', '2.2.10 (L1) Ensure ''Allow log on through Remote Desktop  Services'' is set to ''Administrators, Remote Desktop Users'' (MS  only)', 'W2016MS', '2', 'This policy setting determines which users or groups have the right to log on as a Remote Desktop Services client. If your organization uses Remote Assistance as part of its help desk strategy, create a group and assign it this user right through Group Policy.If the help desk in your organization does not use Remote Assistance, assign this user right only to the Administrators group or use the Restricted Groups feature to ensure that no user accounts are part of the Remote Desktop Users group. Restrict this user right to the Administrators group, and possibly the Remote Desktop Users group, to prevent unwanted users from gaining access to computers on your network by means of the Remote Assistance feature. The recommended state for this setting is: Administrators, Remote Desktop Users.', 'Removal of the Allow log on through Remote Desktop Services user right from other groups or membership changes in these default groups could limit the abilities of users who perform specific administrative roles in your environment. You should confirm that delegated activities will not be adversely affected.', 'To establish the recommended configuration via GP, configure the following UI path: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Allow log on through Remote Desktop Services', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeRemoteInteractiveLogonRight'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5-32"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('18', '0e9b6994-61af-4aa2-99d2-50eeb2c6b6d5', '1', NULL, '1', NULL, 'false', 'false', '2.2.11 (L1) Ensure ''Back up files and directories'' is set to  ''Administrators''', 'W2016MS', '2', 'This policy setting allows users to circumvent file and directory permissions to back up the system. This user right is enabled only when an application (such as NTBACKUP) attempts to access a file or directory through the NTFS file system backup application programming interface (API). Otherwise, the assigned file and directory permissions apply. The recommended state for this setting is: Administrators.', 'Changes in the membership of the groups that have the Back up files and directories user right could limit the abilities of users who are assigned to specific administrative roles in your environment. You should confirm that authorized backup administrators are still able to perform backup operations.', 'To establish the recommended configuration via GP, set the following UI path to Administrators. Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Back up files and directories', '2', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeBackupPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5"}]}]', '["W2016MS", "CIS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('19', '55c40ab2-a7ad-4b47-ab3d-7f4167952a41', '1', NULL, NULL, NULL, 'false', 'false', '2.2.12 (L1) Ensure ''Change the system time'' is set to  ''Administrators, LOCAL SERVICE''', 'W2016MS', '2', 'This policy setting determines which users and groups can change the time and date on the internal clock of the computers in your environment. Users who are assigned this user right can affect the appearance of event logs. When a computer''s time setting is changed, logged events reflect the new time, not the actual time that the events occurred.The recommended state for this setting is: Administrators, LOCAL SERVICE.', 'There should be no impact, because time synchronization for most organizations should be fully automated for all computers that belong to the domain. Computers that do not belong to the domain should be configured to synchronize with an external source.', 'To establish the recommended configuration via GP, set the following UI path to Administrators, LOCAL SERVICE: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Change the system time', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeSystemTimePrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "S-1-5"}]}]', NULL, 'false', '1');
INSERT INTO deployment.compliances VALUES ('20', 'd4733407-4c84-4274-8e99-0260b267da41', '1', NULL, NULL, NULL, 'false', 'false', '2.2.13 (L1) Ensure ''Change the time zone'' is set to  ''Administrators, LOCAL SERVICE''', 'W2016MS', '2', 'This setting determines which users can change the time zone of the computer. This ability holds no great danger for the computer and may be useful for mobile workers.The recommended state for this setting is: Administrators, LOCAL SERVICE.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to Administrators, LOCAL SERVICE: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Change the time zone', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeTimeZonePrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-19,*S-1-5-32-544"}]}]', '["W2016MS"]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('21', 'e06592d1-1745-4f1e-8b08-7aefc5c177ce', '1', NULL, NULL, NULL, 'false', 'false', '2.2.14 (L1) Ensure ''Create a pagefile'' is set to ''Administrators''', 'W2016MS', '2', 'This policy setting allows users to change the size of the pagefile. By making the pagefile extremely large or extremely small, an attacker could easily affect the performance of a compromised computer. The recommended state for this setting is: Administrators.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to Administrators: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Create a pagefile', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeCreatePagefilePrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-32-544"}]}]', NULL, 'false', '1');
INSERT INTO deployment.compliances VALUES ('22', '933b0b67-ad34-4a6c-8cc9-79e4ac2f38a1', '1', NULL, NULL, NULL, 'false', 'false', '2.2.15 (L1) Ensure ''Create a token object'' is set to ''No One''', 'W2016MS', '2', 'This policy setting allows a process to create an access token, which may provide elevated rights to access sensitive data.The recommended state for this setting is: No One. Note: This user right is considered a "sensitive privilege" for the purposes of auditing.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to No One: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Create a token object', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeCreateTokenPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-32-544 *S-1-5-19"}]}]', NULL, 'false', '1');
INSERT INTO deployment.compliances VALUES ('23', '9eb8fded-057c-44ba-9d77-3830a14dab1a', '1', NULL, NULL, NULL, 'false', 'false', '2.2.16 (L1) Ensure ''Create global objects'' is set to ''Administrators,  LOCAL SERVICE, NETWORK SERVICE, SERVICE''', 'W2016MS', '2', 'This policy setting determines whether users can create global objects that are available to all sessions. Users can still create objects that are specific to their own session if they do not have this user right. Users who can create global objects could affect processes that run under other users''  sessions. This capability could lead to a variety of problems, such as application failure or data corruption. The recommended state for this setting is: Administrators, LOCAL SERVICE, NETWORK SERVICE, SERVICE.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to Administrators, LOCAL SERVICE, NETWORK SERVICE, SERVICE: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Create global objects', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeCreateGlobalPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-19"}, {"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-20"}, {"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-32-544"}, {"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-6"}]}]', NULL, 'false', '1');
INSERT INTO deployment.compliances VALUES ('24', '3c6c76cd-1baf-44a2-8eb2-96f250f04742', '1', NULL, '1', NULL, 'false', 'false', '2.2.17 (L1) Ensure ''Create permanent shared objects'' is set to  ''No One''', 'W2016MS', '2', 'This user right is useful to kernel-mode components that extend the object namespace. However, components that run in kernel mode have this user right inherently. Therefore, it is typically not necessary to specifically assign this user right. The recommended state for this setting is: No One.', 'None - this is the default behavior.', 'To establish the recommended configuration via GP, set the following UI path to No One: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Create permanent shared objects', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeCreatePermanentPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 8, "ConditionValue": "0"}]}]', '[]', 'false', '1');
INSERT INTO deployment.compliances VALUES ('25', '52e1e4fb-3f94-422f-b4f5-48e09ec53ac1', '1', NULL, NULL, NULL, 'false', 'false', '2.2.19 (L1) Ensure ''Create symbolic links'' is set to  ''Administrators, NT VIRTUAL MACHINE\Virtual Machines'' (', 'W2016MS', '2', 'This policy setting determines which users can create symbolic links. In Windows Vista, existing NTFS file system objects, such as files and folders, can be accessed by referring to a new kind of file system object called a symbolic link. A symbolic link is a pointer (much like a shortcut or .lnk file) to another file system object, which can be a file, folder, shortcut or another symbolic link. The difference between a shortcut and a symbolic link is that a shortcut only works from within the Windows shell. To other programs and applications, shortcuts are just another file, whereas with symbolic links,the concept of a shortcut is implemented as a feature of the NTFS file system. Symbolic links can potentially expose security vulnerabilities in applications that are not designed to use them. For this reason, the privilege for creating symbolic links should only be assigned to trusted users. By default, only Administrators can create symbolic links.', 'In most cases there will be no impact because this is the default configuration.However, on Windows Servers with the Hyper-V server role installed, this user right should also be granted to the special group Virtual Machines - otherwise you will not be able to create new virtual machines.', 'To implement the recommended configuration state, configure the following UI path: Computer Configuration\Policies\Windows Settings\Security Settings\Local Policies\User Rights Assignment\Create symbolic links', '3', NULL, '[{"Key": "", "Command": "Get-Content -Path C:\\temp\\secpol.cfg | Select-String -Pattern ''SeCreateSymbolicLinkPrivilege'';", "OrderId": 0, "Condition": 1, "ScriptFile": {"url": "", "refName": "", "realName": "", "locationId": 0}, "CommandType": 1, "ComplianceRuleConditions": [{"Condition": 1, "RuleCondition": 7, "ConditionValue": "*S-1-5-32-544"}, {"Condition": 1, "RuleCondition": 7, "ConditionValue": ",*S-1-5-83-0"}]}]', '["W2016MS"]', 'false', '1');
SELECT setval('deployment.compliances_id_seq', (SELECT MAX(id) FROM deployment.compliances));
