package logger

import (
	"os"
	"os/signal"
	"path/filepath"
	"syscall"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

var ServiceLogger *logrus.Logger
var PatchPoolingLogger *logrus.Logger
var DiscoveryLogger *logrus.Logger
var APILogger *logrus.Logger
var DBLogger *logrus.Logger

func ConfigLogger(logDir, logLevel string, maxSize int, maxAge int) {
	configureServiceLogger(logDir, logLevel, maxSize, maxAge)
}

func configureServiceLogger(logDir, logLevel string, maxSize int, maxAge int) {
	ServiceLogger = logrus.New()
	PatchPoolingLogger = logrus.New()
	DiscoveryLogger = logrus.New()
	APILogger = logrus.New()
	DBLogger = logrus.New()
	level, _ := logrus.ParseLevel(logLevel)
	ServiceLogger.SetLevel(level)
	PatchPoolingLogger.SetLevel(level)
	DiscoveryLogger.SetLevel(level)
	APILogger.SetLevel(level)
	DBLogger.SetLevel(level)
	timeFormatter := new(logrus.TextFormatter)
	timeFormatter.TimestampFormat = "dd-MMMM-yyyy hh:mm:ss a"
	logrus.SetFormatter(timeFormatter)
	fsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "service.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	ServiceLogger.SetOutput(fsLogger)
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP)
	go func() {
		for {
			<-c
			err := fsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()

	patchFsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "patch.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	PatchPoolingLogger.SetOutput(patchFsLogger)
	c1 := make(chan os.Signal, 1)
	signal.Notify(c1, syscall.SIGHUP)
	go func() {
		for {
			<-c1
			err := patchFsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()

	discoveryFsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "discovery.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	DiscoveryLogger.SetOutput(discoveryFsLogger)
	c3 := make(chan os.Signal, 1)
	signal.Notify(c3, syscall.SIGHUP)
	go func() {
		for {
			<-c3
			err := discoveryFsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()

	apiFsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "api.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	APILogger.SetOutput(apiFsLogger)
	c4 := make(chan os.Signal, 1)
	signal.Notify(c4, syscall.SIGHUP)
	go func() {
		for {
			<-c4
			err := apiFsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()
	dbFsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "db.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	DBLogger.SetOutput(dbFsLogger)
	c5 := make(chan os.Signal, 1)
	signal.Notify(c5, syscall.SIGHUP)
	go func() {
		for {
			<-c5
			err := dbFsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()
}

func ChangeLogLeve(logLevel string) {
	level, _ := logrus.ParseLevel(logLevel)
	ServiceLogger.SetLevel(level)
	PatchPoolingLogger.SetLevel(level)
	DiscoveryLogger.SetLevel(level)
	APILogger.SetLevel(level)
	DBLogger.SetLevel(level)
}
