package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type MsrcVulnerabilityRepository struct {
	dbConnection *bun.DB
}

func NewMsrcVulnerabilityRepository() *MsrcVulnerabilityRepository {
	return &MsrcVulnerabilityRepository{dbConnection: db.Connection}
}

func (repo MsrcVulnerabilityRepository) Create(msrcVulnerability *model.MsrcVulnerability) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(msrcVulnerability).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return msrcVulnerability.Id, nil
}

func (repo MsrcVulnerabilityRepository) Update(msrcVulnerabilityId *model.MsrcVulnerability) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(msrcVulnerabilityId).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return msrcVulnerabilityId.Id, nil
}

func (repo MsrcVulnerabilityRepository) GetById(msrcVulnerabilityId int64, includeArchive bool) (model.MsrcVulnerability, error) {
	var msrcVulnerability model.MsrcVulnerability
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&msrcVulnerability).Where("id = ?", msrcVulnerabilityId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&msrcVulnerability).Where("id = ?", msrcVulnerabilityId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return msrcVulnerability, err
	}
	return msrcVulnerability, nil
}

// GetAllMsrcVulnerabilities executes a parameterized query to prevent SQL injection
func (repo MsrcVulnerabilityRepository) GetAllMsrcVulnerabilities(query string, params []interface{}) ([]model.MsrcVulnerability, error) {
	var msrcVulnerability []model.MsrcVulnerability
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &msrcVulnerability)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &msrcVulnerability)
	}
	if err != nil {
		return msrcVulnerability, err
	}
	return msrcVulnerability, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo MsrcVulnerabilityRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo MsrcVulnerabilityRepository) GetByCve(cve string) (model.MsrcVulnerability, error) {
	var msrcVulnerability model.MsrcVulnerability
	err := repo.dbConnection.NewSelect().Model(&msrcVulnerability).Where("cve = ?", cve).Scan(context.Background())
	if err != nil {
		return msrcVulnerability, err
	}
	return msrcVulnerability, nil
}

func (repo MsrcVulnerabilityRepository) GetLatestMsrcVulnerability() (model.MsrcVulnerability, error) {
	var msrcVulnerability model.MsrcVulnerability
	err := repo.dbConnection.NewSelect().Model(&msrcVulnerability).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.MsrcVulnerability{}, err
	}
	return msrcVulnerability, nil
}
