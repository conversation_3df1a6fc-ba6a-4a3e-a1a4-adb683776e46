package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type ComputerGroupRepository struct {
	dbConnection *bun.DB
}

func NewComputerGroupRepository() *ComputerGroupRepository {
	return &ComputerGroupRepository{dbConnection: db.Connection}
}

func (repo ComputerGroupRepository) Create(computerGroup *model.ComputerGroup) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(computerGroup).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return computerGroup.Id, nil
}

func (repo ComputerGroupRepository) Update(computerGroup *model.ComputerGroup) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(computerGroup).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return computerGroup.Id, nil
}

func (repo ComputerGroupRepository) GetById(computerGroupId int64) (model.ComputerGroup, error) {
	var computerGroup model.ComputerGroup
	err := repo.dbConnection.NewSelect().Model(&computerGroup).Where("id = ?", computerGroupId).Scan(context.Background())
	if err != nil {
		return computerGroup, err
	}
	return computerGroup, nil
}

func (repo ComputerGroupRepository) DeleteById(computerGroupId int64) (bool, error) {
	computerGroup, err := repo.GetById(computerGroupId)
	if err != nil {
		return false, err
	}
	computerGroup.Removed = true
	_, err = repo.dbConnection.NewDelete().Model(&computerGroup).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo ComputerGroupRepository) GetAll(query string, params []interface{}) ([]model.ComputerGroup, error) {
	var computerGroups []model.ComputerGroup
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &computerGroups)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &computerGroups)
	}
	if err != nil {
		return computerGroups, err
	}
	return computerGroups, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo ComputerGroupRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
