package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type UbuntuPatchRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuPatchRepository() *UbuntuPatchRepository {
	return &UbuntuPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuPatchRepository) Create(patch *model.UbuntuPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patch).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo UbuntuPatchRepository) Update(patch *model.UbuntuPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo UbuntuPatchRepository) GetById(patchID int64) (model.UbuntuPatch, error) {
	var patch model.UbuntuPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", patchID).Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo UbuntuPatchRepository) GetByUUID(uuid string) (*model.UbuntuPatch, error) {
	patch := new(model.UbuntuPatch)
	err := repo.dbConnection.NewSelect().Model(patch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return patch, nil
}

func (repo UbuntuPatchRepository) DeleteById(patchID int64) (bool, error) {
	patch, err := repo.GetById(patchID)
	if err != nil {
		return false, err
	}
	_, err = repo.dbConnection.NewDelete().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllUbuntuPatchesByQuery executes a parameterized query to prevent SQL injection
func (repo UbuntuPatchRepository) GetAllUbuntuPatchesByQuery(query string, params []interface{}) ([]model.UbuntuPatch, error) {
	var patches []model.UbuntuPatch
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patches)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patches)
	}
	if err != nil {
		return patches, err
	}
	return patches, nil
}

// CountByQuery executes a parameterized count query to prevent SQL injection
func (repo UbuntuPatchRepository) CountByQuery(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo UbuntuPatchRepository) GetLatestUbuntuPatch() (model.UbuntuPatch, error) {
	var product model.UbuntuPatch
	err := repo.dbConnection.NewSelect().Model(&product).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.UbuntuPatch{}, err
	}
	return product, nil
}
