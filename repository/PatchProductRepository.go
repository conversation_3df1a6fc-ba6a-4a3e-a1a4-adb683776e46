package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type PatchProductRepository struct {
	dbConnection *bun.DB
}

func NewPatchProductRepository() *PatchProductRepository {
	return &PatchProductRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchProductRepository) Create(product *model.PatchProduct) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(product).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return product.Id, nil
}

func (repo PatchProductRepository) Update(product *model.PatchProduct) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(product).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return product.Id, nil
}

func (repo PatchProductRepository) GetById(productId int64, includeArchive bool) (model.PatchProduct, error) {
	var product model.PatchProduct
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&product).Where("id = ?", productId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&product).Where("id = ?", productId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return product, err
	}
	return product, nil
}

func (repo PatchProductRepository) PermanentDeleteById(productId int64) (bool, error) {
	product := new(model.PatchProduct)
	product.Id = productId
	_, err := repo.dbConnection.NewDelete().Model(product).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllPatchProducts executes a parameterized query to prevent SQL injection
func (repo PatchProductRepository) GetAllPatchProducts(query string, params []interface{}) ([]model.PatchProduct, error) {
	var product []model.PatchProduct
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &product)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &product)
	}
	if err != nil {
		return product, err
	}
	return product, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo PatchProductRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo PatchProductRepository) GetByUuid(uuid string) (model.PatchProduct, error) {
	var product model.PatchProduct
	err := repo.dbConnection.NewSelect().Model(&product).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		return product, err
	}
	return product, nil
}

func (repo PatchProductRepository) GetLatestPatchProduct() (model.PatchProduct, error) {
	var product model.PatchProduct
	err := repo.dbConnection.NewSelect().Model(&product).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.PatchProduct{}, err
	}
	return product, nil
}
