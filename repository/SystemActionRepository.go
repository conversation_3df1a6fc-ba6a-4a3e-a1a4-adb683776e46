package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type SystemActionRepository struct {
	dbConnection *bun.DB
}

func NewSystemActionRepository() *SystemActionRepository {
	return &SystemActionRepository{dbConnection: db.Connection}
}

func (repo SystemActionRepository) Create(systemAction *model.SystemAction) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(systemAction).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return systemAction.Id, nil
}

func (repo SystemActionRepository) Update(systemAction *model.SystemAction) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(systemAction).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return systemAction.Id, nil
}

func (repo SystemActionRepository) GetById(systemActionId int64) (model.SystemAction, error) {
	var systemAction model.SystemAction
	var err error
	err = repo.dbConnection.NewSelect().Model(&systemAction).Where("id = ?", systemActionId).Where("removed = ?", false).Scan(context.Background())
	if err != nil {
		return systemAction, err
	}
	return systemAction, nil
}

func (repo SystemActionRepository) DeleteById(systemActionId int64) (bool, error) {
	systemAction, err := repo.GetById(systemActionId)
	if err != nil {
		return false, err
	}
	systemAction.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&systemAction).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllSystemAction executes a parameterized query to prevent SQL injection
func (repo SystemActionRepository) GetAllSystemAction(query string, params []interface{}) ([]model.SystemAction, error) {
	var systemActions []model.SystemAction
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &systemActions)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &systemActions)
	}
	if err != nil {
		return systemActions, err
	}
	return systemActions, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo SystemActionRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
