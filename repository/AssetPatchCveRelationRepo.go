package repository

import (
	"context"
	"deployment/db"
	"github.com/uptrace/bun"
)

type AssetPatchCveRelationRepository struct {
	dbConnection *bun.DB
}

func NewAssetPatchCveRelationRepository() *AssetPatchCveRelationRepository {
	return &AssetPatchCveRelationRepository{
		dbConnection: db.Connection,
	}
}

// Count executes a parameterized count query to prevent SQL injection
func (repo AssetPatchCveRelationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

// GetDetails executes a parameterized query to prevent SQL injection
func (repo AssetPatchCveRelationRepository) GetDetails(query string, params []interface{}) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &result)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &result)
	}
	if err != nil {
		return result, err
	}

	return result, nil
}

// GetCveDetail executes a parameterized query to prevent SQL injection
func (repo AssetPatchCveRelationRepository) GetCveDetail(query string, params []interface{}) (map[string]interface{}, error) {
	var result map[string]interface{}
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &result)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &result)
	}
	if err != nil {
		return result, err
	}

	return result, nil
}
