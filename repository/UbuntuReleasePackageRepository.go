package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type UbuntuReleasePackageRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuReleasePackageRepository() *UbuntuReleasePackageRepository {
	return &UbuntuReleasePackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuReleasePackageRepository) Create(pkg *model.UbuntuReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(pkg).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo UbuntuReleasePackageRepository) Update(pkg *model.UbuntuReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo UbuntuReleasePackageRepository) GetById(id int64) (*model.UbuntuReleasePackage, error) {
	pkg := new(model.UbuntuReleasePackage)
	err := repo.dbConnection.NewSelect().Model(pkg).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return pkg, nil
}

func (repo UbuntuReleasePackageRepository) GetByNameAndVersionUniqueKey(nameAndVersionUniqueKey string) (*model.UbuntuReleasePackage, error) {
	pkg := new(model.UbuntuReleasePackage)
	err := repo.dbConnection.NewSelect().Model(pkg).Where("name_and_version_unique_key = ?", nameAndVersionUniqueKey).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return pkg, nil
}

func (repo UbuntuReleasePackageRepository) DeleteById(id int64) (bool, error) {
	pkg, err := repo.GetById(id)
	if err != nil {
		return false, err
	}
	_, err = repo.dbConnection.NewDelete().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllUbuntuReleasePackagesByQuery executes a parameterized query to prevent SQL injection
func (repo UbuntuReleasePackageRepository) GetAllUbuntuReleasePackagesByQuery(query string, params []interface{}) ([]model.UbuntuReleasePackage, error) {
	var ubuntuReleasePackages []model.UbuntuReleasePackage
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &ubuntuReleasePackages)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &ubuntuReleasePackages)
	}
	if err != nil {
		return ubuntuReleasePackages, err
	}
	return ubuntuReleasePackages, nil
}

// CountByQuery executes a parameterized count query to prevent SQL injection
func (repo UbuntuReleasePackageRepository) CountByQuery(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo UbuntuReleasePackageRepository) GetLatestUbuntuReleasePackage() (model.UbuntuReleasePackage, error) {
	var product model.UbuntuReleasePackage
	err := repo.dbConnection.NewSelect().Model(&product).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.UbuntuReleasePackage{}, err
	}
	return product, nil
}
