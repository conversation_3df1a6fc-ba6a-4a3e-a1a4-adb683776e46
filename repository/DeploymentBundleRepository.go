package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type DeploymentBundleRepository struct {
	dbConnection *bun.DB
}

func NewDeploymentBundleRepository() *DeploymentBundleRepository {
	return &DeploymentBundleRepository{
		dbConnection: db.Connection,
	}
}

func (repo DeploymentBundleRepository) Create(bundle *model.DeploymentBundle) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(bundle).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return bundle.Id, nil
}

func (repo DeploymentBundleRepository) Update(bundle *model.DeploymentBundle) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(bundle).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return bundle.Id, nil
}

func (repo DeploymentBundleRepository) GetById(bundleId int64, includeArchive bool) (model.DeploymentBundle, error) {
	var bundle model.DeploymentBundle
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&bundle).Where("id = ?", bundleId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&bundle).Where("id = ?", bundleId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return bundle, err
	}
	return bundle, nil
}

func (repo DeploymentBundleRepository) DeleteById(bundleId int64) (bool, error) {
	bundle, err := repo.GetById(bundleId, false)
	if err != nil {
		return false, err
	}
	bundle.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&bundle).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo DeploymentBundleRepository) PermanentDeleteById(bundleId int64) (bool, error) {
	bundle := new(model.DeploymentBundle)
	bundle.Id = bundleId
	_, err := repo.dbConnection.NewDelete().Model(bundle).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllBundles executes a parameterized query to prevent SQL injection
func (repo DeploymentBundleRepository) GetAllBundles(query string, params []interface{}) ([]model.DeploymentBundle, error) {
	var bundle []model.DeploymentBundle
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &bundle)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &bundle)
	}
	if err != nil {
		return bundle, err
	}
	return bundle, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo DeploymentBundleRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
