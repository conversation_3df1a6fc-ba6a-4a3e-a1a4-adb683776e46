package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type ThirdPartyPackageRepository struct {
	dbConnection *bun.DB
}

func NewThirdPartyPackageRepository() *ThirdPartyPackageRepository {
	return &ThirdPartyPackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo ThirdPartyPackageRepository) Create(pkg *model.ThirdPartyPackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(pkg).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo ThirdPartyPackageRepository) Update(pkg *model.ThirdPartyPackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo ThirdPartyPackageRepository) GetById(pkgId int64) (model.ThirdPartyPackage, error) {
	var pkg model.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", pkgId).Scan(context.Background())
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

func (repo ThirdPartyPackageRepository) PermanentDeleteById(pkgId int64) (bool, error) {
	pkg := new(model.ThirdPartyPackage)
	pkg.Id = pkgId
	_, err := repo.dbConnection.NewDelete().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllPackage executes a parameterized query to prevent SQL injection
func (repo ThirdPartyPackageRepository) GetAllPackage(query string, params []interface{}) ([]model.ThirdPartyPackage, error) {
	var pkg []model.ThirdPartyPackage
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &pkg)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &pkg)
	}
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo ThirdPartyPackageRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo ThirdPartyPackageRepository) GetByUuid(uuid string) (model.ThirdPartyPackage, error) {
	var pkg model.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("uuid = ?", uuid).Scan(context.Background())
	return pkg, err
}
