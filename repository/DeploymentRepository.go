package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"fmt"
	"github.com/uptrace/bun"
)

type DeploymentRepository struct {
	dbConnection *bun.DB
}

func NewDeploymentRepository() *DeploymentRepository {
	return &DeploymentRepository{
		dbConnection: db.Connection,
	}
}

func (repo DeploymentRepository) Create(deployment *model.Deployment) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(deployment).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return deployment.Id, nil
}

func (repo DeploymentRepository) Update(deployment *model.Deployment) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(deployment).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return deployment.Id, nil
}

func (repo DeploymentRepository) GetById(deploymentId int64, includeArchived bool) (model.Deployment, error) {
	var deployment model.Deployment
	var err error
	if includeArchived {
		err = repo.dbConnection.NewSelect().Model(&deployment).Where("id = ?", deploymentId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&deployment).Where("id = ?", deploymentId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return deployment, err
	}
	return deployment, nil
}

func (repo DeploymentRepository) DeleteById(deploymentId int64) (bool, error) {
	deployment, err := repo.GetById(deploymentId, false)
	if err != nil {
		return false, err
	}
	deployment.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&deployment).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllDeployments executes a parameterized query to prevent SQL injection
func (repo DeploymentRepository) GetAllDeployments(query string, params []interface{}) ([]model.Deployment, error) {
	var deployments []model.Deployment
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &deployments)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &deployments)
	}
	if err != nil {
		return deployments, err
	}
	return deployments, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo DeploymentRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo DeploymentRepository) GetAllSelfServiceDeploymentsByAssetId(assetId int64) ([]model.Deployment, error) {
	var deployments []model.Deployment
	query := "select * from deployment.deployments where is_self_service_deployment = true and computer_ids @> '[" + fmt.Sprint(assetId) + "]';"
	fmt.Println(query)
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &deployments)
	if err != nil {
		return deployments, err
	}
	return deployments, nil
}
