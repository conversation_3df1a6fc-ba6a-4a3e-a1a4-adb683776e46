package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type PatchAssetApplicationRepository struct {
	dbConnection *bun.DB
}

func NewPatchAssetApplicationRepository() *PatchAssetApplicationRepository {
	return &PatchAssetApplicationRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchAssetApplicationRepository) Create(patchAssetApplication *model.PatchAssetApplication) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patchAssetApplication).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchAssetApplication.Id, nil
}

func (repo PatchAssetApplicationRepository) Update(patchAssetApplication *model.PatchAssetApplication) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patchAssetApplication).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchAssetApplication.Id, nil
}

func (repo PatchAssetApplicationRepository) GetByAssetAndProductId(assetId, productId int64) (model.PatchAssetApplication, error) {
	var patchAssetApplication model.PatchAssetApplication
	var err error
	err = repo.dbConnection.NewSelect().Model(&patchAssetApplication).Where("asset_id = ? AND product_id = ?", assetId, productId).
		Order("created_time DESC").
		Offset(0).
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return patchAssetApplication, err
	}
	return patchAssetApplication, nil
}
func (repo PatchAssetApplicationRepository) GetById(id int64, includeArchive bool) (model.PatchAssetApplication, error) {

	var patchAssetApplication model.PatchAssetApplication
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patchAssetApplication).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&patchAssetApplication).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patchAssetApplication, err
	}
	return patchAssetApplication, nil
}

func (repo PatchAssetApplicationRepository) DeleteById(id int64) (bool, error) {
	patchAssetApplication, err := repo.GetById(id, false)
	if err != nil {
		return false, err
	}
	patchAssetApplication.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&patchAssetApplication).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo PatchAssetApplicationRepository) PermanentDeleteById(id int64) (bool, error) {
	patchAssetApplication := new(model.PatchAssetApplication)
	patchAssetApplication.Id = id
	_, err := repo.dbConnection.NewDelete().Model(patchAssetApplication).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllApplication executes a parameterized query to prevent SQL injection
func (repo PatchAssetApplicationRepository) GetAllApplication(query string, params []interface{}) ([]model.PatchAssetApplication, error) {
	var patchAssetApplications []model.PatchAssetApplication
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patchAssetApplications)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patchAssetApplications)
	}
	if err != nil {
		return patchAssetApplications, err
	}
	return patchAssetApplications, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo PatchAssetApplicationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo PatchAssetApplicationRepository) DeleteByAssetId(assetIds []string) {
	query := "Update deployment.patch_asset_applications set removed = true WHERE asset_id in (?)"
	_, err := repo.dbConnection.NewRaw(query, bun.In(assetIds)).Exec(context.Background())
	if err != nil {
		return
	}
}
