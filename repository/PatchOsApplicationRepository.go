package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type PatchOsApplicationRepository struct {
	dbConnection *bun.DB
}

func NewPatchOsApplicationRepository() *PatchOsApplicationRepository {
	return &PatchOsApplicationRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchOsApplicationRepository) Create(patchOsApplication *model.PatchOsApplication) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patchOsApplication).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchOsApplication.Id, nil
}

func (repo PatchOsApplicationRepository) Update(patchOsApplication *model.PatchOsApplication) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patchOsApplication).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchOsApplication.Id, nil
}

func (repo PatchOsApplicationRepository) GetById(id int64, includeArchive bool) (model.PatchOsApplication, error) {
	var patchOsApplication model.PatchOsApplication
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patchOsApplication).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&patchOsApplication).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patchOsApplication, err
	}
	return patchOsApplication, nil
}

func (repo PatchOsApplicationRepository) GetByName(appName string) (model.PatchOsApplication, error) {
	var patchOsApplication model.PatchOsApplication
	var err error
	err = repo.dbConnection.NewSelect().Model(&patchOsApplication).Where("name = ?", appName).Scan(context.Background())
	if err != nil {
		return patchOsApplication, err
	}
	return patchOsApplication, nil
}

func (repo PatchOsApplicationRepository) GetByUUID(uuid string) (model.PatchOsApplication, error) {
	var patchOsApplication model.PatchOsApplication
	var err error
	err = repo.dbConnection.NewSelect().Model(&patchOsApplication).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		return patchOsApplication, err
	}
	return patchOsApplication, nil
}

func (repo PatchOsApplicationRepository) DeleteById(id int64) (bool, error) {
	patchOsApplication, err := repo.GetById(id, false)
	if err != nil {
		return false, err
	}
	patchOsApplication.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&patchOsApplication).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllApplication executes a parameterized query to prevent SQL injection
func (repo PatchOsApplicationRepository) GetAllApplication(query string, params []interface{}) ([]model.PatchOsApplication, error) {
	var pkg []model.PatchOsApplication
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &pkg)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &pkg)
	}
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo PatchOsApplicationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
