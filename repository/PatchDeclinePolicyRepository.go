package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type PatchDeclinePolicyRepository struct {
	dbConnection *bun.DB
}

func NewPatchDeclinePolicyRepository() *PatchDeclinePolicyRepository {
	return &PatchDeclinePolicyRepository{
		dbConnection: db.Connection,
	}
}

func (repo *PatchDeclinePolicyRepository) Create(patchDeclinePolicy *model.PatchDeclinePolicy) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patchDeclinePolicy).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchDeclinePolicy.Id, nil
}

func (repo *PatchDeclinePolicyRepository) Update(patchDeclinePolicy *model.PatchDeclinePolicy) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patchDeclinePolicy).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchDeclinePolicy.Id, nil
}

func (repo *PatchDeclinePolicyRepository) GetById(id int64) (model.PatchDeclinePolicy, error) {
	var patchDeclinePolicy model.PatchDeclinePolicy
	err := repo.dbConnection.NewSelect().Model(&patchDeclinePolicy).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		return patchDeclinePolicy, err
	}
	return patchDeclinePolicy, nil
}

func (repo *PatchDeclinePolicyRepository) DeleteById(id int64) (bool, error) {
	patchDeclinePolicy, err := repo.GetById(id)
	if err != nil {
		return false, err
	}
	patchDeclinePolicy.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&patchDeclinePolicy).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo *PatchDeclinePolicyRepository) GetAll(query string, params []interface{}) ([]model.PatchDeclinePolicy, error) {
	var patchDeclinePolicies []model.PatchDeclinePolicy
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patchDeclinePolicies)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patchDeclinePolicies)
	}
	if err != nil {
		return patchDeclinePolicies, err
	}
	return patchDeclinePolicies, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo *PatchDeclinePolicyRepository) Count(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}
