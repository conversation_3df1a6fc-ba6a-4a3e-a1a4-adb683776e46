package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type UbuntuNoticeDataRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuNoticeDataRepository() *UbuntuNoticeDataRepository {
	return &UbuntuNoticeDataRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuNoticeDataRepository) Create(data *model.UbuntuNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(data).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return data.Id, nil
}

func (repo UbuntuNoticeDataRepository) Update(data *model.UbuntuNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(data).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return data.Id, nil
}

func (repo UbuntuNoticeDataRepository) GetById(id int64) (*model.UbuntuNoticeData, error) {
	data := new(model.UbuntuNoticeData)
	err := repo.dbConnection.NewSelect().Model(data).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (repo UbuntuNoticeDataRepository) GetByNoticeId(id string) (*model.UbuntuNoticeData, error) {
	data := new(model.UbuntuNoticeData)
	err := repo.dbConnection.NewSelect().Model(data).Where("notice_id = ?", id).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (repo UbuntuNoticeDataRepository) DeleteById(id int64) (bool, error) {
	data, err := repo.GetById(id)
	if err != nil {
		return false, err
	}
	_, err = repo.dbConnection.NewDelete().Model(data).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllUbuntuNoticeDataByQuery executes a parameterized query to prevent SQL injection
func (repo UbuntuNoticeDataRepository) GetAllUbuntuNoticeDataByQuery(query string, params []interface{}) ([]model.UbuntuNoticeData, error) {
	var data []model.UbuntuNoticeData
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &data)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &data)
	}
	if err != nil {
		return data, err
	}
	return data, nil
}

// CountByQuery executes a parameterized count query to prevent SQL injection
func (repo UbuntuNoticeDataRepository) CountByQuery(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo UbuntuNoticeDataRepository) GetLatestUbuntuNoticeData() (model.UbuntuNoticeData, error) {
	var product model.UbuntuNoticeData
	err := repo.dbConnection.NewSelect().Model(&product).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.UbuntuNoticeData{}, err
	}
	return product, nil
}
