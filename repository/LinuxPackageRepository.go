package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type LinuxPackageRepository struct {
	dbConnection *bun.DB
}

func NewLinuxPackageRepository() *LinuxPackageRepository {
	return &LinuxPackageRepository{dbConnection: db.Connection}
}

func (repo LinuxPackageRepository) Create(linuxPackage *model.LinuxPackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(linuxPackage).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return linuxPackage.Id, nil
}

func (repo LinuxPackageRepository) Update(linuxPackage *model.LinuxPackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(linuxPackage).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return linuxPackage.Id, nil
}

func (repo LinuxPackageRepository) GetById(linuxPackageId int64) (*model.LinuxPackage, error) {
	linuxPackage := new(model.LinuxPackage)
	err := repo.dbConnection.NewSelect().Model(linuxPackage).Where("id = ?", linuxPackageId).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return linuxPackage, nil
}

func (repo LinuxPackageRepository) GetByNameWithDistro(nameWithDistro string) (*model.LinuxPackage, error) {
	linuxPackage := new(model.LinuxPackage)
	err := repo.dbConnection.NewSelect().Model(linuxPackage).Where("name_with_distro = ?", nameWithDistro).Scan(context.Background())
	if err != nil {
		return nil, err
	}
	return linuxPackage, nil
}

func (repo LinuxPackageRepository) DeleteById(linuxPackageId int64) (bool, error) {
	linuxPackage, err := repo.GetById(linuxPackageId)
	if err != nil {
		return false, err
	}
	_, err = repo.dbConnection.NewDelete().Model(linuxPackage).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllLinuxPackageByQuery executes a parameterized query to prevent SQL injection
func (repo LinuxPackageRepository) GetAllLinuxPackageByQuery(query string, params []interface{}) ([]model.LinuxPackage, error) {
	var linuxPackages []model.LinuxPackage
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &linuxPackages)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &linuxPackages)
	}
	if err != nil {
		return linuxPackages, err
	}
	return linuxPackages, nil
}

// CountByQuery executes a parameterized count query to prevent SQL injection
func (repo LinuxPackageRepository) CountByQuery(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo LinuxPackageRepository) GetLatestLinuxPackage() (model.LinuxPackage, error) {
	var linuxPackage model.LinuxPackage
	err := repo.dbConnection.NewSelect().Model(&linuxPackage).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.LinuxPackage{}, err
	}
	return linuxPackage, nil
}
