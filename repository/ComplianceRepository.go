package repository

import (
	"context"
	"deployment/db"
	"deployment/logger"
	"deployment/model"
	"github.com/uptrace/bun"
)

type ComplianceRepository struct {
	dbConnection *bun.DB
}

func NewComplianceRepository() *ComplianceRepository {
	return &ComplianceRepository{dbConnection: db.Connection}
}

func (repo ComplianceRepository) Create(compliance *model.Compliance) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(compliance).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return compliance.Id, nil
}

func (repo ComplianceRepository) CreateComplianceTaskResult(complianceTaskResult *model.ComplianceTaskResult) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(complianceTaskResult).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return complianceTaskResult.Id, nil
}

func (repo ComplianceRepository) Update(compliance *model.Compliance) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(compliance).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return compliance.Id, nil
}

func (repo ComplianceRepository) GetById(complianceId int64, includeArchive bool) (model.Compliance, error) {
	var compliance model.Compliance
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&compliance).Where("id = ?", complianceId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&compliance).Where("id = ?", complianceId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return compliance, err
	}
	return compliance, nil
}

func (repo ComplianceRepository) DeleteById(complianceId int64) (bool, error) {
	compliance, err := repo.GetById(complianceId, false)
	if err != nil {
		return false, err
	}
	compliance.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&compliance).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo ComplianceRepository) PermanentDeleteById(complianceId int64) (bool, error) {
	compliance := new(model.Compliance)
	compliance.Id = complianceId
	_, err := repo.dbConnection.NewDelete().Model(compliance).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllCompliance executes a parameterized query to prevent SQL injection
func (repo ComplianceRepository) GetAllCompliance(query string, params []interface{}) ([]model.Compliance, error) {
	var compliance []model.Compliance
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &compliance)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &compliance)
	}
	if err != nil {
		return compliance, err
	}
	return compliance, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo ComplianceRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo ComplianceRepository) GetComplianceDeploymentIds(agentId int64) ([]string, error) {
	var deploymentIds []string
	err := repo.dbConnection.NewRaw("select deployment_id from deployment.compliance_task_results where removed = false and agent_id = ? group by deployment_id", agentId).Scan(context.Background(), &deploymentIds)
	if err != nil {
		return deploymentIds, err
	}

	return deploymentIds, nil
}

func (repo ComplianceRepository) PermanentDeleteComplianceTaskResult(agentId int64, complianceId int64, deploymentId int64, bundleId int64) (bool, error) {
	complianceTaskResult := new(model.ComplianceTaskResult)
	if agentId != 0 {
		complianceTaskResult.AgentId = agentId
		_, err := repo.dbConnection.NewDelete().Model(complianceTaskResult).Where("agent_id = ? ", agentId).Exec(context.Background())
		if err != nil {
			return false, err
		}
	}
	if complianceId != 0 {
		complianceTaskResult.ComplianceId = complianceId
		_, err := repo.dbConnection.NewDelete().Model(complianceTaskResult).Where("compliance_id = ? ", complianceId).Exec(context.Background())
		if err != nil {
			return false, err
		}
	}
	if deploymentId != 0 {
		complianceTaskResult.DeploymentId = deploymentId
		_, err := repo.dbConnection.NewDelete().Model(complianceTaskResult).Where("deployment_id = ? ", deploymentId).Exec(context.Background())
		if err != nil {
			return false, err
		}
	}
	if bundleId != 0 {
		complianceTaskResult.FrameworkId = bundleId
		_, err := repo.dbConnection.NewDelete().Model(complianceTaskResult).Where("framework_id = ? ", bundleId).Exec(context.Background())
		if err != nil {
			return false, err
		}
	}
	return true, nil
}

func (repo ComplianceRepository) DeleteComplianceTaskResult(agentId int64, complianceId int64, deploymentId int64, bundleId int64) {
	if agentId != 0 {
		query := "Update deployment.compliance_task_results set removed = true WHERE agent_id = ?"
		_, err := repo.dbConnection.NewRaw(query, agentId).Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[DeleteComplianceTaskResult]", err)
		}
	}
	if complianceId != 0 {
		query := "Update deployment.compliance_task_results set removed = true WHERE compliance_id = ?"
		_, err := repo.dbConnection.NewRaw(query, complianceId).Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[DeleteComplianceTaskResult]", err)
		}
	}
	if deploymentId != 0 {
		query := "Update deployment.compliance_task_results set removed = true WHERE deployment_id = ?"
		_, err := repo.dbConnection.NewRaw(query, deploymentId).Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[DeleteComplianceTaskResult]", err)
		}
	}
	if bundleId != 0 {
		query := "Update deployment.compliance_task_results set removed = true WHERE framework_id = ?"
		_, err := repo.dbConnection.NewRaw(query, bundleId).Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[DeleteComplianceTaskResult]", err)
		}
	}
}

func (repo ComplianceRepository) GetLatestComplianceTaskResults(agentId int64, complianceId []int64, deploymentId int64, bundleId int64) ([]map[string]interface{}, error) {
	var complianceTaskResults []map[string]interface{}
	query := "SELECT DISTINCT ON (compliance_id) id, created_time,compliance_id, task_status,task_result FROM deployment.compliance_task_results where agent_id = ? and compliance_id in (?) and deployment_id = ? and framework_id = ? ORDER BY compliance_id, created_time DESC;"
	err := repo.dbConnection.NewRaw(query, agentId, bun.In(complianceId), deploymentId, bundleId).Scan(context.Background(), &complianceTaskResults)
	if err != nil {
		return complianceTaskResults, err
	}

	return complianceTaskResults, nil
}

func (repo ComplianceRepository) GetComplianceTaskResult(agentId int64, complianceId []int64, deploymentId int64, bundleId int64, createdTime int64) ([]map[string]interface{}, error) {
	var complianceTaskResults []map[string]interface{}
	query := "SELECT DISTINCT ON (compliance_id) id, created_time,compliance_id, task_status,task_result FROM deployment.compliance_task_results where agent_id = ? and compliance_id in (?) and deployment_id = ? and framework_id = ? and created_time = ? ORDER BY compliance_id, created_time DESC;"
	err := repo.dbConnection.NewRaw(query, agentId, bun.In(complianceId), deploymentId, bundleId, createdTime).Scan(context.Background(), &complianceTaskResults)
	if err != nil {
		return complianceTaskResults, err
	}

	return complianceTaskResults, nil
}

func (repo ComplianceRepository) GetDeploymentComplianceTaskResults(agentIds []string, complianceId []int64, deploymentId int64, bundleId int64, from int64, to int64) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	query := "SELECT compliance_id,COUNT(*) AS total_count, SUM(CASE WHEN task_status = 5 THEN 1 ELSE 0 END) AS success_count, SUM(CASE WHEN task_status = 6 THEN 1 ELSE 0 END) AS failed_count FROM deployment.compliance_task_results WHERE agent_id in (?) AND compliance_id in (?) AND deployment_id = ? AND framework_id = ? and created_time >= ? AND created_time <= ? GROUP BY compliance_id ORDER BY compliance_id"
	err := repo.dbConnection.NewRaw(query, bun.In(agentIds), bun.In(complianceId), deploymentId, bundleId, from, to).Scan(context.Background(), &result)
	if err != nil {
		return result, err
	}

	return result, nil
}

func (repo ComplianceRepository) GetComplianceTimes(assetId int64, deploymentId int64, count int64) ([]int64, error) {
	var createdTimes []int64
	query := "SELECT DISTINCT created_time FROM deployment.compliance_task_results WHERE agent_id = ? and deployment_id = ? ORDER BY created_time DESC LIMIT ?"
	err := repo.dbConnection.NewRaw(query, assetId, deploymentId, count).Scan(context.Background(), &createdTimes)
	if err != nil {
		return createdTimes, err
	}

	return createdTimes, nil
}

func (repo ComplianceRepository) DeleteByAssetId(assetIds []string) {
	query := "Update deployment.compliance_task_results set removed = true WHERE agent_id in (?)"
	_, err := repo.dbConnection.NewRaw(query, bun.In(assetIds)).Exec(context.Background())
	if err != nil {
		return
	}
}
