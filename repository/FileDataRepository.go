package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type FileDataRepository struct {
	dbConnection *bun.DB
}

func NewFileDataRepository() *FileDataRepository {
	return &FileDataRepository{
		dbConnection: db.Connection,
	}
}

func (repo FileDataRepository) Create(fileData *model.FileData) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(fileData).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return fileData.Id, nil
}

func (repo FileDataRepository) GetById(id int64) (model.FileData, error) {
	var fileData model.FileData
	err := repo.dbConnection.NewSelect().Model(&fileData).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		return fileData, err
	}
	return fileData, nil
}

func (repo FileDataRepository) GetByRefName(refName string) (model.FileData, error) {
	var fileData model.FileData
	err := repo.dbConnection.NewSelect().Model(&fileData).Where("ref_name = ?", refName).Scan(context.Background())
	if err != nil {
		return fileData, err
	}
	return fileData, nil
}
