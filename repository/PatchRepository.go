package repository

import (
	"context"
	"deployment/db"
	"deployment/logger"
	"deployment/model"
	"github.com/uptrace/bun"
)

type PatchRepository struct {
	dbConnection *bun.DB
}

func NewPatchRepository() *PatchRepository {
	return &PatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchRepository) Create(patch *model.Patch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patch).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo PatchRepository) Update(patch *model.Patch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo PatchRepository) GetById(id int64, includeArchive bool) (model.Patch, error) {
	var patch model.Patch
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo PatchRepository) GetByUuid(patchUuid string) (model.Patch, error) {
	var patch model.Patch
	var err error
	err = repo.dbConnection.NewSelect().Model(&patch).Where("uuid = ?", patchUuid).Where("removed = ?", false).Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo PatchRepository) GetByName(patchName string) (model.Patch, error) {
	var patch model.Patch
	var err error
	err = repo.dbConnection.NewSelect().Model(&patch).Where("name = ?", patchName).Where("removed = ?", false).Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo PatchRepository) DeleteById(id int64) (bool, error) {
	patch, err := repo.GetById(id, false)
	if err != nil {
		return false, err
	}
	patch.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&patch).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllPatch executes a parameterized query to prevent SQL injection
func (repo PatchRepository) GetAllPatch(query string, params []interface{}) ([]model.Patch, error) {
	var patches []model.Patch
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patches)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patches)
	}
	if err != nil {
		return patches, err
	}
	return patches, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo PatchRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo PatchRepository) DeleteAll() (bool, error) {
	query := `DELETE FROM deployment.patch WHERE id != 0`
	_, err := repo.dbConnection.NewRaw(query).Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("Error while executing query AllPatchListTxt ", err)
	}
	return true, nil
}
