package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type LanguageRepository struct {
	dbConnection *bun.DB
}

func NewLanguageRepository() *LanguageRepository {
	return &LanguageRepository{dbConnection: db.Connection}
}

func (repo LanguageRepository) Create(language *model.Language) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(language).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return language.Id, nil
}

func (repo LanguageRepository) Update(language *model.Language) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(language).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return language.Id, nil
}

func (repo LanguageRepository) GetById(languageId int64, includeArchive bool) (model.Language, error) {
	var language model.Language
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&language).Where("id = ?", languageId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&language).Where("id = ?", languageId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return language, err
	}
	return language, nil
}

func (repo LanguageRepository) PermanentDeleteById(languageId int64) (bool, error) {
	language := new(model.Language)
	language.Id = languageId
	_, err := repo.dbConnection.NewDelete().Model(language).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo LanguageRepository) GetAllLanguage(query string, params []interface{}) ([]model.Language, error) {
	var languages []model.Language
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &languages)

	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &languages)
	}
	if err != nil {
		return languages, err
	}
	return languages, nil
}

func (repo LanguageRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		return 0
	}
	return count
}

func (repo LanguageRepository) GetByCode(code int64) (model.Language, error) {
	var language model.Language
	err := repo.dbConnection.NewSelect().Model(&language).Where("code = ?", code).Scan(context.Background())
	if err != nil {
		return language, err
	}
	return language, nil
}
