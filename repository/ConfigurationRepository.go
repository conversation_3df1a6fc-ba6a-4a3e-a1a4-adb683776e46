package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type ConfigurationRepository struct {
	dbConnection *bun.DB
}

func NewConfigurationRepository() *ConfigurationRepository {
	return &ConfigurationRepository{
		dbConnection: db.Connection,
	}
}

func (repo ConfigurationRepository) Create(config *model.Configuration) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(config).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return config.Id, nil
}

func (repo ConfigurationRepository) Update(config *model.Configuration) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(config).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return config.Id, nil
}

func (repo ConfigurationRepository) GetById(configId int64, includeArchive bool) (model.Configuration, error) {
	var config model.Configuration
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&config).Where("id = ?", configId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&config).Where("id = ?", configId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return config, err
	}
	return config, nil
}

func (repo ConfigurationRepository) DeleteById(configId int64) (bool, error) {
	config, err := repo.GetById(configId, false)
	if err != nil {
		return false, err
	}
	config.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&config).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo ConfigurationRepository) PermanentDeleteById(configId int64) (bool, error) {
	config := new(model.Configuration)
	config.Id = configId
	_, err := repo.dbConnection.NewDelete().Model(config).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo ConfigurationRepository) GetAllConfiguration(query string) ([]model.Configuration, error) {
	var configurations []model.Configuration
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &configurations)
	if err != nil {
		return configurations, err
	}
	return configurations, nil
}

// GetAllConfigurations executes a parameterized query to prevent SQL injection
func (repo ConfigurationRepository) GetAllConfigurations(query string, params []interface{}) ([]model.Configuration, error) {
	var configurations []model.Configuration
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &configurations)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &configurations)
	}
	if err != nil {
		return configurations, err
	}
	return configurations, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo ConfigurationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
