package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"deployment/rest/view"

	"github.com/uptrace/bun"
)

type AgentTaskRepository struct {
	dbConnection *bun.DB
}

func NewAgentTaskRepository() *AgentTaskRepository {
	return &AgentTaskRepository{
		dbConnection: db.Connection,
	}
}

func (repo AgentTaskRepository) Create(agentTask *model.AgentTask) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(agentTask).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return agentTask.Id, nil
}

func (repo AgentTaskRepository) Update(agentTask *model.AgentTask) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(agentTask).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return agentTask.Id, nil
}

func (repo AgentTaskRepository) GetById(agentTaskId int64) (model.AgentTask, error) {
	var agentTask model.AgentTask
	err := repo.dbConnection.NewSelect().Model(&agentTask).Where("id = ?", agentTaskId).Where("removed = ?", false).Scan(context.Background())
	if err != nil {
		return agentTask, err
	}
	return agentTask, nil
}

func (repo AgentTaskRepository) GetByOffsetAndSize(offset, size int) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask
	err := repo.dbConnection.NewSelect().Model(&agentTasks).
		Offset(offset).
		Limit(size).
		Scan(context.Background())
	if err != nil {
		return agentTasks, err
	}
	return agentTasks, nil
}

func (repo AgentTaskRepository) GetAllByAgentId(taskStatus, agentId int64) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask
	if taskStatus > 0 {
		err := repo.dbConnection.NewSelect().Model(&agentTasks).
			Where("agent_id = ?", agentId).
			Where("task_status = ?", taskStatus).
			Scan(context.Background())
		if err != nil {
			return agentTasks, err
		}
	} else {
		err := repo.dbConnection.NewSelect().Model(&agentTasks).
			Where("agent_id = ?", agentId).
			Scan(context.Background())
		if err != nil {
			return agentTasks, err
		}
	}
	return agentTasks, nil
}

func (repo AgentTaskRepository) CountByAgentId(taskStatus, agentId int64) int {
	var count int
	var err error
	if taskStatus > 0 {
		count, err = repo.dbConnection.NewSelect().Model(&model.AgentTask{}).
			Where("agent_id = ?", agentId).
			Where("task_status = ?", taskStatus).Count(context.Background())
	} else {
		count, err = repo.dbConnection.NewSelect().Model(&model.AgentTask{}).
			Where("agent_id = ?", agentId).
			Count(context.Background())
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo AgentTaskRepository) GetByAgentIdByOffsetAndSize(taskStatus, agentId int64, offset, size int) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask
	if taskStatus > 0 {
		err := repo.dbConnection.NewSelect().Model(&agentTasks).Where("agent_id = ?", agentId).
			Where("task_status = ?", taskStatus).
			Order("created_time DESC").
			Offset(offset).
			Limit(size).
			Scan(context.Background())
		if err != nil {
			return agentTasks, err
		}
	} else {
		err := repo.dbConnection.NewSelect().Model(&agentTasks).Where("agent_id = ?", agentId).
			Order("created_time DESC").
			Offset(offset).
			Limit(size).
			Scan(context.Background())
		if err != nil {
			return agentTasks, err
		}
	}
	return agentTasks, nil
}

func (repo AgentTaskRepository) Count() int {
	count, err := repo.dbConnection.NewSelect().Model(&model.AgentTask{}).Count(context.Background())
	if err != nil {
		return 0
	}
	return count
}

func (repo AgentTaskRepository) GetAllAgentTasks() ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask
	err := repo.dbConnection.NewSelect().Model(&agentTasks).Scan(context.Background())
	if err != nil {
		return agentTasks, err
	}
	return agentTasks, nil
}

func (repo AgentTaskRepository) GetAllTaskByRefIdsAndRefModel(ids []int64, refModel string) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask
	err := repo.dbConnection.NewSelect().Model(&agentTasks).
		Where("ref_id IN (?) AND ref_model = ?", bun.In(ids), refModel).
		Scan(context.Background())
	if err != nil {
		return agentTasks, err
	}
	return agentTasks, nil
}

func (repo AgentTaskRepository) CountByRefIdsAndRefModel(ids []int64, refModel string) int {
	count, err := repo.dbConnection.NewSelect().Model(&model.AgentTask{}).
		Where("ref_id IN (?) AND ref_model = ?", bun.In(ids), refModel).
		Count(context.Background())
	if err != nil {
		return 0
	}
	return count
}

func (repo AgentTaskRepository) GetAllTaskByQuery(query string, params []interface{}) ([]model.AgentTask, error) {
	var tasks []model.AgentTask
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &tasks)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &tasks)
	}
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

func (repo AgentTaskRepository) GetAllTaskByViewQuery(query string, params []interface{}) ([]view.AgentTaskDetailsView, error) {
	var tasks []view.AgentTaskDetailsView
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &tasks)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &tasks)
	}
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

func (repo AgentTaskRepository) CountByQuery(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo AgentTaskRepository) GetByAgentIdAndTaskType(taskType, agentId int64, offset, size int) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask

	if !(taskType <= 0 || agentId <= 0) {
		err := repo.dbConnection.NewSelect().Model(&agentTasks).Where("agent_id = ?", agentId).
			Where("task_type = ?", taskType).
			Order("created_time DESC").
			Offset(offset).
			Limit(size).
			Scan(context.Background())
		if err != nil {
			return agentTasks, err
		}
	}

	return agentTasks, nil
}

func (repo AgentTaskRepository) GetByDeploymentId(deploymentId int64) ([]model.AgentTask, error) {
	var agentTasks []model.AgentTask

	err := repo.dbConnection.NewSelect().Model(&agentTasks).
		Where("deployment_id = ?", deploymentId).
		Order("created_time DESC").
		Scan(context.Background())
	if err != nil {
		return agentTasks, err
	}

	return agentTasks, nil
}

func (repo AgentTaskRepository) DeleteByDeploymentId(deploymentId int64) {
	_, err := repo.dbConnection.NewRaw("Update deployment.agent_tasks set removed = true WHERE deployment_id = ?", deploymentId).Exec(context.Background())
	if err != nil {
		return
	}
}

func (repo AgentTaskRepository) DeleteByAssetId(assetIds []string) {
	_, err := repo.dbConnection.NewRaw("Update deployment.agent_tasks set removed = true WHERE agent_id in (?)", bun.In(assetIds)).Exec(context.Background())
	if err != nil {
		return
	}
}
