package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type AgentTaskResultRepository struct {
	dbConnection *bun.DB
}

func NewAgentTaskResultRepository() *AgentTaskResultRepository {
	return &AgentTaskResultRepository{
		dbConnection: db.Connection,
	}
}

func (repo AgentTaskResultRepository) Create(AgentTaskResult *model.AgentTaskResult) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(AgentTaskResult).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return AgentTaskResult.Id, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo AgentTaskResultRepository) GetAll(query string, params []interface{}) ([]model.AgentTaskResult, error) {
	var tasks []model.AgentTaskResult
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &tasks)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &tasks)
	}
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

func (repo AgentTaskResultRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
