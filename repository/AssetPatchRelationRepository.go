package repository

import (
	"context"
	"deployment/db"
	"deployment/logger"
	"deployment/model"
	"strings"

	"github.com/uptrace/bun"
)

type AssetPatchRelationRepository struct {
	dbConnection *bun.DB
}

func NewAgentPatchRelationRepository() *AssetPatchRelationRepository {
	return &AssetPatchRelationRepository{
		dbConnection: db.Connection,
	}
}

func (repo AssetPatchRelationRepository) Create(patchRelation *model.AssetPatchRelation) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patchRelation).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchRelation.Id, nil
}

func (repo AssetPatchRelationRepository) Update(patchRelation *model.AssetPatchRelation) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patchRelation).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchRelation.Id, nil
}

func (repo AssetPatchRelationRepository) GetById(patchRelationId int64, includeArchive bool) (model.AssetPatchRelation, error) {
	var patchRelation model.AssetPatchRelation
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patchRelation).Where("id = ?", patchRelationId).Scan(context.Background(), &patchRelation)
	} else {
		err = repo.dbConnection.NewSelect().Model(&patchRelation).Where("id = ?", patchRelationId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patchRelation, err
	}
	return patchRelation, nil
}

func (repo AssetPatchRelationRepository) GetByPatchIdAssetIdAndPatchState(patchId, assetId, patchState int64) (model.AssetPatchRelation, error) {
	var patchRelation model.AssetPatchRelation
	var err error
	err = repo.dbConnection.NewSelect().Model(&patchRelation).Where("patch_id = ?", patchId).
		Where("asset_id = ?", assetId).
		Where("patch_state = ?", patchState).Scan(context.Background())
	if err != nil {
		return patchRelation, err
	}
	return patchRelation, nil
}

func (repo AssetPatchRelationRepository) GetByPatchIdAndAssetId(patchId, assetId int64) (model.AssetPatchRelation, error) {
	var patchRelation model.AssetPatchRelation
	var err error
	err = repo.dbConnection.NewSelect().Model(&patchRelation).Where("patch_id = ?", patchId).
		Where("asset_id = ?", assetId).Scan(context.Background())
	if err != nil {
		return patchRelation, err
	}
	return patchRelation, nil
}

func (repo AssetPatchRelationRepository) PermanentDeleteById(patchRelationId int64) (bool, error) {
	assetPatchRelation := new(model.AssetPatchRelation)
	assetPatchRelation.Id = patchRelationId
	_, err := repo.dbConnection.NewDelete().Model(assetPatchRelation).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllAgentPatchView executes a parameterized query to prevent SQL injection
func (repo AssetPatchRelationRepository) GetAllAgentPatchView(query string, params []interface{}) ([]model.AssetPatchRelation, error) {
	var patchRelations []model.AssetPatchRelation
	query = strings.ReplaceAll(query, "SELECT *", "SELECT id, patch_id, asset_id, patch_state")
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patchRelations)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patchRelations)
	}
	if err != nil {
		return patchRelations, err
	}
	return patchRelations, nil
}

// GetAllAgentPatchRelation executes a parameterized query to prevent SQL injection
func (repo AssetPatchRelationRepository) GetAllAgentPatchRelation(query string, params []interface{}) ([]model.AssetPatchRelation, error) {
	var patchRelations []model.AssetPatchRelation
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patchRelations)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patchRelations)
	}
	if err != nil {
		return patchRelations, err
	}
	return patchRelations, nil
}

func (repo AssetPatchRelationRepository) GetAllAgentPatchRelations(query string, params []interface{}) ([]map[string]interface{}, error) {
	var patchRelations []map[string]interface{}
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patchRelations)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patchRelations)
	}
	if err != nil {
		return patchRelations, err
	}
	return patchRelations, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo AssetPatchRelationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
func (repo AssetPatchRelationRepository) DeleteAll() (bool, error) {
	query := `DELETE FROM deployment.asset_patch_relation WHERE id != 0`
	_, err := repo.dbConnection.Exec(query)
	if err != nil {
		logger.ServiceLogger.Error("Error while executing query AllPatchListTxt ", err)
	}
	return true, nil
}

func (repo AssetPatchRelationRepository) DeleteByAssetId(assetIds []string) {
	_, err := repo.dbConnection.NewRaw("Update deployment.asset_patch_relation set removed = true WHERE asset_id in (?)", bun.In(assetIds)).Exec(context.Background())
	if err != nil {
		return
	}
}
