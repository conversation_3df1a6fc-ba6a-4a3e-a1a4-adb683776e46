package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type PackageRepository struct {
	dbConnection *bun.DB
}

func NewPackageRepository() *PackageRepository {
	return &PackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo PackageRepository) Create(pkg *model.Package) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(pkg).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo PackageRepository) Update(pkg *model.Package) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo PackageRepository) GetById(pkgId int64, includeArchive bool) (model.Package, error) {
	var pkg model.Package
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", pkgId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", pkgId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

func (repo PackageRepository) DeleteById(pkgId int64) (bool, error) {
	pkg, err := repo.GetById(pkgId, false)
	if err != nil {
		return false, err
	}
	pkg.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&pkg).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo PackageRepository) PermanentDeleteById(pkgId int64) (bool, error) {
	pkg := new(model.Package)
	pkg.Id = pkgId
	_, err := repo.dbConnection.NewDelete().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

func (repo PackageRepository) GetAllPackage(query string) ([]model.Package, error) {
	var pkg []model.Package
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &pkg)
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

// GetAllPackages executes a parameterized query to prevent SQL injection
func (repo PackageRepository) GetAllPackages(query string, params []interface{}) ([]model.Package, error) {
	var pkg []model.Package
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &pkg)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &pkg)
	}
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo PackageRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
