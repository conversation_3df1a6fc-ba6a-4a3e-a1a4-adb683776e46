package repository

import (
	"context"
	"database/sql"
	"deployment/db"
	"deployment/model"
	"errors"
	"fmt"

	"github.com/uptrace/bun"
)

type AutoPatchTestRepository struct {
	dbConnection *bun.DB
}

func NewAutoPatchTestRepository() *AutoPatchTestRepository {
	return &AutoPatchTestRepository{dbConnection: db.Connection}
}

func (repo *AutoPatchTestRepository) Create(autoPatchTest *model.AutoPatchTest) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(autoPatchTest).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, fmt.Errorf("failed to create AutoPatchTest: %w", err)
	}
	return autoPatchTest.Id, nil
}

func (repo *AutoPatchTestRepository) Update(autoPatchTest *model.AutoPatchTest) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(autoPatchTest).WherePK().Exec(context.Background())
	if err != nil {
		return 0, fmt.Errorf("failed to update AutoPatchTest: %w", err)
	}
	return autoPatchTest.Id, nil
}

func (repo *AutoPatchTestRepository) GetById(id int64) (model.AutoPatchTest, error) {
	var autoPatchTest model.AutoPatchTest
	err := repo.dbConnection.NewSelect().Model(&autoPatchTest).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return autoPatchTest, fmt.Errorf("AutoPatchTest with id %d not found", id)
		}
		return autoPatchTest, fmt.Errorf("failed to get AutoPatchTest by id %d: %w", id, err)
	}
	return autoPatchTest, nil
}

func (repo *AutoPatchTestRepository) DeleteById(id int64) (bool, error) {
	_, err := repo.dbConnection.NewUpdate().Model((*model.AutoPatchTest)(nil)).Where("id = ?", id).Set("removed = ?", true).Exec(context.Background())
	if err != nil {
		return false, fmt.Errorf("failed to mark AutoPatchTest with id %d as removed: %w", id, err)
	}
	return true, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo *AutoPatchTestRepository) GetAll(query string, params []interface{}) ([]model.AutoPatchTest, error) {
	var autoPatchTests []model.AutoPatchTest
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &autoPatchTests)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &autoPatchTests)
	}
	if err != nil {
		return autoPatchTests, fmt.Errorf("failed to get all AutoPatchTests: %w", err)
	}
	return autoPatchTests, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo *AutoPatchTestRepository) Count(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, fmt.Errorf("failed to count AutoPatchTests: %w", err)
	}
	return count, nil
}
