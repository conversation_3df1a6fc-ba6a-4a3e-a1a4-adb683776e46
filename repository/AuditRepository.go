package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type AuditRepository struct {
	dbConnection *bun.DB
}

func NewAuditRepository() *AuditRepository {
	return &AuditRepository{
		dbConnection: db.Connection,
	}
}

func (repo AuditRepository) Create(audit *model.Audit) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(audit).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return audit.Id, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo AuditRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

// GetAllAudits executes a parameterized query to prevent SQL injection
func (repo AuditRepository) GetAllAudits(query string, params []interface{}) ([]model.Audit, error) {
	var audits []model.Audit
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &audits)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &audits)
	}
	if err != nil {
		return audits, err
	}
	return audits, nil
}
