package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type AssetRepository struct {
	dbConnection *bun.DB
}

func NewAssetRepository() *AssetRepository {
	return &AssetRepository{
		dbConnection: db.Connection,
	}
}

func (repo AssetRepository) Create(asset *model.Asset) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(asset).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return asset.Id, nil
}

func (repo AssetRepository) Update(asset *model.Asset) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(asset).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return asset.Id, nil
}

func (repo AssetRepository) GetById(assetId int64, includeArchive bool) (model.Asset, error) {
	var asset model.Asset
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&asset).Where("asset_id = ?", assetId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&asset).Where("asset_id = ?", assetId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return asset, err
	}
	return asset, nil
}

func (repo AssetRepository) DeleteByAssetId(assetIds []string) {
	_, err := repo.dbConnection.NewRaw("Update deployment.asset set removed = true WHERE asset_id in (?)", bun.In(assetIds)).Exec(context.Background())
	if err != nil {
		return
	}
}
