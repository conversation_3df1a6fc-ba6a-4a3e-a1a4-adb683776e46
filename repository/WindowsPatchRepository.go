package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type WindowsPatchRepository struct {
	dbConnection *bun.DB
}

func NewWindowsPatchRepository() *WindowsPatchRepository {
	return &WindowsPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo WindowsPatchRepository) Create(wPatch *model.WindowsPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(wPatch).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return wPatch.Id, nil
}

func (repo WindowsPatchRepository) Update(wPatch *model.WindowsPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(wPatch).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return wPatch.Id, nil
}

func (repo WindowsPatchRepository) GetById(wPatchId int64, includeArchive bool) (model.WindowsPatch, error) {
	var wPatch model.WindowsPatch
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&wPatch).Where("id = ?", wPatchId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&wPatch).Where("id = ?", wPatchId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return wPatch, err
	}
	return wPatch, nil
}

func (repo WindowsPatchRepository) PermanentDeleteById(wPatchId int64) (bool, error) {
	wPatch := new(model.WindowsPatch)
	wPatch.Id = wPatchId
	_, err := repo.dbConnection.NewDelete().Model(wPatch).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllWindowsPatches executes a parameterized query to prevent SQL injection
func (repo WindowsPatchRepository) GetAllWindowsPatches(query string, params []interface{}) ([]model.WindowsPatch, error) {
	var wPatch []model.WindowsPatch
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &wPatch)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &wPatch)
	}
	if err != nil {
		return wPatch, err
	}
	return wPatch, nil
}

// GetAllWindowsPatch executes a parameterized query to prevent SQL injection
func (repo WindowsPatchRepository) GetAllWindowsPatch(query string, params []interface{}) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &result)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &result)
	}
	if err != nil {
		return result, err
	}
	return result, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo WindowsPatchRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo WindowsPatchRepository) GetByUuid(uuid string) (model.WindowsPatch, error) {
	var patch model.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo WindowsPatchRepository) GetLatestWindowsPatch() (model.WindowsPatch, error) {
	var patch model.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("last_updated_time != 0").
		Order("last_updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.WindowsPatch{}, err
	}
	return patch, nil
}
