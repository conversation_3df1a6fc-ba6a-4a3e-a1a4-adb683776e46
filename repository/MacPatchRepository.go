package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type MacPatchRepository struct {
	dbConnection *bun.DB
}

func NewMacPatchRepository() *MacPatchRepository {
	return &MacPatchRepository{dbConnection: db.Connection}
}

func (repo MacPatchRepository) Create(mPatch *model.MacOsPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(mPatch).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return mPatch.Id, nil
}

func (repo MacPatchRepository) Update(mPatch *model.MacOsPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(mPatch).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return mPatch.Id, nil
}

func (repo <PERSON><PERSON>atchRepository) GetById(mPatchId int64, includeArchive bool) (model.MacOsPatch, error) {
	var macOsPatch model.MacOsPatch
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&macOsPatch).Where("id = ?", mPatchId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&macOsPatch).Where("id = ?", mPatchId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return macOsPatch, err
	}
	return macOsPatch, nil
}

// GetAllMacPatches executes a parameterized query to prevent SQL injection
func (repo MacPatchRepository) GetAllMacPatches(query string, params []interface{}) ([]model.MacOsPatch, error) {
	var macOsPatches []model.MacOsPatch
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &macOsPatches)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &macOsPatches)
	}
	if err != nil {
		return macOsPatches, err
	}
	return macOsPatches, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo MacPatchRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo MacPatchRepository) GetByProductKey(productKey string) (model.MacOsPatch, error) {
	var patch model.MacOsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("product_key = ?", productKey).Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo MacPatchRepository) GetLatestMacPatch() (model.MacOsPatch, error) {
	var patch model.MacOsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).
		Order("updated_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.MacOsPatch{}, err
	}
	return patch, nil
}
