package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type CabSyncHistoryRepository struct {
	dbConnection *bun.DB
}

func NewCabSyncHistoryRepository() *CabSyncHistoryRepository {
	return &CabSyncHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo CabSyncHistoryRepository) Create(cabSyncHistory *model.CabSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(cabSyncHistory).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return cabSyncHistory.Id, nil
}

func (repo CabSyncHistoryRepository) Update(cabSyncHistory *model.CabSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(cabSyncHistory).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return cabSyncHistory.Id, nil
}

func (repo CabSyncHistoryRepository) GetById(id int64) (model.CabSyncHistory, error) {
	var history model.CabSyncHistory
	err := repo.dbConnection.NewSelect().Model(&history).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		return history, err
	}
	return history, nil
}

func (repo CabSyncHistoryRepository) GetByReleaseDate(releaseDate string) (model.CabSyncHistory, error) {
	var history model.CabSyncHistory
	err := repo.dbConnection.NewSelect().Model(&history).Where("release_date = ?", releaseDate).Scan(context.Background())
	if err != nil {
		return history, err
	}
	return history, nil
}

func (repo CabSyncHistoryRepository) GetLatestCabSyncHistory() (model.CabSyncHistory, error) {
	var history model.CabSyncHistory
	err := repo.dbConnection.NewSelect().Model(&history).Where("last_sync_time != 0").
		Order("last_sync_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return model.CabSyncHistory{}, err
	}
	return history, nil
}
