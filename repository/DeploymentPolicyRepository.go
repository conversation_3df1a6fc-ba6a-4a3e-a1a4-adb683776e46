package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type DeploymentPolicyRepository struct {
	dbConnection *bun.DB
}

func NewDeploymentPolicyRepository() *DeploymentPolicyRepository {
	return &DeploymentPolicyRepository{
		dbConnection: db.Connection,
	}
}

func (repo DeploymentPolicyRepository) Create(policy *model.DeploymentPolicy) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(policy).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return policy.Id, nil
}

func (repo DeploymentPolicyRepository) Update(policy *model.DeploymentPolicy) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(policy).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return policy.Id, nil
}

func (repo DeploymentPolicyRepository) GetById(policyId int64, includeArchive bool) (model.DeploymentPolicy, error) {
	var deploymentPolicy model.DeploymentPolicy
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&deploymentPolicy).Where("id = ?", policyId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&deploymentPolicy).Where("id = ?", policyId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return deploymentPolicy, err
	}
	return deploymentPolicy, nil
}

func (repo DeploymentPolicyRepository) DeleteById(policyId int64) (bool, error) {
	deploymentPolicy, err := repo.GetById(policyId, false)
	if err != nil {
		return false, err
	}
	deploymentPolicy.Removed = true
	_, err = repo.dbConnection.NewUpdate().Model(&deploymentPolicy).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllPolicy executes a parameterized query to prevent SQL injection
func (repo DeploymentPolicyRepository) GetAllPolicy(query string, params []interface{}) ([]model.DeploymentPolicy, error) {
	var deploymentPolicies []model.DeploymentPolicy
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &deploymentPolicies)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &deploymentPolicies)
	}
	if err != nil {
		return deploymentPolicies, err
	}
	return deploymentPolicies, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo DeploymentPolicyRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}

func (repo DeploymentPolicyRepository) GetNameById(id int64) string {
	var name string
	// Use parameterized query to prevent SQL injection
	query := "select name from deployment.deployment_policies where id = ?"
	err := repo.dbConnection.NewRaw(query, id).Scan(context.Background(), &name)
	if err != nil {
		return ""
	}
	return name
}

func (repo DeploymentPolicyRepository) GetDisplayNameById(id int64) string {
	var displayName string
	// Use parameterized query to prevent SQL injection
	query := "select display_name from deployment.deployment_policies where id = ?"
	err := repo.dbConnection.NewRaw(query, id).Scan(context.Background(), &displayName)
	if err != nil {
		return ""
	}
	return displayName
}
