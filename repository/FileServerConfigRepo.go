package repository

import (
	"context"
	"deployment/db"
	"deployment/model"

	"github.com/uptrace/bun"
)

type FileServerConfigRepo struct {
	dbConnection *bun.DB
}

func NewFileServerConfigRepo() *FileServerConfigRepo {
	return &FileServerConfigRepo{dbConnection: db.Connection}
}

func (repo FileServerConfigRepo) Create(config *model.FileServerConfig) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(config).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return config.Id, nil
}

func (repo FileServerConfigRepo) Update(config *model.FileServerConfig) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(config).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return config.Id, nil
}

func (repo FileServerConfigRepo) GetById(configId int64, includeArchive bool) (model.FileServerConfig, error) {
	var config model.FileServerConfig
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&config).Where("id = ?", configId).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&config).Where("id = ?", configId).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return config, err
	}
	return config, nil
}

func (repo FileServerConfigRepo) GetByLocation(locationId int64, includeArchive bool) (model.FileServerConfig, error) {
	var config model.FileServerConfig
	var err error

	err = repo.dbConnection.NewSelect().Model(&config).
		Where("location_id = ?", locationId).
		Offset(0).
		Limit(1).
		Scan(context.Background())

	if err != nil {
		return config, err
	}
	return config, nil
}

func (repo FileServerConfigRepo) PermanentDeleteById(configId int64) (bool, error) {
	config := new(model.FileServerConfig)
	config.Id = configId
	_, err := repo.dbConnection.NewDelete().Model(config).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo FileServerConfigRepo) GetAll(query string, params []interface{}) ([]model.FileServerConfig, error) {
	var config []model.FileServerConfig
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &config)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &config)
	}
	if err != nil {
		return config, err
	}
	return config, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo FileServerConfigRepo) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
