package repository

import (
	"context"
	"database/sql"
	"deployment/db"
	"deployment/model"
	"errors"
	"fmt"

	"github.com/uptrace/bun"
)

type AutoPatchDeployRepository struct {
	dbConnection *bun.DB
}

func NewAutoPatchDeployRepository() *AutoPatchDeployRepository {
	return &AutoPatchDeployRepository{dbConnection: db.Connection}
}

func (repo *AutoPatchDeployRepository) Create(autoPatchDeploy *model.AutoPatchDeploy) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(autoPatchDeploy).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, fmt.Errorf("failed to create AutoPatchDeploy: %w", err)
	}
	return autoPatchDeploy.Id, nil
}

func (repo *AutoPatchDeployRepository) Update(autoPatchDeploy *model.AutoPatchDeploy) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(autoPatchDeploy).WherePK().Exec(context.Background())
	if err != nil {
		return 0, fmt.Errorf("failed to update AutoPatchDeploy: %w", err)
	}
	return autoPatchDeploy.Id, nil
}

func (repo *AutoPatchDeployRepository) GetById(id int64) (model.AutoPatchDeploy, error) {
	var autoPatchDeploy model.AutoPatchDeploy
	err := repo.dbConnection.NewSelect().Model(&autoPatchDeploy).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return autoPatchDeploy, fmt.Errorf("AutoPatchDeploy with id %d not found", id)
		}
		return autoPatchDeploy, fmt.Errorf("failed to get AutoPatchDeploy by id %d: %w", id, err)
	}
	return autoPatchDeploy, nil
}

func (repo *AutoPatchDeployRepository) DeleteById(id int64) (bool, error) {
	_, err := repo.dbConnection.NewUpdate().Model((*model.AutoPatchDeploy)(nil)).Where("id = ?", id).Set("removed = ?", true).Exec(context.Background())
	if err != nil {
		return false, fmt.Errorf("failed to mark AutoPatchDeploy with id %d as removed: %w", id, err)
	}
	return true, nil
}

// GetAll executes a parameterized query to prevent SQL injection
func (repo *AutoPatchDeployRepository) GetAll(query string, params []interface{}) ([]model.AutoPatchDeploy, error) {
	var autoPatchDeploys []model.AutoPatchDeploy
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &autoPatchDeploys)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &autoPatchDeploys)
	}
	if err != nil {
		return autoPatchDeploys, fmt.Errorf("failed to get all AutoPatchDeploys: %w", err)
	}
	return autoPatchDeploys, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo *AutoPatchDeployRepository) Count(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, fmt.Errorf("failed to count AutoPatchDeploys: %w", err)
	}
	return count, nil
}
