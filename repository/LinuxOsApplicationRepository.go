package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type LinuxOsApplicationRepository struct {
	dbConnection *bun.DB
}

func NewLinuxOsApplicationRepository() *LinuxOsApplicationRepository {
	return &LinuxOsApplicationRepository{dbConnection: db.Connection}
}

func (repo *LinuxOsApplicationRepository) Create(app *model.LinuxOsApplication) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(app).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return app.Id, nil
}

func (repo *LinuxOsApplicationRepository) Update(app *model.LinuxOsApplication) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(app).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return app.Id, nil
}

func (repo *LinuxOsApplicationRepository) GetById(appID int64) (model.LinuxOsApplication, error) {
	var app model.LinuxOsApplication
	err := repo.dbConnection.NewSelect().Model(&app).Where("id = ?", appID).Scan(context.Background())
	if err != nil {
		return app, err
	}
	return app, nil
}

func (repo *LinuxOsApplicationRepository) GetByNameWithVersion(nameWithVersion string) (model.LinuxOsApplication, error) {
	var app model.LinuxOsApplication
	err := repo.dbConnection.NewSelect().Model(&app).Where("name_with_version = ?", nameWithVersion).Scan(context.Background())
	if err != nil {
		return app, err
	}
	return app, nil
}

func (repo *LinuxOsApplicationRepository) DeleteById(appID int64) (bool, error) {
	app, err := repo.GetById(appID)
	if err != nil {
		return false, err
	}
	_, err = repo.dbConnection.NewDelete().Model(app).WherePK().Exec(context.Background())
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllLinuxOsApplicationsByQuery executes a parameterized query to prevent SQL injection
func (repo *LinuxOsApplicationRepository) GetAllLinuxOsApplicationsByQuery(query string, params []interface{}) ([]model.LinuxOsApplication, error) {
	var apps []model.LinuxOsApplication
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &apps)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &apps)
	}
	if err != nil {
		return apps, err
	}
	return apps, nil
}

// Count executes a parameterized count query to prevent SQL injection
func (repo *LinuxOsApplicationRepository) Count(query string, params []interface{}) int {
	var count int
	var err error
	if len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0
	}
	return count
}
