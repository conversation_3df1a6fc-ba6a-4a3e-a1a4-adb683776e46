package repository

import (
	"context"
	"deployment/db"
	"deployment/logger"
	"deployment/model"

	"github.com/uptrace/bun"
)

type PatchPreferenceRepository struct {
	dbConnection *bun.DB
}

func NewPatchPreferenceRepo() *PatchPreferenceRepository {
	return &PatchPreferenceRepository{
		dbConnection: db.Connection,
	}
}

func (repo *PatchPreferenceRepository) Get() (model.PatchPreference, error) {
	var preference model.PatchPreference
	err := repo.dbConnection.NewSelect().Model(&preference).Where("id = ?", 1).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("Error retrieving patch preference: %v", err)
		return preference, err
	}
	return preference, nil
}

func (repo PatchPreferenceRepository) Update(patchPreference *model.PatchPreference) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patchPreference).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patchPreference.Id, nil
}

func (repo *PatchPreferenceRepository) Create(patchPreference *model.PatchPreference) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patchPreference).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("Error creating patch preference: %v", err)
		return 0, err
	}
	return patchPreference.Id, nil
}

func (repo PatchPreferenceRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		return 0
	}
	return count
}
