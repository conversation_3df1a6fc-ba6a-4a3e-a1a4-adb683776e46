package common

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

var OsVersionPattern = regexp.MustCompile(".*?((\\d+\\.?){3}) ((Service Pack (\\d)|N\\/\\w|.+) )?[ -\\xa5]+ (\\d+).*")
var OsNamePattern = regexp.MustCompile(".*?Microsoft[\\(R\\)]{0,3} Windows[\\(R\\)?]{0,3} ?(Serverr? )?(\\d+\\.?\\d?( R2)?|XP|VistaT).*")
var OsArchPattern = regexp.MustCompile(".*?([\\w\\d]+?)-based PC.*")
var HotfixPattern = regexp.MustCompile(".*KB\\d+.*")
var OnlyHotfixPattern = regexp.MustCompile("KB\\d+")
var DisplayVersionPattern = regexp.MustCompile(`(\d+)H\d`)

var buildNumbers = map[string]string{
	"10240": "1507",
	"10586": "1511",
	"14393": "1607",
	"15063": "1703",
	"16299": "1709",
	"17134": "1803",
	"17763": "1809",
	"18362": "1903",
	"18363": "1909",
	"19041": "2004",
}

var clientVersionMap = map[string]string{
	"10.0.22000": "Windows 11 version 21H2",
	"10.0.19042": "Windows 10 Version 20H2",
	"10.0.19041": "Windows 10 Version 2004",
	"10.0.18363": "Windows 10 Version 1909",
	"10.0.18362": "Windows 10 Version 1903",
	"10.0.17763": "Windows 10 Version 1809",
	"10.0.17134": "Windows 10 Version 1803",
	"10.0.16299": "Windows 10 Version 1709",
	"10.0.15063": "Windows 10 Version 1703",
	"10.0.14393": "Windows 10 Version 1607",
	"10.0.10586": "Windows 10 Version 1511",
	"10.0.10240": "Windows 10",
	"6.3.9600":   "Windows 8.1",
	"6.2.9200":   "Windows 8",
	"6.1.7601":   "Windows 7 SP1",
	"6.1.7600":   "Windows 7",
}

var serverVersionMap = map[string]string{
	"10.0.17763": "Windows Server 2019, Version 1809",
	"10.0.16299": "Windows Server 2016, Version 1709",
	"10.0.14393": "Windows Server 2016, Version 1607",
	"6.3.9600":   "Windows Server 2012 R2",
	"6.2.9200":   "Windows Server 2012",
	"6.1.8400":   "Windows Home Server 2011",
}

type SysInfo struct {
	ProductFilter          string
	Version                string
	Arch                   string
	Win                    string
	MajorVersion           int
	MinorVersion           int
	BuildNumber            string
	HotFixes               []string
	OsArch                 string
	OsName                 string
	Language               string
	ServicePack            string
	ServerOs               bool
	InstalledBaseIds       []int64
	MissingBaseIds         []int64
	InstalledThirdPartyIds []int64
	MissingThirdPartyIds   []int64
}

func (s SysInfo) GetSysInfo(systemInfoTxt string) SysInfo {
	var info SysInfo
	var osVersion, productFilter, myBuild, servicePack, win, arch, version string
	var majorVersion, minorVersion int
	var serverOs bool

	if systemInfoTxt != "" {
		systemInfoTxt = strings.ReplaceAll(systemInfoTxt, "\\xA0", "\\x20")
		match := OsVersionPattern.FindStringSubmatch(systemInfoTxt)
		if len(match) > 0 {
			myBuild = match[6]
			servicePack = match[5]
		}
		match = OsNamePattern.FindStringSubmatch(systemInfoTxt)
		if len(match) > 0 {
			win = match[2]
		}
		match = OsArchPattern.FindStringSubmatch(systemInfoTxt)
		if len(match) > 0 {
			arch = match[1]
		}

		language := ""
		sysInfoAsArr := strings.Split(systemInfoTxt, "\n")
		for _, key := range sysInfoAsArr {
			if strings.Contains(strings.ToLower(key), "system locale") {
				language = strings.Split(strings.Split(key, ":")[1], ";")[0]
			} else if strings.Contains(strings.ToLower(key), "os version") && !strings.Contains(key, "BIOS Version") {
				osVersion = strings.Split(strings.Split(key, ":")[1], " ")[0]
			}
		}

		allMatches := []string{}
		m3 := HotfixPattern.FindAllString(systemInfoTxt, -1)
		if len(m3) > 0 {
			for _, mm := range m3 {
				allMatches = append(allMatches, strings.TrimSpace(strings.Split(mm, ":")[1]))
			}
		}

		if myBuild != "" {
			for build, ver := range buildNumbers {
				buildNum, _ := strconv.Atoi(build)
				myBuildNum, _ := strconv.Atoi(myBuild)
				if myBuildNum == buildNum {
					version = ver
					break
				}
				if myBuildNum > buildNum {
					version = ver
				} else {
					break
				}
			}
		}

		if !strings.ContainsAny(win, "XP VistaT 2003 2003 R2") {
			if arch == "X86" {
				arch = "32-bit"
			} else if arch == "64" {
				arch = "x64-based"
			}
		}

		switch win {
		case "XP":
			productFilter = "Microsoft Windows XP"
			if arch != "X86" {
				productFilter += fmt.Sprintf(" Professional %s Edition", arch)
			}
			if servicePack != "" {
				productFilter += fmt.Sprintf(" Service Pack %s", servicePack)
			}
		case "VistaT":
			productFilter = "Windows Vista"
			if arch != "X86" {
				productFilter += fmt.Sprintf(" %s Edition", arch)
			}
			if servicePack != "" {
				productFilter += fmt.Sprintf(" Service Pack %s", servicePack)
			}
		case "7":
			productFilter = fmt.Sprintf("Windows %s for %s Systems", win, arch)
			if servicePack != "" {
				productFilter += fmt.Sprintf(" Service Pack %s", servicePack)
			}
		case "8", "8.1", "10":
			productFilter = fmt.Sprintf("Windows %s Version %s for %s Systems", win, version, arch)
		case "2003":
			if arch == "X86" {
				arch = ""
			} else if arch == "64" {
				arch = " x64 Edition"
			}
			pversion := ""
			if version != "" {
				pversion = " " + version
			}
			productFilter = fmt.Sprintf("Microsoft Windows Server %s%s%s", win, arch, pversion)
		case "2008", "2008 R2", "2012", "2012 R2", "2016", "2019":
			pversion := ""
			if version != "" {
				pversion = " " + version
			}
			productFilter = fmt.Sprintf("Windows Server %s", win)
			if win == "2008" || win == "2008 R2" {
				productFilter += fmt.Sprintf(" for %s Systems", arch)
			}
			productFilter += pversion
		}

		osArch := ""
		if strings.Contains(arch, "64") || strings.Contains(strings.ToLower(arch), "arm64") {
			osArch = "x64"
		} else if strings.Contains(arch, "x86") || arch == "32-bit" {
			osArch = "x86"
		}

		osName := ""
		switch win {
		case "2008", "2008 R2", "2012", "2012 R2", "2016", "2019":
			osName = fmt.Sprintf("Windows Server %s", win)
		case "10":
			osName = fmt.Sprintf("Windows %s", win)
		default:
			osName = "Windows " + win
		}

		versions := strings.Split(osVersion, ".")
		if len(versions) >= 2 {
			majorVersion, _ = strconv.Atoi(versions[0])
			minorVersion, _ = strconv.Atoi(versions[1])
		}

		if strings.Contains(osName, "Server") {
			serverOs = true
		}

		info = SysInfo{
			ProductFilter: productFilter,
			Version:       version,
			Arch:          arch,
			Win:           win,
			MajorVersion:  majorVersion,
			MinorVersion:  minorVersion,
			BuildNumber:   myBuild,
			HotFixes:      allMatches,
			OsArch:        osArch,
			OsName:        osName,
			Language:      strings.TrimSpace(language),
			ServicePack:   servicePack,
			ServerOs:      serverOs,
		}
	}
	return info
}

func ExtractDisplayVersion(versionStr string) int {
	match := DisplayVersionPattern.FindStringSubmatch(versionStr)
	if len(match) > 1 {
		if num, err := strconv.Atoi(match[1]); err == nil {
			return num
		}
	}
	return 0
}

//func main() {
//	getSystemInfo("\nHost Name:                 WIN-UIA3P0EIIPS\nOS Name:                   Microsoft Windows Server 2019 Standard Evaluation\nOS Version:                10.0.17763 N/A Build 17763\nOS Manufacturer:           Microsoft Corporation\nOS Configuration:          Standalone Server\nOS Build Type:             Multiprocessor Free\nRegistered Owner:          Windows User\nRegistered Organization:\nProduct ID:                00431-10000-00000-AA707\nOriginal Install Date:     5/5/2024, 10:07:33 PM\nSystem Boot Time:          6/2/2024, 12:01:49 AM\nSystem Manufacturer:       Dell Inc.\nSystem Model:              Latitude 7490\nSystem Type:               x64-based PC\nProcessor(s):              1 Processor(s) Installed.\n                           [01]: Intel64 Family 6 Model 142 Stepping 10 GenuineIntel ~1910 Mhz\nBIOS Version:              Dell Inc. 1.35.0, 11/2/2023\nWindows Directory:         C:\\Windows\nSystem Directory:          C:\\Windows\\system32\nBoot Device:               \\Device\\HarddiskVolume1\nSystem Locale:             en-us;English (United States)\nInput Locale:              en-us;English (United States)\nTime Zone:                 (UTC-08:00) Pacific Time (US & Canada)\nTotal Physical Memory:     16,256 MB\nAvailable Physical Memory: 10,356 MB\nVirtual Memory: Max Size:  18,688 MB\nVirtual Memory: Available: 12,152 MB\nVirtual Memory: In Use:    6,536 MB\nPage File Location(s):     C:\\pagefile.sys\nDomain:                    WORKGROUP\nLogon Server:              N/A\nHotfix(s):                 3 Hotfix(s) Installed.\n                           [01]: KB5020627\n                           [02]: KB5019966\n                           [03]: KB5020374\nNetwork Card(s):           1 NIC(s) Installed.\n                           [01]: Intel(R) Ethernet Connection (4) I219-LM\n                                 Connection Name: Ethernet\n                                 DHCP Enabled:    Yes\n                                 DHCP Server:     ***********\n                                 IP address(es)\n                                 [01]: ***********\n                                 [02]: fe80::6960:c475:453d:a160\n                                 [03]: 2401:4900:1f3f:972c:740b:29c6:66:839b\nHyper-V Requirements:      VM Monitor Mode Extensions: Yes\n                           Virtualization Enabled In Firmware: Yes\n                           Second Level Address Translation: Yes\n                           Data Execution Prevention Available: Yes")
//}
