package common

import (
	"deployment/constant"
	"strings"
)

func GetLinuxDistributionNameByOsName(osName string) string {
	var version string
	tempOsName := osName
	if osName != "" {
		if len(strings.Split(osName, "-")) > 1 {
			osName = strings.Split(osName, "-")[1]
			if len(strings.Split(strings.TrimSpace(osName), " ")) > 0 {
				osName = strings.Split(strings.TrimSpace(osName), " ")[0]
				if len(strings.Split(strings.TrimSpace(osName), ".")) > 2 {
					versionInfo := strings.Split(strings.TrimSpace(osName), ".")
					osName = versionInfo[0] + "." + versionInfo[1]
				}
			}
		}
		var versionNumber string
		if strings.Contains(strings.ToLower(tempOsName), "ubuntu") {
			versionNumber = strings.TrimSpace(osName)
			if versionNumber != "" {
				version = constant.UbuntuVersionMap[versionNumber]
			}
		}
	}

	return version
}

func GetPatchArchitectureByAgentArch(osArch string) []string {
	var osArchSet []string

	if strings.Contains(strings.ToLower(osArch), "64") {
		osArchSet = []string{
			"amd64",
			"x64",
			"x86_64",
			"noarch",
			"all",
		}
	} else {
		osArchSet = []string{
			"i386",
			"x86",
			"i686",
			"i586",
			"noarch",
			"all",
		}
	}

	return osArchSet
}
