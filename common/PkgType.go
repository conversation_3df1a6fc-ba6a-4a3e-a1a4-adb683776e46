package common

import (
	"errors"
	"fmt"
)

type PkgType int

const (
	EXE PkgType = iota + 1
	MSI
	Script
	Zip
	Application
)

func (pt PkgType) String() string {
	switch pt {
	case EXE:
		return "exe"
	case MSI:
		return "msi"
	case Script:
		return "script"
	case Zip:
		return "zip"
	case Application:
		return "application"
	default:
		return "Unknown"
	}
}

func (pt PkgType) ToPkgType(pkgType string) (PkgType, error) {
	switch pkgType {
	case "exe":
		return EXE, nil
	case "msi":
		return MSI, nil
	case "application":
		return Application, nil
	case "zip":
		return Zip, nil
	case "script":
		return Script, nil
	default:
		return PkgType(0), errors.New(fmt.Sprintf("invalid os type value '%s'", pkgType))
	}
}
