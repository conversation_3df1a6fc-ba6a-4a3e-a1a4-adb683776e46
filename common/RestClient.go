package common

import (
	"bytes"
	"crypto/tls"
	"deployment/logger"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

func ExecutePostRequest(url string, payload []byte, headers map[string]string) ([]byte, bool) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	var buffer *bytes.Buffer
	if payload != nil {
		buffer = bytes.NewBuffer(payload)
	}

	request, err := http.NewRequest(http.MethodPost, url, buffer)

	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	request.Header.Add("Accept", "application/json")
	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status: ", response.Status)
		return nil, false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err.Error())
		}
	}(response.Body)

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	return body, true
}

func ExecuteGetRequest(url string, headers map[string]string) ([]byte, bool) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	request, err := http.NewRequest(http.MethodGet, url, nil)

	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	request.Header.Add("Accept", "application/json")
	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status: ", response.Status)
		return nil, false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err.Error())
		}
	}(response.Body)

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	return body, true
}

func ExecuteRequestToDownloadFile(url, downloadFilePath string, headers map[string]string) bool {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	request, err := http.NewRequest(http.MethodGet, url, nil)

	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}

	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}

		return false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status:", response.Status)
		return false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.PatchPoolingLogger.Error("Error download :", err.Error())
			err := os.Remove(downloadFilePath)
			if err != nil {
				logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
			}

		}
	}(response.Body)

	_, err2 := os.Stat(downloadFilePath)
	if !os.IsNotExist(err2) {
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
	}
	out, err := os.Create(downloadFilePath)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}
	defer func(out *os.File) {
		err := out.Close()
		if err != nil {
			logger.PatchPoolingLogger.Error("Error download :", err.Error())
			err := os.Remove(downloadFilePath)
			if err != nil {
				logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
			}
		}
	}(out)

	_, err = io.Copy(out, response.Body)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}

	return true
}

func ExecuteRequestToDownloadFileToDir(url, downloadDir string, headers map[string]string) bool {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error creating request:", err.Error())
		return false
	}

	// Add headers if provided
	for key, value := range headers {
		request.Header.Add(key, value)
	}

	// Execute the request
	response, err := client.Do(request)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error executing request:", err.Error())
		return false
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		logger.ServiceLogger.Warn(url, " response status:", response.Status)
		return false
	}

	// Extract filename from Content-Disposition header
	contentDisposition := response.Header.Get("Content-Disposition")
	_, params, err := mime.ParseMediaType(contentDisposition)
	var filename string

	if err == nil && params["filename"] != "" {
		filename = params["filename"]
	} else {
		// fallback to filename from URL
		parts := strings.Split(url, "/")
		filename = parts[len(parts)-1]
	}

	// Final file path to save
	finalPath := filepath.Join(downloadDir, filename)

	// Remove existing file if exists
	if _, err := os.Stat(finalPath); err == nil {
		_ = os.Remove(finalPath)
	}

	// Create the file
	out, err := os.Create(finalPath)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error creating file:", err.Error())
		return false
	}
	defer out.Close()

	// Write to file
	_, err = io.Copy(out, response.Body)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error saving file:", err.Error())
		_ = os.Remove(finalPath)
		return false
	}

	logger.PatchPoolingLogger.Info("Downloaded file saved to: " + finalPath)
	return true
}
