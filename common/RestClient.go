package common

import (
	"bytes"
	"crypto/tls"
	"deployment/logger"
	"io"
	"net/http"
	"os"
)

func ExecutePostRequest(url string, payload []byte, headers map[string]string) ([]byte, bool) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	var buffer *bytes.Buffer
	if payload != nil {
		buffer = bytes.NewBuffer(payload)
	}

	request, err := http.NewRequest(http.MethodPost, url, buffer)

	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	request.Header.Add("Accept", "application/json")
	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status: ", response.Status)
		return nil, false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err.Error())
		}
	}(response.Body)

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	return body, true
}

func ExecuteGetRequest(url string, headers map[string]string) ([]byte, bool) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	request, err := http.NewRequest(http.MethodGet, url, nil)

	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	request.Header.Add("Accept", "application/json")
	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status: ", response.Status)
		return nil, false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err.Error())
		}
	}(response.Body)

	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return nil, false
	}

	return body, true
}

func ExecuteRequestToDownloadFile(url, downloadFilePath string, headers map[string]string) bool {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	request, err := http.NewRequest(http.MethodGet, url, nil)

	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}

	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	response, err := client.Do(request)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}

		return false
	}

	if response.StatusCode != 200 {
		logger.ServiceLogger.Warn(url, " response status:", response.Status)
		return false
	}

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.PatchPoolingLogger.Error("Error download :", err.Error())
			err := os.Remove(downloadFilePath)
			if err != nil {
				logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
			}

		}
	}(response.Body)

	_, err2 := os.Stat(downloadFilePath)
	if !os.IsNotExist(err2) {
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
	}
	out, err := os.Create(downloadFilePath)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}
	defer func(out *os.File) {
		err := out.Close()
		if err != nil {
			logger.PatchPoolingLogger.Error("Error download :", err.Error())
			err := os.Remove(downloadFilePath)
			if err != nil {
				logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
			}
		}
	}(out)

	_, err = io.Copy(out, response.Body)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error download :", err.Error())
		err := os.Remove(downloadFilePath)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error removing downloaded file: "+downloadFilePath, err)
		}
		return false
	}

	return true
}
