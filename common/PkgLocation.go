package common

import (
	"errors"
	"fmt"
)

type PkgLocation int

const (
	LocalDir PkgLocation = iota + 1
	SharedDir
	PublicURL
)

func (pl PkgLocation) String() string {
	switch pl {
	case LocalDir:
		return "local_dir"
	case SharedDir:
		return "shared_dir"
	case PublicURL:
		return "public_url"
	default:
		return "Unknown"
	}
}

func (pl PkgLocation) ToPkgLocation(pkgLocation string) (PkgLocation, error) {
	switch pkgLocation {
	case "local_dir":
		return LocalDir, nil
	case "shared_dir":
		return SharedDir, nil
	case "public_url":
		return PublicURL, nil
	default:
		return PkgLocation(0), errors.New(fmt.Sprintf("invalid os type value '%s'", pkgLocation))
	}
}
