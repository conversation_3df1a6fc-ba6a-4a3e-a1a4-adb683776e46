package common

type OsType int

const (
	None OsType = iota + 1
	Linux
	MacOS
	Windows
	Ubuntu
	Redhat
)

func (os OsType) String() string {
	switch os {
	case Windows:
		return "windows"
	case Linux:
		return "linux"
	case Ubuntu:
		return "ubuntu"
	case MacOS:
		return "mac"
	case Redhat:
		return "redhat"
	case None:
		return "none"
	default:
		return "none"
	}
}

func (os OsType) ToOsType(osType string) OsType {
	switch osType {
	case "windows":
		return Windows
	case "linux":
		return Linux
	case "ubuntu":
		return Ubuntu
	case "mac":
		return MacOS
	case "redhat":
		return Redhat
	case "none":
		return None
	default:
		return None
	}
}
