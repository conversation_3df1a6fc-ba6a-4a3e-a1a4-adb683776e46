package common

import (
	"bytes"
	"crypto/aes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
)

const (
	secretKey = "GK@#$Ziro@#$AK@2019"
)

func Encrypt(plainText string) (string, error) {
	key := []byte("INETDATAENDPOINT")
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	blockSize := block.BlockSize()
	paddedPlainText := PKCS7Padding([]byte(plainText), blockSize)

	cipherText := make([]byte, len(paddedPlainText))
	for i := 0; i < len(paddedPlainText); i += blockSize {
		block.Encrypt(cipherText[i:i+blockSize], paddedPlainText[i:i+blockSize])
	}

	return base64.StdEncoding.EncodeToString(cipherText), nil
}

func Decrypt(cipherText string) string {
	result := ""
	if cipherText != "" {
		key := []byte("INETDATAENDPOINT")
		decodedCipherText, err := base64.StdEncoding.DecodeString(cipherText)
		if err != nil {
			return ""
		}

		block, err := aes.NewCipher(key)
		if err != nil {
			return ""
		}

		blockSize := block.BlockSize()
		plainText := make([]byte, len(decodedCipherText))
		for i := 0; i < len(decodedCipherText); i += blockSize {
			block.Decrypt(plainText[i:i+blockSize], decodedCipherText[i:i+blockSize])
		}

		plainText, err = PKCS7Unpadding(plainText)
		if err != nil {
			return ""
		}

		return string(plainText)
	}

	return result
}

func PKCS7Unpadding(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("invalid padding length")
	}
	padding := int(data[length-1])
	if padding > length || padding == 0 {
		return nil, errors.New("invalid padding")
	}
	for i := length - padding; i < length; i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("invalid padding")
		}
	}
	return data[:length-padding], nil
}

func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

func GenerateSignature(id string, expires int64) string {
	data := fmt.Sprintf("%s:%d", id, expires)
	mac := hmac.New(sha256.New, []byte(secretKey))
	mac.Write([]byte(data))
	return base64.RawURLEncoding.EncodeToString(mac.Sum(nil))
}
