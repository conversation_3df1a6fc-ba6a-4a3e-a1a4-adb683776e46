package common

type OsArchitecture int

const (
	x64 OsArchitecture = iota + 1
	x86
	amd64
	all
	arm64
	none
)

func (oa OsArchitecture) String() string {
	switch oa {
	case x64:
		return "x64"
	case x86:
		return "x86"
	case amd64:
		return "amd64"
	case none:
		return "none"
	case all:
		return "all"
	case arm64:
		return "arm64"
	default:
		return "none"
	}
}

func (oa OsArchitecture) ToOsArch(osArch string) OsArchitecture {
	switch osArch {
	case "x64":
		return x64
	case "x86":
		return x86
	case "amd64":
		return amd64
	case "none":
		return none
	case "all":
		return all
	case "arm64":
		return arm64
	default:
		return none
	}
}
