package constant

import "context"

var CallContext = context.Background() // Initialize global context

const (
	UserId         = "userId"
	User           = "user"
	UserDepartment = "userDepartment"
)

const (
	GET    = "1"
	CREATE = "2"
	DELETE = "3"
	MANAGE = "4"
	UPDATE = "5"
)

var (
	DefaultInstantDeploymentPolicyId = int64(0)
)

const (
	SnmpVer                      = "version"
	SnmpVerV3                    = "v3"
	CommunityString              = "community"
	Context                      = "context"
	Host                         = "host"
	Port                         = "port"
	Timeout                      = "timeout"
	RetryCount                   = "retry_count"
	Error                        = "error"
	Message                      = "message"
	ErrorMessageConnectionFailed = "Failed to establish %s connection on %s:%d"
)
