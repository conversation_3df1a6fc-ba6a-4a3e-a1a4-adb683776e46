package constant

var PatchScanQueries = map[string]string{
	"ubuntu": `{
					"cmd": {
							"ubuntu_installed_list" : "sudo apt list --installed"
					}
                }`,
	"darwin": `{
					"cmd" : {
						"mac_installed_list" : "system_profiler SPInstallHistoryDataType -json",
						"mac_os_version" : "sw_vers -productVersion"
					}
				}`,
	"windows": `{
					"reg":{
						"reg_patches" : "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Installer\\UserData",
						"edge_info_64" : "HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\EdgeUpdate\\Clients\\",
						"edge_info_32" : "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\EdgeUpdate\\Clients\\",
						"edge_info_64_hkcu" : "HKEY_CURRENT_USER\\SOFTWARE\\WOW6432Node\\Microsoft\\EdgeUpdate\\Clients\\",
						"edge_info_32_hkcu" : "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\EdgeUpdate\\Clients\\",
						"defender_policy_info" : "HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows Defender",
						"defender_info" : "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows Defender"
					},
					"cmd" : {
						"sysInfo" : "systeminfo",
						"mrtversion_info" : "wmic datafile where name=\"'C:\\\\Windows\\\\System32\\\\mrt.exe'\"  get Version /value"
					},
					"ps" : {
						"software_list" : "Get-Package | Select-Object -Property Name,Version | ConvertTo-Json",
						"mpcomputerstatus_info" : "Get-MpComputerStatus",
						"displayVersion" : "Get-ItemPropertyValue 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion' DisplayVersion"
					}
				}`,

	//						"mrtversion_info" : "wmic datafile where name=\"C:\\Windows\\System32\\mrt.exe\" get Version /value"
}

const (
	WindowsServer2022     = "Windows Server 2022"
	WindowsServer2025     = "Windows Server 2025"
	MicrosoftServerOs21H2 = "Microsoft Server operating system-21H2"
	MicrosoftServerOs24H2 = "Microsoft Server Operating System-24H2"
)

const (
	GroupNameWindows7            = "Windows 7"
	GroupNameWindows8            = "Windows 8"
	GroupNameWindows81           = "Windows 8.1"
	GroupNameWindows10           = "Windows 10"
	GroupNameWindows10Ltsb       = "Windows 10 LTSB"
	GroupNameWindows11           = "Windows 11"
	GroupNameWindows21H2         = "Windows 21H2"
	GroupNameWindows24H2         = "Windows 24H2"
	GroupNameWindowsServer2012   = "Windows Server 2012"
	GroupNameWindowsServer2012R2 = "Windows Server 2012 R2"
	GroupNameWindowsServer2016   = "Windows Server 2016"
	GroupNameWindowsServer2019   = "Windows Server 2019"
	GroupNameWindowsServer       = "Windows Server"
	GroupNameWindowsDefender     = "Microsoft Defender Antivirus"
	GroupNameWindowsEdge         = "Microsoft Edge"
	GroupNameAllOs               = "All OS"
	PkgNameVersionSeparator      = "@_@"
	PkgNameDistroSeparator       = "/"
)

const (
	WindowsEdgeApp       = "Microsoft Edge"
	WindowsDefenderApp   = "Microsoft Defender Antivirus"
	WindowsEdgeStableKey = "56EB18F8-B008-4CBD-B6D2-8C97FE7E9062"
	WindowsEdgeStableApp = "Microsoft Edge-Stable"
	WindowsEdgeBetaApp   = "Microsoft Edge-Beta"
	WindowsEdgeBetaKey   = "2CD8A007-E189-409D-A2C8-9AF4EF3C72AA"
	WindowsEdgeDevApp    = "Microsoft Edge-Dev"
	WindowsEdgeDevKey    = "0D50BFEC-CD6A-4F9A-964C-C7416E3ACB10"
	WindowsEdgeCanaryApp = "Microsoft Edge-Canary"
	WindowsEdgeCanaryKey = "65C35B14-6C1D-4122-AC46-7148CC9D6497"
)

const (
	PatchAllowedCategory = "DD78B8A1-0B20-45C1-ADD6-4DA72E9364CF,393789F5-61C1-4881-B5E7-C47BCCA90F94, D2085B71-5F1F-43A9-880D-ED159016D5C6,E4B04398-ADBD-4B69-93B9-477322331CD3,21210D67-50BC-4254-A695-281765E10665,F4B9C883-F4DB-4FB5-B204-3343C11FA021,B3C75DC1-155F-4BE4-B015-3F1A91758E52,6407468E-EDC7-4ECD-8C32-521F64CEE65E,D31BD4C3-D872-41C9-A2E7-231F372588CB,69010383-FF55-4227-BD6C-D91C26F2643B,BFE5B177-A086-47A0-B102-097E4FA1F807,18E5EA77-E3D1-43B6-A0A8-FA3DBCD42E93,	2EE2AD83-828C-4405-9479-544D767993FC,8C3FCC84-7410-4A95-8B89-A166A0190486,	589DB546-7849-47F5-BBC0-1F66CF12F5C2,569E8E8F-C6CD-42C8-92A3-EFBB20A0F6F5,	F702A48C-919B-45D6-9AEF-CA4248D50397,A105A108-7C9B-4518-BBBE-73F0FE30012B,72E7624A-5B00-45D2-B92F-E561C0A6A160,B6EA7C03-5339-4D45-A215-314A05FE37E0, 71718F13-7324-4B0F-8F9E-2CA9DC978E53"
)

var UbuntuVersionMap = map[string]string{
	"14.04": "trusty",
	"16.04": "xenial",
	"18.04": "bionic",
	"20.10": "groovy",
	"20.04": "focal",
	"21.10": "impish",
	"21.04": "hirsute",
	"23.04": "lunar",
	"22.10": "kinetic",
	"22.04": "jammy",
	"24.04": "noble",
	"24.10": "oracular",
	"25.04": "plucky",
}
