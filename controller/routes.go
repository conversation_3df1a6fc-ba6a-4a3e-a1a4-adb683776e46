package controller

import (
	"deployment/common"
	"deployment/constant"
	"deployment/handler"
	"deployment/middleware"
	"github.com/gorilla/mux"
	"net/http"
)

func Routes(route *mux.Router) {
	route.Use(middleware.AuthMiddleware, middleware.CommonMiddleware)

	pathPrefix := common.GetEnv("API_PATH_PREFIX", "/api")
	apiRoute := route.PathPrefix(pathPrefix).Subrouter()

	//Update cache
	serverPostOperationHandler := handler.GetServerPostOperationHandler()
	apiRoute.Methods(http.MethodPost).Path("/system/command").HandlerFunc(serverPostOperationHandler.ExecutePostOperation)

	//product, _ := cache.ProductCache.Get("product")
	//Agent API
	agentRouters(apiRoute)

	//Asset-Patch Handler and Patch Discovery related API
	assetPatchRouters(apiRoute)

	//Asset-Compliance API
	assetComplianceRouters(apiRoute)

	//File-upload API
	fileUploadRouters(apiRoute)

	//Package API
	packageRouters(apiRoute)

	//ThirdPartyPackage API
	thirdPartyPackageRouters(apiRoute)

	//File server config API
	fileServerConfigRouters(apiRoute)

	//Patch API
	patchRouters(apiRoute)

	//Asset-Patch-Relation API
	assetPatchRelationRouters(apiRoute)

	//Asset-Patch API
	assetPatchCveRelationRouters(apiRoute)

	//Patch Os Application API (Affected Products)
	patchApplicationRouters(apiRoute)

	//Patch Language API
	patchLanguageRouters(apiRoute)

	//DeploymentPolicy API
	deploymentPolicyRouters(apiRoute)

	//Deployment API
	deploymentRouters(apiRoute)

	//Asset-Configuration API
	assetConfigurationRouters(apiRoute)

	//Compliance API
	complianceRouters(apiRoute)

	//Audit API
	auditHandler := handler.NewAuditHandler()
	apiRoute.Methods(http.MethodGet).Path("/audit/search").HandlerFunc(auditHandler.GetAllAuditByEventModelHandler)

	//AgentTask API
	agentTaskRouters(apiRoute)

	agentTaskResultHandler := handler.NewAgentTaskResultHandler()
	apiRoute.Methods(http.MethodPost).Path("/agent/taskresult/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskResultHandler.GetAllTaskResultHandler)))

	//Deployment bundle API
	deploymentBundleRouters(apiRoute)

	// Create an instance of SystemActionHandler
	systemActionHandler := handler.NewSystemActionHandler()

	// System Action
	systemActionRouters(apiRoute, systemActionHandler)

	//Patch-Sync-Handler API
	patchSyncHandler := handler.NewPatchSyncHandler()
	apiRoute.Methods(http.MethodPost).Path("/execute-patch-sync").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchSyncHandler.ExecutePatchSync)))

	//Patch-Preference
	patchPreferenceHandler := handler.NewPatchPreferenceHandler()
	apiRoute.Methods(http.MethodGet).Path("/patch-preference").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchPreferenceHandler.GetPatchPreferenceHandler)))
	apiRoute.Methods(http.MethodPut).Path("/patch-preference").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchPreferenceHandler.UpdatePatchPreferenceHandler)))

	//Computer group api
	computerGroupRouters(apiRoute)

	//Patch decline policy  api
	patchDeclinePolicyRouters(apiRoute)

	//Auto Patch Deploy api
	autoPatchDeployRouters(apiRoute)

	//Auto Patch Test api
	autoPatchTestRouters(apiRoute)

}

func autoPatchTestRouters(apiRoute *mux.Router) {
	autoPatchTestHandler := handler.NewAutoPatchTestHandler()
	apiRoute.Methods(http.MethodGet).Path("/auto-patch-test/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchTestHandler.GetAutoPatchTestHandler)))
	apiRoute.Methods(http.MethodPost).Path("/auto-patch-test").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchTestHandler.CreateAutoPatchTestHandler)))
	apiRoute.Methods(http.MethodPut).Path("/auto-patch-test/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchTestHandler.UpdateAutoPatchTestHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/auto-patch-test/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchTestHandler.DeleteAutoPatchTestHandler)))
	apiRoute.Methods(http.MethodPost).Path("/auto-patch-test/search").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchTestHandler.GetAllAutoPatchTestsHandler)))
}

func autoPatchDeployRouters(apiRoute *mux.Router) {
	autoPatchDeployHandler := handler.NewAutoPatchDeployHandler()
	apiRoute.Methods(http.MethodGet).Path("/auto-patch-deploy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchDeployHandler.GetAutoPatchDeployHandler)))
	apiRoute.Methods(http.MethodPost).Path("/auto-patch-deploy").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchDeployHandler.CreateAutoPatchDeployHandler)))
	apiRoute.Methods(http.MethodPut).Path("/auto-patch-deploy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchDeployHandler.UpdateAutoPatchDeployHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/auto-patch-deploy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchDeployHandler.DeleteAutoPatchDeployHandler)))
	apiRoute.Methods(http.MethodPost).Path("/auto-patch-deploy/search").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(autoPatchDeployHandler.GetAllAutoPatchDeploysHandler)))
}

func patchDeclinePolicyRouters(apiRoute *mux.Router) {
	patchDeclinePolicyHandler := handler.NewPatchDeclinePolicyHandler()
	apiRoute.Methods(http.MethodGet).Path("/patch-decline-policy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchDeclinePolicyHandler.GetPatchDeclinePolicyHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch-decline-policy").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchDeclinePolicyHandler.CreatePatchDeclinePolicyHandler)))
	apiRoute.Methods(http.MethodPut).Path("/patch-decline-policy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchDeclinePolicyHandler.UpdatePatchDeclinePolicyHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/patch-decline-policy/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchDeclinePolicyHandler.DeletePatchDeclinePolicyHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch-decline-policy/search").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(patchDeclinePolicyHandler.GetAllPatchDeclinePoliciesHandler)))
}

func computerGroupRouters(apiRoute *mux.Router) {
	computerGroupHandler := handler.NewComputerGroupHandler()
	apiRoute.Methods(http.MethodGet).Path("/computer-group/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(computerGroupHandler.GetComputerGroupHandler)))
	apiRoute.Methods(http.MethodPost).Path("/computer-group").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(computerGroupHandler.CreateComputerGroupHandler)))
	apiRoute.Methods(http.MethodPut).Path("/computer-group/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(computerGroupHandler.UpdateComputerGroupHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/computer-group/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(computerGroupHandler.DeleteComputerGroupHandler)))
	apiRoute.Methods(http.MethodPost).Path("/computer-group/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(computerGroupHandler.GetAllComputerGroupsHandler)))
}

func systemActionRouters(apiRoute *mux.Router, systemActionHandler *handler.SystemActionHandler) {
	apiRoute.Methods(http.MethodGet).Path("/systemAction/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(systemActionHandler.GetSystemActionHandler)))
	apiRoute.Methods(http.MethodPost).Path("/systemAction").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(systemActionHandler.CreateSystemActionHandler)))
	apiRoute.Methods(http.MethodPut).Path("/systemAction/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(systemActionHandler.UpdateSystemActionHandler)))
	//apiRoute.Methods(http.MethodDelete).Path("/systemAction/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(systemActionHandler.DeleteSystemActionHandler)))
	apiRoute.Methods(http.MethodPost).Path("/systemAction/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(systemActionHandler.GetAllSystemActionsHandler)))
}

func deploymentBundleRouters(apiRoute *mux.Router) {
	bundleHandler := handler.NewDeploymentBundleHandler()
	apiRoute.Methods(http.MethodGet).Path("/bundle/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.GetHandler)))
	apiRoute.Methods(http.MethodPost).Path("/bundle").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.CreateHandler)))
	apiRoute.Methods(http.MethodPut).Path("/bundle/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.UpdateHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/bundle/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.DeleteHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/bundle").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.BulkDeleteHandler)))
	apiRoute.Methods(http.MethodPost).Path("/bundle/search").Handler(middleware.ValidatePermission(common.DEPLOYMENT_BUNDLE, constant.MANAGE, http.HandlerFunc(bundleHandler.GetAllHandler)))
}

func agentTaskRouters(apiRoute *mux.Router) {
	agentTaskHandler := handler.NewAgentTaskHandler()
	apiRoute.Methods(http.MethodGet).Path("/agent/task/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.GetAgentTaskHandler)))
	apiRoute.Methods(http.MethodGet).Path("/agent/task-details/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.GetAgentTask)))
	apiRoute.Methods(http.MethodPost).Path("/agent/task/create").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.CreateAgentTaskHandler)))
	apiRoute.Methods(http.MethodPut).Path("/agent/task/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.UpdateAgentTaskHandler)))
	apiRoute.Methods(http.MethodPut).Path("/agent/task/bulk/update").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.BulkUpdateAgentTaskHandler)))
	apiRoute.Methods(http.MethodGet).Path("/list/agent-task").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.GetAllAgentTasksHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/task/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.GetAgentTaskByDeploymentIdsHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/task-details/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentTaskHandler.GetAllAgentTasksFromViewHandler)))
}

func complianceRouters(apiRoute *mux.Router) {
	complianceHandler := handler.NewComplianceHandler()
	apiRoute.Methods(http.MethodGet).Path("/compliance/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(complianceHandler.GetCompliance)))
	apiRoute.Methods(http.MethodPost).Path("/compliance").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.CREATE, http.HandlerFunc(complianceHandler.CreateCompliance)))
	apiRoute.Methods(http.MethodPut).Path("/compliance/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.UPDATE, http.HandlerFunc(complianceHandler.UpdateCompliance)))
	apiRoute.Methods(http.MethodDelete).Path("/compliance/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.DELETE, http.HandlerFunc(complianceHandler.DeleteCompliance)))
	apiRoute.Methods(http.MethodDelete).Path("/compliance").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.DELETE, http.HandlerFunc(complianceHandler.BulkDeleteCompliance)))
	apiRoute.Methods(http.MethodPost).Path("/compliance/search").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(complianceHandler.GetAllCompliance)))
	apiRoute.Methods(http.MethodPost).Path("/compliance/test/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(complianceHandler.TestCompliance)))
}

func assetConfigurationRouters(apiRoute *mux.Router) {
	configurationHandler := handler.NewConfigurationHandler()
	apiRoute.Methods(http.MethodGet).Path("/asset/configuration/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(configurationHandler.GetConfigurationHandler)))
	apiRoute.Methods(http.MethodPost).Path("/asset/configuration").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.CREATE, http.HandlerFunc(configurationHandler.CreateConfigurationHandler)))
	apiRoute.Methods(http.MethodPut).Path("/asset/configuration/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.UPDATE, http.HandlerFunc(configurationHandler.UpdateConfigurationHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/asset/configuration/{id}").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.DELETE, http.HandlerFunc(configurationHandler.DeleteConfigurationHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/asset/configuration").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.DELETE, http.HandlerFunc(configurationHandler.BulkDeleteConfigurationHandler)))
	apiRoute.Methods(http.MethodPost).Path("/asset/configuration/search").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(configurationHandler.GetAllConfigurationHandler)))
	apiRoute.Methods(http.MethodGet).Path("/asset/configuration/tags/filter").Handler(middleware.ValidatePermission(common.CONFIGURATION, constant.GET, http.HandlerFunc(configurationHandler.GetTagFilterHandler)))
}

func deploymentRouters(apiRoute *mux.Router) {
	deploymentHandler := handler.NewDeploymentHandler()
	apiRoute.Methods(http.MethodGet).Path("/deployment/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.GetDeploymentHandler)))
	apiRoute.Methods(http.MethodPost).Path("/deployment/heatmap/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.GetDeploymentHitMapHandler)))
	apiRoute.Methods(http.MethodPost).Path("/deployment").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.CreateDeploymentHandler)))
	apiRoute.Methods(http.MethodPut).Path("/deployment/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.UpdateDeploymentHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/deployment/{id}").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.DeleteDeploymentHandler)))
	apiRoute.Methods(http.MethodPost).Path("/deployment/search").Handler(middleware.ValidatePermission(common.DEPLOYMENT, constant.MANAGE, http.HandlerFunc(deploymentHandler.GetAllDeploymentHandler)))
}

func deploymentPolicyRouters(apiRoute *mux.Router) {
	deploymentPolicyHandler := handler.NewDeploymentPolicyHandler()
	apiRoute.Methods(http.MethodGet).Path("/policy/{id}").HandlerFunc(deploymentPolicyHandler.GetDeploymentPolicyHandler)
	apiRoute.Methods(http.MethodPost).Path("/policy").HandlerFunc(deploymentPolicyHandler.CreateDeploymentPolicyHandler)
	apiRoute.Methods(http.MethodPut).Path("/policy/{id}").HandlerFunc(deploymentPolicyHandler.UpdateDeploymentPolicyHandler)
	apiRoute.Methods(http.MethodDelete).Path("/policy/{id}").HandlerFunc(deploymentPolicyHandler.DeleteDeploymentPolicyHandler)
	apiRoute.Methods(http.MethodPost).Path("/policy/search").HandlerFunc(deploymentPolicyHandler.GetAllDeploymentPolicyHandler)
}

func patchLanguageRouters(apiRoute *mux.Router) {
	patchLanguageHandler := handler.NewPatchLanguageHandler()
	apiRoute.Methods(http.MethodPost).Path("/patch-language/search").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchLanguageHandler.GetAllPatchLanguageHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch-language").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchLanguageHandler.CreateHandler)))
}

func patchApplicationRouters(apiRoute *mux.Router) {
	patchOsApplicationHandler := handler.NewPatchOsApplicationHandler()
	apiRoute.Methods(http.MethodPost).Path("/patch-affected-products").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(patchOsApplicationHandler.CreateHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch-affected-products/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(patchOsApplicationHandler.GetAllPatchOsApplication)))
}

func assetPatchCveRelationRouters(apiRoute *mux.Router) {
	assetPatchCveRelationHandler := handler.NewAssetPatchCveRelationHandler()
	apiRoute.Methods(http.MethodPost).Path("/asset-patch-cve-relation/search").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchCveRelationHandler.GetAll)))
	apiRoute.Methods(http.MethodGet).Path("/asset-patch-cve-relation/{cve}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchCveRelationHandler.GetByCVE)))
}

func assetPatchRelationRouters(apiRoute *mux.Router) {
	assetPatchRelationHandler := handler.NewAssetPatchRelationHandler()
	apiRoute.Methods(http.MethodPost).Path("/asset-patch-relation").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchRelationHandler.CreateAssetPatchRelationHandler)))
	apiRoute.Methods(http.MethodPut).Path("/asset-patch-relation/exception").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchRelationHandler.AddPatchExceptionHandler)))
	apiRoute.Methods(http.MethodPost).Path("/asset-patch-relation/search").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchRelationHandler.GetAllAssetPatchRelation)))
	apiRoute.Methods(http.MethodPost).Path("/asset-patch-relation/{patch_state}/{asset_id}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchRelationHandler.GetAllAssetPatchRelationByAssetIdAndPatchState)))
	apiRoute.Methods(http.MethodDelete).Path("/asset-patch-relation/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(assetPatchRelationHandler.DeleteAssetPatchRelation)))
}

func patchRouters(apiRoute *mux.Router) {
	patchHandler := handler.NewPatchHandler()
	apiRoute.Methods(http.MethodGet).Path("/patch/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchHandler.GetPatchHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch").Handler(middleware.ValidatePermission(common.PATCH, constant.CREATE, http.HandlerFunc(patchHandler.CreatePatchHandler)))
	apiRoute.Methods(http.MethodPut).Path("/patch/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.UPDATE, http.HandlerFunc(patchHandler.UpdatePatchHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/patch/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.DELETE, http.HandlerFunc(patchHandler.DeletePatchHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/patch").Handler(middleware.ValidatePermission(common.PATCH, constant.DELETE, http.HandlerFunc(patchHandler.BulkDeletePatchHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch/search").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchHandler.GetAllPatchHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch/{patch_state}/search").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchHandler.GetAllPatchByPatchStateHandler)))
	apiRoute.Methods(http.MethodGet).Path("/patch/superseded/{uuid}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchHandler.GetSupersededPatchHandler)))
	apiRoute.Methods(http.MethodGet).Path("/patch/superseded-by/{uuid}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(patchHandler.GetSupersededByPatchHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/delete-all/pch-relation").Handler(middleware.ValidatePermission(common.PATCH, constant.DELETE, http.HandlerFunc(patchHandler.DeleteAllPatchWithRelationHandler)))
	apiRoute.Methods(http.MethodPost).Path("/patch/download-status/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.UPDATE, http.HandlerFunc(patchHandler.UpdateDownloadStatus)))
}

func fileServerConfigRouters(apiRoute *mux.Router) {
	fileServerConfigHandler := handler.NewFileServerConfigHandler()
	apiRoute.Methods(http.MethodGet).Path("/file-server-config/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.GET, http.HandlerFunc(fileServerConfigHandler.GetFileServerConfigHandler)))
	apiRoute.Methods(http.MethodPost).Path("/file-server-config").Handler(middleware.ValidatePermission(common.PACKAGE, constant.CREATE, http.HandlerFunc(fileServerConfigHandler.CreateFileServerConfigHandler)))
	apiRoute.Methods(http.MethodPut).Path("/file-server-config/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.UPDATE, http.HandlerFunc(fileServerConfigHandler.UpdateFileServerConfigHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/file-server-config/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.DELETE, http.HandlerFunc(fileServerConfigHandler.DeleteFileServerConfigHandler)))
	apiRoute.Methods(http.MethodPost).Path("/file-server-config/search").Handler(middleware.ValidatePermission(common.PACKAGE, constant.GET, http.HandlerFunc(fileServerConfigHandler.GetAllFileServerConfigHandler)))
}

func packageRouters(apiRoute *mux.Router) {
	packageHandler := handler.NewPackageHandler()
	apiRoute.Methods(http.MethodGet).Path("/package/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.GET, http.HandlerFunc(packageHandler.GetPackageHandler)))
	apiRoute.Methods(http.MethodPost).Path("/package").Handler(middleware.ValidatePermission(common.PACKAGE, constant.CREATE, http.HandlerFunc(packageHandler.CreatePackageHandler)))
	apiRoute.Methods(http.MethodPut).Path("/package/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.UPDATE, http.HandlerFunc(packageHandler.UpdatePackageHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/package/{id}").Handler(middleware.ValidatePermission(common.PACKAGE, constant.DELETE, http.HandlerFunc(packageHandler.DeletePackageHandler)))
	apiRoute.Methods(http.MethodDelete).Path("/package").Handler(middleware.ValidatePermission(common.PACKAGE, constant.CREATE, http.HandlerFunc(packageHandler.BulkDeletePackageHandler)))
	apiRoute.Methods(http.MethodPost).Path("/package/search").Handler(middleware.ValidatePermission(common.PACKAGE, constant.GET, http.HandlerFunc(packageHandler.GetAllPackageHandler)))
	apiRoute.Methods(http.MethodGet).Path("/package/tags/filter").Handler(middleware.ValidatePermission(common.PACKAGE, constant.GET, http.HandlerFunc(packageHandler.GetTagFilterHandler)))
}

func thirdPartyPackageRouters(apiRoute *mux.Router) {
	thirdPartyPackageHandler := handler.NewThirdPartyPackageHandler()
	apiRoute.Methods(http.MethodGet).Path("/third-party-package/metadata/{osType}").Handler(middleware.ValidatePermission(common.PATCH, constant.GET, http.HandlerFunc(thirdPartyPackageHandler.GetThirdPartyPackageMetadataHandler)))
}

func fileUploadRouters(apiRoute *mux.Router) {
	apiRoute.Methods(http.MethodPost).Path("/upload").HandlerFunc(handler.UploadFile)
	apiRoute.Methods(http.MethodPost).Path("/upload/patch/{id}").HandlerFunc(handler.UploadPatchFile)
	apiRoute.Methods(http.MethodPost).Path("/generate").HandlerFunc(handler.GenerateScriptFile)
	apiRoute.Methods(http.MethodGet).Path("/download/{filename}").HandlerFunc(handler.DownloadFile)
	apiRoute.Methods(http.MethodGet).Path("/download/patchdb/{filename}").HandlerFunc(handler.DownloadPatchDbFile)
	apiRoute.Methods(http.MethodGet).Path("/download/patch/{filename}").HandlerFunc(handler.DownloadPatchFile)
	apiRoute.Methods(http.MethodGet).Path("/log/download").HandlerFunc(handler.DownloadLogFile)
	apiRoute.Methods(http.MethodGet).Path("/public/preview/{filename}").HandlerFunc(handler.PreviewFile)
	apiRoute.Methods(http.MethodGet).Path("/file/{filename}").HandlerFunc(handler.GetFileData)

	apiRoute.Methods(http.MethodGet).Path("/generate-signed-url").HandlerFunc(handler.GenerateSignedUrlHandler)

}

func assetComplianceRouters(apiRoute *mux.Router) {
	assetHandler := handler.NewAssetHandler()
	apiRoute.Methods(http.MethodPost).Path("/asset/compliance/heatmap/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(assetHandler.GetAssetComplianceHeatMapHandler)))
	apiRoute.Methods(http.MethodPost).Path("/asset/compliance/history/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(assetHandler.GetAssetComplianceHistoryHandler)))
	apiRoute.Methods(http.MethodPost).Path("/asset/quick/checks/{id}").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(assetHandler.TestQuickCheck)))
}

func assetPatchRouters(apiRoute *mux.Router) {
	assetPatchHandler := handler.NewAssetPatchHandler()
	apiRoute.Methods(http.MethodPost).Path("/asset/execute-scan-patch/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.CreateAssetPatchScanCommand)))
	apiRoute.Methods(http.MethodPost).Path("/asset/bulk/execute-scan-patch").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.CreateBulkAssetPatchScanCommand)))
	apiRoute.Methods(http.MethodGet).Path("/asset/execute-scan-patch/history/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.GetAssetPatchScanCommandHistory)))
	apiRoute.Methods(http.MethodPost).Path("/asset/get-all-pending-scan-patch-task/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.GetAllPendingPatchScanCommandByAssetId)))
	apiRoute.Methods(http.MethodPost).Path("/asset/discovered-patch-data/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.ProcessDiscoveredPatchData)))
	apiRoute.Methods(http.MethodPost).Path("/asset/remove-superseded-patch").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.ProcessRemoveSupersededPatch)))

	apiRoute.Methods(http.MethodPost).Path("/asset/applicability-rule-filemap/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.GetAllApplicabilityRuleFileList)))
	apiRoute.Methods(http.MethodGet).Path("/asset/download-applicability-rule-file/{filename}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.DownloadApplicabilityRuleFileByFileName)))
	apiRoute.Methods(http.MethodGet).Path("/asset/download-cab-file/{filename}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.DownloadCabFileByFileName)))
	apiRoute.Methods(http.MethodPost).Path("/asset/linux/installed-patches/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.ProcessLinuxInstalledPatchData)))
	apiRoute.Methods(http.MethodGet).Path("/asset/linux/prepare/latestPackageInfo/{id}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.GetLatestPackageInfoByInstalledList)))
	apiRoute.Methods(http.MethodGet).Path("/asset/macos/download-dist/{osVersion}").Handler(middleware.ValidatePermission(common.PATCH, constant.MANAGE, http.HandlerFunc(assetPatchHandler.DownloadMacDistFiles)))
}

func agentRouters(apiRoute *mux.Router) {
	agentHandler := handler.GetAgentHandler()
	apiRoute.Methods(http.MethodPost).Path("/agent/refresh").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentRefreshHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/v2/refresh").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentRefreshHandlerV2)))
	apiRoute.Methods(http.MethodPost).Path("/agent/system-action/refresh").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentSystemActionRefreshHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/resolve/taskDetails").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentTaskResolverHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/search/byUUID").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentSearchHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentSearchByScope)))
	apiRoute.Methods(http.MethodPost).Path("/agent/selfservice/deployment").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentSelfServiceDeploymentHandler)))
	apiRoute.Methods(http.MethodPost).Path("/agent/selfservice/deployment/search").Handler(middleware.ValidatePermission(common.AGENT, constant.MANAGE, http.HandlerFunc(agentHandler.AgentSelfServiceDeploymentSearchHandler)))
}
