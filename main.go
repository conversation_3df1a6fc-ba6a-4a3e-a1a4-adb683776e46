package main

import (
	"deployment/common"
	"deployment/db"
	"deployment/handler"
	"deployment/logger"
	"deployment/server"
	"deployment/service"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/joho/godotenv"
	"github.com/uptrace/bun"
	"os"
	"strings"
	"time"
)

func main() {
	channel := make(chan struct{})
	workingDir := common.CurrentWorkingDir()
	LoadConfigFile(workingDir)
	logDir := common.PrepareFilePath(workingDir, common.GetEnv("LOG_DIR", "logs"))
	logLevel := common.GetEnv("LOG_LEVEL", "info")
	logMaxSize := common.GetEnvNumeric("LOG_MAX_SIZE", 10)
	logMaxAge := common.GetEnvNumeric("LOG_MAX_AGE", 30)
	logger.ConfigLogger(logDir, logLevel, logMaxSize, logMaxAge)
	logger.ServiceLogger.Info("starting server...")
	logger.ServiceLogger.Info("initializing database connection...")
	_, err := db.Connect()
	if err != nil {
		panic(err)
	}
	logger.ServiceLogger.Info("database connection successfully initialized...")
	err = checkMainServerAvailability()
	if err != nil {
		panic(err)
	}
	logger.ServiceLogger.Info("main server connected successfully...")
	if service.NewLicenseService().ValidLicense() {
		logger.ServiceLogger.Info("processing system on board activities...")
		handler.SystemOnBoardService()
		logger.ServiceLogger.Info("system on board activities completed successfully...")
		tlsServer := server.NewServer()
		err = tlsServer.ListenAndServe()
		if err != nil {
			panic(err)
		}

		defer tlsServer.Shutdown()

		defer func(Connection *bun.DB) {
			err := Connection.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}(db.Connection)
		logger.ServiceLogger.Info("server started...")
		<-channel
	} else {
		panic("invalid license")
	}
}

func LoadConfigFile(workingDir string) {
	if strings.Contains(workingDir, "zirozen") {
		os.Setenv("profile", "prod")
		err := godotenv.Load(common.PrepareFilePath(workingDir, "app.config"))
		if err != nil {
			fmt.Printf("Error loading app.config file %s", err.Error())
		}
	} else {
		os.Setenv("profile", "dev")
		err := godotenv.Load(common.PrepareFilePath(workingDir, "app1.config"))
		if err != nil {
			fmt.Printf("Error loading app1.config file %s", err.Error())
		}
	}
}

func checkMainServerAvailability() error {
	delay := 10 * time.Second
	maxRetries := 10
	for attempt := 1; attempt <= maxRetries; attempt++ {
		var payload []byte
		url := common.MainServerUrl()
		tokenUrl := url + "/api/token"
		credential := map[string]string{"username": "zirozen", "password": "Zir@zen2@24"}
		payload, _ = json.Marshal(credential)
		response, success := common.ExecutePostRequest(tokenUrl, payload, nil)
		if success && response != nil && len(response) > 0 {
			return nil
		}
		if !success {
			logger.ServiceLogger.Info(fmt.Sprintf("Attempt %d: error reaching main service", attempt))
		}

		if attempt < maxRetries {
			time.Sleep(delay)
			delay *= 2 // Exponential backoff
		}
	}

	return errors.New("main service is not available after retries")
}
