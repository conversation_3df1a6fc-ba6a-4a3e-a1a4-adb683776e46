package plugin

import (
	"deployment/client"
	"deployment/constant"
	"deployment/logger"
	"encoding/json"
	"fmt"
	"math"
	"reflect"
	"strconv"
	"strings"
	"sync"
)

var mu sync.Mutex

func CollectUsingSNMP(context client.SNMPContext) map[string]interface{} {
	mu.Lock()
	snmpClient := &client.SNMPClient{}
	host := context.GetStringValue("host")
	snmpClient.SetSNMPParameters(context)
	discoveryResult := make(map[string]interface{})
	if snmpClient.Init() {
		discoveryResult["status"] = "success"
		logger.DiscoveryLogger.Debug("SNMP Client initialize successfully for " + host)
		logger.DiscoveryLogger.Debug("Getting system info for " + host)
		metrics, _ := snmpClient.GetScalarMetrics(getSystemOIDs())
		if len(metrics) > 0 {
			jsonData, err := json.Marshal(&metrics)
			logger.DiscoveryLogger.Debug("System info retrieved for " + host)
			if err != nil {
				logger.DiscoveryLogger.Error("Error while parsing System info for " + host + " " + err.Error())
			}
			discoveryResult["metrics"] = string(jsonData)
		} else {
			logger.DiscoveryLogger.Debug("failed to retrieve system info for " + host)
		}

		logger.DiscoveryLogger.Debug("Getting system info for " + host)
		result, _ := snmpClient.GetSystemInformationResult()
		jsonData, err := json.Marshal(&result)
		if err != nil {
			logger.DiscoveryLogger.Error("Error while parsing System info for " + host + " " + err.Error())
		}
		discoveryResult["result"] = string(jsonData)

		interfacemetrics := snmpClient.GetInterfaceData(getInterfaceOID())
		if interfacemetrics != nil && len(interfacemetrics) > 0 {
			logger.DiscoveryLogger.Debug("Interface data retrieved for " + host)
			interfaceList := make([]interface{}, 0)
			interfaceObj := make(map[string]interface{})
			valueData := interfacemetrics["interface-index"]
			endLen := len(valueData)
			counter := 0
			interfaceKeys := getInterfaceKeyWithJsonName()
			for counter < endLen {
				for key, jsonVal := range interfaceKeys {
					keyData := interfacemetrics[key]
					if jsonVal == "description" {
						strData := fmt.Sprintf("%s", keyData[counter])
						interfaceObj[jsonVal] = strData
					} else if jsonVal == "interface_index" {
						interfaceObj[jsonVal] = fmt.Sprint(keyData[counter])
					} else if jsonVal == "alias" {
						interfaceObj[jsonVal] = fmt.Sprintf("%s", keyData[counter])
					} else if jsonVal == "mac_address" {
						strData := fmt.Sprint(keyData[counter])
						interfaceObj[jsonVal] = strings.ToLower(strData)
					} else if jsonVal == "speed" {
						x := ToFloat(keyData[counter]) / 1000000
						interfaceObj[jsonVal] = math.Round(x*100) / 100
					} else if jsonVal == "admin_status" {
						strData := fmt.Sprint(keyData[counter])
						interfaceObj[jsonVal] = getStatusString(strData)
					} else if jsonVal == "operational_state" {
						strData := fmt.Sprint(keyData[counter])
						interfaceObj[jsonVal] = getStatusString(strData)
					} else if jsonVal == "interface_type" {
						strData := fmt.Sprint(keyData[counter])
						output, _ := strconv.ParseInt(strings.TrimSpace(strData), 10, 64)
						intType := int(output)
						intMap := getInterfaceTypeMap()
						if _, found := intMap[intType]; found {
							interfaceObj[jsonVal] = intMap[intType]
						} else {
							interfaceObj[jsonVal] = intType
						}
					}
				}
				interfaceList = append(interfaceList, interfaceObj)
				interfaceObj = make(map[string]interface{})
				counter++
			}
			jsonData, err := json.Marshal(&interfaceList)
			if err != nil {
				logger.DiscoveryLogger.Error("Error while parsing Interface data for " + host + " " + err.Error())
			}
			discoveryResult["interfaceList"] = string(jsonData)
		} else {
			logger.DiscoveryLogger.Debug("failed to retrieve Interface data for " + host)
		}
	} else {
		for _, errors := range snmpClient.GetErrors() {
			for k, v := range errors {
				discoveryResult[k] = v
			}
		}
		discoveryResult["status"] = "failed"
		discoveryResult["message"] = fmt.Sprintf(constant.ErrorMessageConnectionFailed, "SNMP", context[constant.Host].(string), context[constant.Port].(int))
		logger.DiscoveryLogger.Debug("Failed to initialize SNMP Client " + host)
	}
	snmpClient.Destroy()
	mu.Unlock()
	return discoveryResult
}

func ToFloat(value interface{}) (output float64) {
	if reflect.ValueOf(value).Kind().String() == "float32" {
		output = float64(value.(float32))
	} else if reflect.ValueOf(value).Kind().String() == "float64" {
		output = value.(float64)
	} else if reflect.ValueOf(value).Kind().String() == "uint" {
		output = float64(value.(uint))
	}
	return
}

func getStatusString(str string) (result string) {
	result = "unknown"
	output, _ := strconv.ParseInt(strings.TrimSpace(str), 10, 64)
	switch int(output) {
	case 1:
		result = "up"
		break
	case 2:
		result = "down"
		break
	case 3:
		result = "testing"
		break
	case 4:
		result = "unknown"
		break
	case 5:
		result = "dormant"
		break
	case 6:
		result = "notPresent"
		break
	}
	return
}

func getInterfaceTypeMap() map[int]string {

	return map[int]string{
		1:  "other",
		2:  "regular1822",
		3:  "hdh1822",
		4:  "ddn-x25",
		5:  "rfc877-x25",
		6:  "ethernet-csmacd",
		7:  "iso88023-csmacd",
		8:  "iso88024-tokenBus",
		9:  "iso88025-tokenRing",
		10: "iso88026-man",
		11: "starLan",
		12: "proteon-10Mbit",
		13: "proteon-80Mbit",
		14: "hyperchannel",
		15: "fddi",
		16: "lapb",
		17: "sdlc",
		18: "ds1",
		19: "e1",
		20: "basicISDN",
		21: "primaryISDN",
		22: "propPointToPointSerial",
		23: "ppp",
		24: "softwareLoopback",
		25: "eon",
		26: "ethernet-3Mbit",
		27: "nsip",
		28: "slip",
		29: "ultra",
		30: "ds3",
		31: "sip",
		32: "frae-relay",
	}
}

func getSystemOIDs() map[string]string {

	return map[string]string{
		".*******.*******.0":      "system_oid",
		".*******.********.1.3.0": "uptime_sec",
		".*******.*******.0":      "uptime",
		".*******.*******.0":      "object_name",
		".*******.*******.0":      "system_location",
	}
}

func getInterfaceOID() map[string]string {

	return map[string]string{
		".*******.*******.1.1":     "interface-index",
		".*******.*******.1.3":     "interface-type",
		".*******.*******.1.2":     "interface-description",
		".*******.********.1.1.18": "interface-alias",
		".*******.*******.1.5":     "interface-speed",
		".*******.*******.1.6":     "interface-address",
		".*******.*******.1.7":     "interface-admin-status",
		".*******.*******.1.8":     "interface-operational-status",
		/*".*******.*******.1.10":    "received-octets",
		".*******.*******.1.11":    "interface-in-packets",
		".*******.*******.1.13":    "received-discard-packets",
		".*******.*******.1.14":    "received-error-packets",
		".*******.*******.1.16":    "sent-octets",
		".*******.*******.1.17":    "interface-out-packets",
		".*******.*******.1.19":    "sent-discard-packets",
		".*******.*******.1.20":    "sent-error-packets",*/
	}
}

func getInterfaceKeyWithJsonName() map[string]string {
	return map[string]string{
		"interface-index":              "interface_index",
		"interface-type":               "interface_type",
		"interface-description":        "description",
		"interface-alias":              "alias",
		"interface-speed":              "speed",
		"interface-address":            "mac_address",
		"interface-admin-status":       "admin_status",
		"interface-operational-status": "operational_state",
		/*"received-octets":              "received-octets",
		"interface-in-packets":         "interface-in-packets",
		"received-discard-packets":     "received-discard-packets",
		"received-error-packets":       "received-error-packets",
		"sent-octets":                  "sent-octets",
		"interface-out-packets":        "interface-out-packets",
		"sent-discard-packets":         "sent-discard-packets",
		"sent-error-packets":           "sent-error-packets",*/
	}
}
