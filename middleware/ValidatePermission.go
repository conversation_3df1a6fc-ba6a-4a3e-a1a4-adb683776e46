package middleware

import (
	"deployment/cache"
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"fmt"
	"net/http"
	"os"
	"strings"
)

func ValidatePermission(module common.EntityManageModel, action string, next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !strings.Contains(os.Getenv("profile"), "dev") {

			var permissionList []string
			if common.PACKAGE == module {
				permissionList = append(permissionList, "14_"+action)
			} else if common.CONFIGURATION == module {
				permissionList = append(permissionList, "15_"+action)
			} else if common.DEPLOYMENT == module || common.DEPLOYMENT_BUNDLE == module || common.AGENT == module {
				permissionList = append(permissionList, "14_"+action)
				permissionList = append(permissionList, "15_"+action)
				permissionList = append(permissionList, "16_"+action)
			} else if common.PATCH == module {
				permissionList = append(permissionList, "16_"+action)
			} else {
				w.WriteHeader(http.StatusForbidden)
				_, err := fmt.Fprint(w, "Not enough permission")
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
				return
			}

			isValid := false
			userId := constant.CallContext.Value(constant.UserId)
			permissions, exist := cache.UserPermissionCache.Get(userId.(int64))
			if exist {
				for _, permission := range permissionList {
					if strings.Contains(permissions, permission) {
						isValid = true
						break
					}
				}
			}

			if !isValid {
				w.WriteHeader(http.StatusForbidden)
				_, err := fmt.Fprint(w, "Not enough permission")
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
				return
			}
		}
		next.ServeHTTP(w, r)
	})
}
