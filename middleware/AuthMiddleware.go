package middleware

import (
	"context"
	"crypto/rsa"
	"deployment/cache"
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"fmt"
	"github.com/golang-jwt/jwt"
	"github.com/gorilla/mux"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
)

var privateKey interface{}
var publicKey interface{}

func AuthMiddleware(next http.Handler) http.Handler {

	if privateKey == nil || publicKey == nil {
		privateKey = LoadPrivateKey(common.CurrentWorkingDir() + "/server.key")
		publicKey = LoadPublicKey(common.CurrentWorkingDir() + "/server-pub.key")
	}

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		if !(strings.Contains(r.URL.Path, "/login") || strings.Contains(r.URL.Path, "/public")) {
			if isDownloadEndpoint(r.URL.Path) && isSignedUrlRequest(r) {
				if !validateSignedUrlAuth(w, r) {
					return
				}
			} else {
				tokenString := r.Header.Get("Authorization")
				if tokenString == "" {
					w.WriteHeader(http.StatusUnauthorized)
					_, err := fmt.Fprint(w, "Missing authorization header")
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
					return
				}
				tokenString = tokenString[len("Bearer "):]

				err := verifyToken(tokenString)
				if err != nil {
					w.WriteHeader(http.StatusUnauthorized)
					_, err := fmt.Fprint(w, "Invalid token")
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
					return
				}
			}
		}
		next.ServeHTTP(w, r)
	})
}

func LoadPrivateKey(path string) *rsa.PrivateKey {
	keyBytes, err := os.ReadFile(path)
	if err != nil {
		log.Fatalf("Error reading private key: %v", err)
	}

	key, err := jwt.ParseRSAPrivateKeyFromPEM(keyBytes)
	if err != nil {
		log.Fatalf("Error parsing private key: %v", err)
	}

	return key
}

func LoadPublicKey(path string) *rsa.PublicKey {
	keyBytes, err := os.ReadFile(path)
	if err != nil {
		log.Fatalf("Error reading public key: %v", err)
	}

	key, err := jwt.ParseRSAPublicKeyFromPEM(keyBytes)
	if err != nil {
		log.Fatalf("Error parsing public key: %v", err)
	}

	return key
}

func verifyToken(tokenString string) error {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate the algorithm
		if token.Method.Alg() != jwt.SigningMethodRS512.Alg() {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		// Return the public key for verification
		return publicKey, nil
	})

	if err != nil {
		return err
	}

	if !token.Valid {
		return fmt.Errorf("invalid token")
	}

	tokenDetails, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		logger.ServiceLogger.Error("Error extracting jwt claims")
		return fmt.Errorf("error extracting jwt claims")
	}

	exp, ok := tokenDetails["exp"].(float64) // JWT stores exp as float64
	if !ok {
		logger.ServiceLogger.Error("exp claim not found or invalid")
		return fmt.Errorf("exp claim not found or invalid")
	}

	expTime := time.Unix(int64(exp), 0)
	if time.Now().After(expTime) {
		logger.ServiceLogger.Error("Token is expired")
		return fmt.Errorf("token is expired")
	}

	userIdStr := tokenDetails["id"]
	if userIdStr != nil {
		userId := int64(userIdStr.(float64))
		userDetails, exist := cache.UserCache.Get(int64(userIdStr.(float64)))
		if exist {
			if userDetail, ok := userDetails.(map[string]interface{}); ok {
				constant.CallContext = context.WithValue(constant.CallContext, constant.UserId, userId)
				constant.CallContext = context.WithValue(constant.CallContext, constant.User, userDetail)
				constant.CallContext = context.WithValue(constant.CallContext, constant.UserDepartment, userDetail["department"])
			}
		} else {
			return fmt.Errorf("invalid token")
		}
	}
	return nil
}

func isDownloadEndpoint(path string) bool {
	return strings.Contains(path, "/download/") || strings.Contains(path, "/download/patchdb/") || strings.Contains(path, "/download/patch/")
}

func isSignedUrlRequest(r *http.Request) bool {
	queryParams := r.URL.Query()
	_, hasSignature := queryParams["sig"]
	_, hasExpires := queryParams["expires"]
	return hasSignature && hasExpires
}

func validateSignedUrlAuth(w http.ResponseWriter, r *http.Request) bool {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return false
	}

	expiresStr := r.URL.Query().Get("expires")
	sig := r.URL.Query().Get("sig")

	if expiresStr == "" || sig == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Missing parameters : ", http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return false
	}

	expires, err := strconv.ParseInt(expiresStr, 10, 64)
	if err != nil || time.Now().Unix() > expires {
		jsonData, _ := common.RestToJson(w, common.Error("Link expired : ", http.StatusUnauthorized))
		http.Error(w, jsonData, http.StatusUnauthorized)

		return false
	}

	expectedSig := common.GenerateSignature(filename, expires)
	if sig != expectedSig {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid signature", http.StatusUnauthorized))
		http.Error(w, jsonData, http.StatusUnauthorized)
		return false
	}

	return true
}
