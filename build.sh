#!/bin/bash

# Run go get
echo "Running go get..."
go get

# Check if go get was successful
if [ $? -ne 0 ]; then
    echo "Error: go get failed. Exiting..."
    exit 1
fi

# Run go build
echo "Running go build..."
go build

# Check if go build was successful
if [ $? -ne 0 ]; then
    echo "Error: go build failed. Exiting..."
    exit 1
fi

# List of files to include in the zip
binary_file="deployment"
environment_file="app.config"
servicefile="package-deployment.service"
folder_to_copy="db"
directory1="filedb"
report_directory="Report"
directory2="logs"

# Create a temporary directory to store files
temp_dir="package-service"
mkdir -p "$temp_dir"

cp "$binary_file" "$temp_dir"
cp "$environment_file" "$temp_dir"
cp "$servicefile" "zirozen-package-deployment.service"
mv "zirozen-package-deployment.service" "$temp_dir"
cp -r "$folder_to_copy" "$temp_dir"
mkdir -p "$temp_dir/$directory1"
cp -r "$directory1/$report_directory" "$temp_dir/$directory1"
mkdir -p "$temp_dir/$directory2"


cp "oob_files.zip" "$temp_dir/$directory1/."
unzip -o "oob_files.zip" -d  "$temp_dir/$directory1/"


# Create a zip file
echo "Creating deployment zip file..."
zip -r Package_service.zip "$temp_dir"

# Check if zip creation was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to create zip file. Exiting..."
    exit 1
fi

rm -rf "$temp_dir"
echo "Build successful!"

