package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
)

type AssetPatchRelationHandler struct {
	Service *service.AssetPatchRelationService
}

func NewAssetPatchRelationHandler() *AssetPatchRelationHandler {
	return &AssetPatchRelationHandler{
		Service: service.NewAssetPatchRelationService(),
	}
}

func (handler AssetPatchRelationHandler) CreateAssetPatchRelationHandler(w http.ResponseWriter, r *http.Request) {
	var relationRest rest.AssetPatchRelationRest
	relationRest, err := rest.ConvertJsonToAgentPatchRest(w, r, relationRest)
	if err != nil {
		return
	}

	bulkActionResponse, err := handler.Service.BulkCreateAgentPatchRelation(relationRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating package : "+err.Error(), http.StatusInternalServerError))
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAssetPatchRelationHandler] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: bulkActionResponse})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAssetPatchRelationHandler] ", err)
		return
	}
}

func (handler AssetPatchRelationHandler) GetAllAssetPatchRelation(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pkgRest, err := handler.Service.GetAllAgentPatchView(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAssetPatchRelation] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAssetPatchRelation] ", err)
		return
	}
}

func (handler AssetPatchRelationHandler) GetAllAssetPatchRelationByAssetIdAndPatchState(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["asset_id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting id from api path  : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAssetPatchRelationByAssetIdAndPatchState] ", err)
			return
		}
		return
	}
	patchStateString, _ := vars["patch_state"]
	if patchStateString == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting id from api path  : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAssetPatchRelationByAssetIdAndPatchState] ", err)
			return
		}
		return
	}
	if searchFilter.Size == 0 {
		searchFilter.Size = 20
	}
	var qualifications []rest.Qualification
	qualifications = append(qualifications, rest.Qualification{
		Column:    "patchState",
		Operator:  "equals",
		Value:     patchStateString,
		Condition: "and",
		Type:      "enum",
		Reference: "patchState",
	})
	qualifications = append(qualifications, rest.Qualification{
		Column:    "assetId",
		Operator:  "equals",
		Value:     idString,
		Condition: "and",
	})
	qualifications = append(qualifications, searchFilter.Qualification...)
	searchFilter.Qualification = qualifications

	pkgRest, err := handler.Service.GetAllAgentPatchView(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAssetPatchRelationByAssetIdAndPatchState] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAssetPatchRelationByAssetIdAndPatchState] ", err)
		return
	}
}

func (handler AssetPatchRelationHandler) DeleteAssetPatchRelation(w http.ResponseWriter, r *http.Request) {
	relationId := common.ParseIdFromApiPath(w, r)
	if relationId == 0 {
		return
	}

	success, err := handler.Service.DeleteAgentPatchRelation(relationId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteAssetPatchRelation] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      relationId,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAssetPatchRelation] ", err)
		return
	}
}

func (handler AssetPatchRelationHandler) AddPatchExceptionHandler(w http.ResponseWriter, r *http.Request) {
	var status []map[string]interface{}
	var relationRest rest.AssetPatchRelationRest
	relationRest, err := rest.ConvertJsonToAgentPatchRest(w, r, relationRest)
	if err != nil {
		return
	}

	assetIds, err := service.NewAgentService().GetAllAssetIdsByScope(service.ConvertToAgentScopeFilter(relationRest.AgentScopeFilterRest))
	if err == nil {
		for _, assetId := range assetIds {
			for _, pchId := range relationRest.PatchIds {
				relation, err := handler.Service.Repository.GetByPatchIdAndAssetId(pchId, assetId)
				if err == nil {
					if relation.PatchState != model.Installed {
						_, err = handler.Service.UpdateAgentPatchRelation(relation.Id, relationRest)
						if err != nil {
							status = append(status, map[string]interface{}{
								"assetId": assetId,
								"patchId": pchId,
								"success": false,
								"error":   err.Error(),
							})
						} else {
							status = append(status, map[string]interface{}{
								"assetId": assetId,
								"patchId": pchId,
								"success": true,
							})
						}
					} else {
						status = append(status, map[string]interface{}{
							"assetId": assetId,
							"patchId": pchId,
							"success": false,
							"error":   "installed patch can not be added in exception",
						})
					}

				} else {
					status = append(status, map[string]interface{}{
						"assetId": assetId,
						"patchId": pchId,
						"success": false,
						"error":   err.Error(),
					})
				}
			}
		}
	}
	jsonData, _ := common.RestToJson(w, status)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AddPatchExceptionHandler] ", err)
		return
	}
}
