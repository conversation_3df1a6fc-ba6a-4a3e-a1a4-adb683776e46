package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type ComputerGroupHandler struct {
	service *service.ComputerGroupService
}

func NewComputerGroupHandler() *ComputerGroupHandler {
	return &ComputerGroupHandler{
		service: service.NewComputerGroupService(),
	}
}

func (handler *ComputerGroupHandler) GetComputerGroupHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)

	group, err := handler.service.GetById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetComputerGroupHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: group})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetComputerGroupHandler]", err)
	}
}

func (handler *ComputerGroupHandler) CreateComputerGroupHandler(w http.ResponseWriter, r *http.Request) {
	var groupRest rest.ComputerGroupRest
	groupRest, err := convertJsonToComputerGroupRest(w, r, groupRest)
	if err != nil {
		return
	}

	id, err := handler.service.Create(groupRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateComputerGroupHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateComputerGroupHandler]", err)
	}
}

func (handler *ComputerGroupHandler) UpdateComputerGroupHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	var groupRest rest.ComputerGroupRest
	groupRest, err := convertJsonToComputerGroupRest(w, r, groupRest)
	if err != nil {
		return
	}

	isUpdated, updateError := handler.service.Update(id, groupRest)
	if updateError.Message != "" {
		jsonData, _ := common.RestToJson(w, updateError)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateComputerGroupHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateComputerGroupHandler]", err)
	}
}

func (handler *ComputerGroupHandler) DeleteComputerGroupHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)

	success, err := handler.service.DeleteById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteComputerGroupHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteComputerGroupHandler]", err)
	}
}

func (handler *ComputerGroupHandler) GetAllComputerGroupsHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	groupsRest, err := handler.service.GetAllComputerGroups(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllComputerGroupsHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, groupsRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllComputerGroupsHandler]", err)
	}
}

func convertJsonToComputerGroupRest(w http.ResponseWriter, r *http.Request, cgRest rest.ComputerGroupRest) (rest.ComputerGroupRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &cgRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid JSON format", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToComputerGroupRest]", err)
		}
		return cgRest, err
	}

	v := validator.New()
	err = v.Struct(cgRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values: %s", err.Field(), err.Param()), http.StatusBadRequest))
			w.WriteHeader(http.StatusBadRequest)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToComputerGroupRest]", err)
			}
			break
		}
		return cgRest, err
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertJsonToComputerGroupRest]", err)
	}
	cgRest.PatchMap = patchMap
	return cgRest, nil
}
