package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

type ServerPostOperationHandler struct {
}

func GetServerPostOperationHandler() *ServerPostOperationHandler {
	return &ServerPostOperationHandler{}
}

func (handler ServerPostOperationHandler) ExecutePostOperation(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}

	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("failed to parse request", http.StatusBadRequest))
		w.<PERSON>rite<PERSON><PERSON>(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ExecutePostOperation]", err)
		}
		return
	}

	sysCommandType := request["type"]
	if "reload_permission" == sysCommandType.(string) {
		ReloadUserCache()
	} else if "reload_asset_configuration" == sysCommandType.(string) {
		PrepareAssetCache()
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go handler.executeAssetPostOperation(contextMap)
			}
		}
	} else if "execute_remediation_action" == sysCommandType.(string) {
		logger.ServiceLogger.Info("processing execute_remediation_action :")
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go service.NewSystemActionService().CreateSystemActionsAgentTasks(contextMap)
			}
		}
	} else if "execute_policy_remediation_action" == sysCommandType.(string) {
		logger.ServiceLogger.Info("processing execute_policy_remediation_action :")
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go service.NewConfigurationService().CreateRemediationActionsAgentTasks(contextMap)
			}
		}
	} else if "create_application_policy_task" == sysCommandType.(string) {
		logger.ServiceLogger.Info("processing create_application_policy_task :")
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go service.NewAgentTaskService().CreateApplicationPolicyAgentTask(contextMap)
			}
		}
	} else if "discovery_command" == sysCommandType.(string) {
		logger.ServiceLogger.Info("processing discovery_command :")
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go service.NewNetworkDiscoveryService().ExecuteRequest(contextMap)
			}
		}
	} else if "sync_msrc_vulnerabilities" == sysCommandType.(string) {
		go service.NewMsrcVulnerabilityService().ExecuteMsrcVulnerabilitiesSync()
	} else if "change_log_level" == sysCommandType.(string) {
		if context, ok := request["context"]; ok {
			if contextMap, ok := context.(map[string]interface{}); ok {
				go logger.ChangeLogLeve(contextMap["level"].(string))
			}
		}
	}

	result := map[string]interface{}{
		"success": true,
	}
	jsonData, _ := common.RestToJson(w, result)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		return
	}
}

func (handler ServerPostOperationHandler) executeAssetPostOperation(context map[string]interface{}) {
	logger.ServiceLogger.Info("executeAssetPostOperation : ", context)
	if context != nil && len(context) > 0 {
		if typeStr, ok := context["type"].(string); ok {
			if strings.EqualFold(typeStr, "delete") {
				if assetIds, ok := context["asset_id"].([]interface{}); ok {
					stringIDs := make([]string, len(assetIds))
					for i, id := range assetIds {
						stringIDs[i] = id.(string)
					}
					if len(stringIDs) > 0 {
						service.NewAgentTaskService().Repository.DeleteByAssetId(stringIDs)
						service.NewComplianceService().Repository.DeleteByAssetId(stringIDs)
						service.NewAssetService().Repository.DeleteByAssetId(stringIDs)
						service.NewAssetPatchRelationService().Repository.DeleteByAssetId(stringIDs)
						service.NewPatchAssetApplicationService().Repository.DeleteByAssetId(stringIDs)
					}
				}
			} else if strings.EqualFold(typeStr, "add") {
				if assetId, ok := context["asset_id"].(string); ok {
					id, _ := strconv.ParseInt(assetId, 10, 64)
					service.NewDeploymentService().PostOperationAddAsset(id)
				}
			}
		}

	}
}
