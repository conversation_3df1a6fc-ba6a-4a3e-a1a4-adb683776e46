package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

type ConfigurationHandler struct {
	Service *service.ConfigurationService
}

func NewConfigurationHandler() *ConfigurationHandler {
	return &ConfigurationHandler{
		Service: service.NewConfigurationService(),
	}
}

func (handler ConfigurationHandler) GetConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting configuration with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetConfigurationHandler]", err)
		}
		return
	}

	intValue, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid configuration id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetConfigurationHandler]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	configurationRest, err := service.NewConfigurationService().GetConfiguration(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetConfigurationHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, configurationRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) CreateConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	var configurationRest rest.ConfigurationRest
	configurationRest, err := convertJsonToConfigurationRest(w, r, configurationRest)
	if err != nil {
		return
	}

	//TODO Handle Before Create

	id, err := service.NewConfigurationService().Create(configurationRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating configuration : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateConfigurationHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) UpdateConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	var configurationRest rest.ConfigurationRest
	configurationRest, err := convertJsonToConfigurationRest(w, r, configurationRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting configuration with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateConfigurationHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetConfigurationHandler]", err)
		}
		return
	}

	isUpdated, customErr := service.NewConfigurationService().Update(id, configurationRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateConfigurationHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) BulkDeleteConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	var bulkDeleteRequest rest.BulkDeleteRequest
	bulkActionResponse := rest.BulkActionResponse{}
	deleteRequest, err := service.ConvertJsonToBulkDeleteRequest(w, r, bulkDeleteRequest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range deleteRequest.Ids {
		isUpdated, err := handler.Service.DeleteConfiguration(objId, deleteRequest.IsPermanentDelete)
		if err != nil {
			failedIdMap[objId] = err.Error()
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeleteConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) DeleteConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting configuration with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteConfigurationHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid configuration id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteConfigurationHandler]", err)
		}
		return
	}

	success, err := service.NewConfigurationService().DeleteConfiguration(id, false)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteConfigurationHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) GetAllConfigurationHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	configurationRest, err := service.NewConfigurationService().GetAllConfiguration(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllConfigurationHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, configurationRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllConfigurationHandler]", err)
	}
}

func (handler ConfigurationHandler) GetTagFilterHandler(w http.ResponseWriter, r *http.Request) {
	response := handler.Service.GetTagFilterWithCount()
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetTagFilterHandler]", err)
	}
}

func convertJsonToConfigurationRest(w http.ResponseWriter, r *http.Request, configurationRest rest.ConfigurationRest) (rest.ConfigurationRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &configurationRest)
	v := validator.New()
	err = v.Struct(configurationRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToConfigurationRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertJsonToConfigurationRest]", err)
	}
	configurationRest.PatchMap = patchMap
	return configurationRest, err
}
