package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

type DeploymentBundleHandler struct {
	Service *service.DeploymentBundleService
}

func NewDeploymentBundleHandler() *DeploymentBundleHandler {
	return &DeploymentBundleHandler{Service: service.NewDeploymentBundleService()}
}

func (handler DeploymentBundleHandler) GetHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting bundle with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetHand<PERSON>]", err)
		}
		return
	}

	intValue, err := strconv.ParseInt(idString, 10, 64)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid bundle id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetHandler]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	bundleRest, err := handler.Service.GetById(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, bundleRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetHandler]", err)
	}
}

func (handler DeploymentBundleHandler) CreateHandler(w http.ResponseWriter, r *http.Request) {
	var restModel rest.DeploymentBundleRest
	restModel, err := convertJsonToRest(w, r, restModel)
	if err != nil {
		return
	}

	id, customErr := handler.Service.Create(restModel)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating bundle : "+customErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateHandler]", err)
	}
}

func (handler DeploymentBundleHandler) UpdateHandler(w http.ResponseWriter, r *http.Request) {
	var restModel rest.DeploymentBundleRest
	restModel, err := convertJsonToRest(w, r, restModel)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting bundle with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateHandler]", err)
		}
		return
	}

	isUpdated, customErr := handler.Service.Update(id, restModel)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateHandler]", err)
	}
}

func (handler DeploymentBundleHandler) DeleteHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting bundle with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid bundle id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteHandler]", err)
		}
		return
	}

	var permanentDelete = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("permanentDelete")); err == nil {
		permanentDelete = isVisibleParam
	}

	success, err := handler.Service.DeletePackage(id, permanentDelete)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteHandler]", err)
	}
}

func (handler DeploymentBundleHandler) BulkDeleteHandler(w http.ResponseWriter, r *http.Request) {
	var bulkDeleteRequest rest.BulkDeleteRequest
	bulkActionResponse := rest.BulkActionResponse{}
	deleteRequest, err := service.ConvertJsonToBulkDeleteRequest(w, r, bulkDeleteRequest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range deleteRequest.Ids {
		isUpdated, err := handler.Service.DeletePackage(objId, deleteRequest.IsPermanentDelete)
		if err != nil {
			failedIdMap[objId] = err.Error()
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeleteHandler]", err)
	}
}

func (handler DeploymentBundleHandler) GetAllHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[BulkDeleteHandler]", err)
		}
		return
	}

	pkgRest, err := handler.Service.GetAllBundles(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[BulkDeleteHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeleteHandler]", err)
	}
}

func convertJsonToRest(w http.ResponseWriter, r *http.Request, restModel rest.DeploymentBundleRest) (rest.DeploymentBundleRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &restModel)
	v := validator.New()
	err = v.Struct(restModel)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusBadRequest))
			w.WriteHeader(http.StatusBadRequest)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		return rest.DeploymentBundleRest{}, err
	}
	restModel.PatchMap = patchMap
	return restModel, err
}
