package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
)

type AssetPatchCveRelationHandler struct {
	Service *service.AssetPatchCveRelationService
}

func NewAssetPatchCveRelationHandler() *AssetPatchCveRelationHandler {
	return &AssetPatchCveRelationHandler{
		Service: service.NewAssetPatchCveRelationService(),
	}
}

func (handler AssetPatchCveRelationHandler) GetByCVE(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cve, _ := vars["cve"]
	if cve == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting AssetPatchCveRelation with cve : "+cve, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AssetPatchCveRelationHandler][GetByCVE]", err)
			return
		}
		return
	}

	relationRest, err := handler.Service.GetByCve(cve)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AssetPatchCveRelationHandler][GetByCVE]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: relationRest})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AssetPatchCveRelationHandler][GetByCVE]", err)
		return
	}
}

func (handler AssetPatchCveRelationHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	if searchFilter.SortBy == "" {
		searchFilter.SortBy = "cve"
	}
	configurationRest, err := handler.Service.GetAllPatchCveRelation(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AssetPatchCveRelationHandler][GetAll]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, configurationRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AssetPatchCveRelationHandler][GetAll]", err)
		return
	}
}
