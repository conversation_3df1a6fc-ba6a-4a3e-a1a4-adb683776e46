package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"net/http"
)

type PatchLanguageHandler struct {
	service *service.LanguageService
}

func NewPatchLanguageHandler() *PatchLanguageHandler {
	return &PatchLanguageHandler{
		service: service.NewLanguageService(),
	}
}

func (handler PatchLanguageHandler) CreateHandler(w http.ResponseWriter, r *http.Request) {
	var languageRest rest.LanguageRest
	languageRest, err := rest.ConvertJsonToLanguageRest(w, r, languageRest)
	if err != nil {
		return
	}

	id, createErr := handler.service.Create(languageRest)
	if createErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating package : "+createErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateHandler]", err)
	}
}

func (handler PatchLanguageHandler) GetAllPatchLanguageHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	patchRest, err := handler.service.GetAllLanguage(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchLanguageHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, patchRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPatchLanguageHandler]", err)
	}
}
