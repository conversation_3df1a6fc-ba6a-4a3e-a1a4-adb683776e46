package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"io"
	"mime"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
)

var allowedScriptExtensions = map[string]struct{}{
	"sh": {}, "ps1": {}, "bat": {}, "vbs": {}, "reg": {}, "vbe": {},
	"wsf": {}, "wsc": {}, "ps1xml": {}, "psm1": {}, "cab": {},
}

var allowedImageExtensions = map[string]struct{}{
	"jpg": {}, "png": {}, "jpeg": {},
}

var allowedPackageExtensions = map[string]struct{}{
	"exe": {}, "msi": {}, "zip": {}, "cab": {}, "pkg": {}, "dmg": {}, "deb": {}, "rpm": {}, "iso": {},
}

func isAllowedExtension(filename string) bool {
	ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(filename), "."))
	_, isImage := allowedImageExtensions[ext]
	_, isScript := allowedScriptExtensions[ext]
	_, isPackage := allowedPackageExtensions[ext]
	return isImage || isScript || isPackage
}

func UploadFile(w http.ResponseWriter, r *http.Request) {
	err := r.ParseMultipartForm(1024 << 20)
	if err != nil {
		logger.ServiceLogger.Error("failed to parse form data", err)
	} // 1024 MB limit
	file, handler, err := r.FormFile("file")
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	realName := handler.Filename
	if !isAllowedExtension(realName) {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid file extension", http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[UploadFile]", err)
		}
	}(file)

	checksum, err := common.GenerateFileCheckSum(file)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	handler.Filename = uuid.New().String()

	_, err = common.UploadFileToFileDB(handler, err, file)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	fileDataRest := rest.FileDataRest{
		BaseEntityRest: rest.BaseEntityRest{},
		RealName:       realName,
		RefName:        handler.Filename,
		CheckSum:       checksum,
		Size:           handler.Size,
	}
	fileDataService := service.NewFileDataService()
	response, err := fileDataService.CreateFileData(fileDataRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}
	response.SignedUrl = generateUrl(response.RefName)
	jsonData, _ := common.RestToJson(w, response)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UploadFile]", err)
	}
}

func UploadPatchFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	patchId, _ := vars["id"]
	if patchId == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting patch id : "+patchId, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	err := r.ParseMultipartForm(1024 << 20)
	if err != nil {
		logger.ServiceLogger.Error("failed to parse form data", err)
	} // 1024 MB limit
	file, handler, err := r.FormFile("file")
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	realName := handler.Filename
	if !isAllowedExtension(realName) {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid file extension", http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[UploadPatchFile]", err)
		}
	}(file)

	patch, err := service.NewPatchService().GetPatch(common.ConvertToInt64(patchId), false)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}
	checksum, err := common.GenerateFileCheckSum(file)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	handler.Filename = uuid.New().String()

	_, err = common.UploadPatchFile(handler, err, file, patch.Name)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	fileDataRest := rest.FileDataRest{
		BaseEntityRest: rest.BaseEntityRest{Name: patch.Name},
		RealName:       realName,
		RefName:        handler.Filename,
		CheckSum:       checksum,
		Size:           handler.Size,
	}
	fileDataService := service.NewFileDataService()
	response, err := fileDataService.CreateFileData(fileDataRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}
	response.SignedUrl = generateUrl(response.RefName)
	jsonData, _ := common.RestToJson(w, response)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UploadPatchFile]", err)
	}
}

func GetFileData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	fileDataService := service.NewFileDataService()
	response, err := fileDataService.GetFileDataByFileRefName(filename)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}
	response.SignedUrl = generateUrl(response.RefName)
	jsonData, _ := common.RestToJson(w, response)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetFileData]", err)
	}
}

func DownloadPatchDbFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.PatchDbPath(), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	//Get FileInfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		http.Error(w, "Error accessing file", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadPatchDbFile]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadPatchDbFile]", err)
	}
}

func DownloadPatchFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	//Get File data service and get FileData Model from DB
	fileDataService := service.NewFileDataService()
	response, err := fileDataService.GetFileDataByFileRefName(filename)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.PatchFilePath(response.Name), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	//Get FileInfo
	filename = response.RealName
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		http.Error(w, "Error accessing file", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFile]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadFile]", err)
	}
}

func DownloadFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	//Get File data service and get FileData Model from DB
	fileDataService := service.NewFileDataService()
	response, err := fileDataService.GetFileDataByFileRefName(filename)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.FileDirectoryPath(), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	//Get FileInfo
	filename = response.RealName
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		http.Error(w, "Error accessing file", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFile]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadFile]", err)
	}
}

func DownloadLogFile(w http.ResponseWriter, r *http.Request) {

	//Check file exist or not
	currentDir, _ := os.Getwd()
	filePath := fmt.Sprintf("%s/%s/%s", currentDir, "logs", "service.log")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	//Get FileInfo
	filename := "service.log"
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		http.Error(w, "Error accessing file", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadLogFile]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadLogFile]", err)
	}
}

func PreviewFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		http.Error(w, jsonData, http.StatusBadRequest)
		return
	}

	fileDataService := service.NewFileDataService()
	response, err := fileDataService.GetFileDataByFileRefName(filename)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		http.Error(w, jsonData, http.StatusInternalServerError)
		return
	}

	filePath := fmt.Sprintf("%s/%s", common.FileDirectoryPath(), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	filename = response.RealName

	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))

	file, err := os.Open(filePath)
	if err != nil {
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[PreviewFile]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[PreviewFile]", err)
	}
}

func GenerateScriptFile(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		logger.ServiceLogger.Error("[GenerateScriptFile]", err)
	}

	uploadDirectory := common.FileDirectoryPath()
	if _, err := os.Stat(uploadDirectory); os.IsNotExist(err) {
		err := os.Mkdir(uploadDirectory, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
	}

	fileName := request["file_name"].(string) + retrieveExtension(request["script_type"].(string))
	refName := uuid.New().String()
	filePath := filepath.Join(uploadDirectory, refName)
	file, err := os.Create(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
	}(file)

	_, err = file.Write([]byte(request["file_content"].(string)))
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
		return
	}

	checksum, err := common.GenerateFileCheckSum(file)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
		return
	}

	fi, err := file.Stat()
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
		return
	}

	fileDataRest := rest.FileDataRest{
		BaseEntityRest: rest.BaseEntityRest{},
		RealName:       fileName,
		RefName:        refName,
		CheckSum:       checksum,
		Size:           fi.Size(),
	}
	fileDataService := service.NewFileDataService()
	response, err := fileDataService.CreateFileData(fileDataRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GenerateScriptFile]", err)
		}
		return
	}
	response.SignedUrl = generateUrl(response.RefName)
	jsonData, _ := common.RestToJson(w, response)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GenerateScriptFile]", err)
	}
}

func retrieveExtension(fileType string) string {
	if strings.EqualFold(fileType, "powershell") || strings.EqualFold(fileType, "ps") || strings.EqualFold(fileType, "ps1") {
		return ".ps1"
	} else if strings.EqualFold(fileType, "bat") || strings.EqualFold(fileType, "batch") {
		return ".bat"
	} else {
		return ".sh"
	}
}

func GenerateSignedUrlHandler(w http.ResponseWriter, r *http.Request) {
	filename := r.URL.Query().Get("filename")
	if filename == "" {
		http.Error(w, "Missing filename", http.StatusBadRequest)
		return
	}
	response := map[string]string{"id": filename, "url": generateUrl(filename)}
	w.Header().Set("Content-Type", "application/json")
	err := json.NewEncoder(w).Encode(response)
	if err != nil {
		logger.ServiceLogger.Error(err)
		return
	}
}

func generateUrl(filename string) string {
	expires := time.Now().Unix() + 600
	sig := common.GenerateSignature(filename, expires)
	return fmt.Sprintf("expires=%d&sig=%s", expires, sig)
}
