package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"net/http"
)

type AgentTaskResultHandler struct {
	Service *service.AgentTaskResultService
}

func NewAgentTaskResultHandler() *AgentTaskResultHandler {
	return &AgentTaskResultHandler{
		Service: service.NewAgentTaskResultService(),
	}
}

func (handler AgentTaskResultHandler) GetAllTaskResultHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pkgRest, err := handler.Service.GetAllTaskResult(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.Write<PERSON>eader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllTaskResultHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllTaskResultHandler]", err)
		return
	}
}
