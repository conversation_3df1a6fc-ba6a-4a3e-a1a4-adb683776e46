package handler

import (
	"deployment/common"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type PatchPreferenceHandler struct {
	service *service.PatchPreferenceService
}

func NewPatchPreferenceHandler() *PatchPreferenceHandler {
	return &PatchPreferenceHandler{
		service: service.NewPatchPreferenceService(),
	}
}

func writeJSONResponse(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	jsonData, err := json.Marshal(data)
	if err != nil {
		http.Error(w, "Failed to marshal JSON", http.StatusInternalServerError)
		return
	}
	_, err = w.Write(jsonData)
	if err != nil {
		return
	}
}

func (handler PatchPreferenceHandler) GetPatchPreferenceHandler(w http.ResponseWriter, r *http.Request) {
	patchPreference, err := handler.service.Get()
	if err != nil {
		writeJSONResponse(w, common.Error(err.Error(), http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}
	writeJSONResponse(w, rest.ResponseRest{Object: patchPreference}, http.StatusOK)
}

func (handler PatchPreferenceHandler) UpdatePatchPreferenceHandler(w http.ResponseWriter, r *http.Request) {
	var patchPreferenceRest rest.PatchPreferenceRest
	patchPreferenceRest, err := convertJsonToPatchPreferenceRest(w, r, patchPreferenceRest)
	if err != nil {
		return
	}

	isUpdated, updateErr := handler.service.Update(patchPreferenceRest)
	if updateErr.Message != "" {
		writeJSONResponse(w, updateErr, http.StatusInternalServerError)
		return
	}

	writeJSONResponse(w, rest.IdResponseRest{Id: 0, Success: isUpdated}, http.StatusOK)
}

func convertJsonToPatchPreferenceRest(w http.ResponseWriter, r *http.Request, patchPreferenceRest rest.PatchPreferenceRest) (rest.PatchPreferenceRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &patchPreferenceRest)
	if err != nil {
		writeJSONResponse(w, common.Error("Failed to unmarshal JSON", http.StatusBadRequest), http.StatusBadRequest)
		return patchPreferenceRest, err
	}

	v := validator.New()
	err = v.Struct(patchPreferenceRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			writeJSONResponse(w, common.Error(fmt.Sprintf("Validation error on field %s: %s", err.Field(), err.Error()), http.StatusBadRequest), http.StatusBadRequest)
			return patchPreferenceRest, err
		}
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		writeJSONResponse(w, common.Error("Failed to unmarshal patch map", http.StatusBadRequest), http.StatusBadRequest)
		return patchPreferenceRest, err
	}
	patchPreferenceRest.PatchMap = patchMap

	return patchPreferenceRest, nil
}
