package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"io"
	"mime"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	time2 "time"
)

type AssetPatchHandler struct {
	Service *service.AssetPatchService
}

func NewAssetPatchHandler() *AssetPatchHandler {
	return &AssetPatchHandler{Service: service.NewAssetPatchService()}
}

func (handler AssetPatchHandler) GetAllPendingPatchScanCommandByAssetId(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("invalid asset  : %d", id), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPendingPatchScanCommandByAssetId]", err)
		}
		return
	}

	agentTaskService := service.NewAgentTaskService()
	taskList, err := agentTaskService.Repository.GetByAgentIdAndTaskType(int64(model.PATCH_SCAN), id, 0, 1)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting patch_scan task list for asset  : %d", id), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPendingPatchScanCommandByAssetId]", err)
		}
		return
	}

	var pendingTask []model.AgentTask
	if taskList != nil {
		for _, patchScanTask := range taskList {
			if !(patchScanTask.TaskStatus == model.TaskFailed || patchScanTask.TaskStatus == model.TaskSuccess) {
				pendingTask = append(pendingTask, patchScanTask)
			}
		}
	}

	var response rest.ListResponseRest
	response.ObjectList = pendingTask
	response.TotalCount = len(pendingTask)

	jsonData, _ := common.RestToJson(w, response)

	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPendingPatchScanCommandByAssetId]", err)
		return
	}
}

func (handler AssetPatchHandler) CreateAssetPatchScanCommand(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting request parameter  : %d", id), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
		}
		return
	}

	agentTaskService := service.NewAgentTaskService()
	taskList, err := agentTaskService.Repository.GetByAgentIdAndTaskType(int64(model.PATCH_SCAN), id, 0, 1)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting patch_scan task list for asset  : %d", id), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
		}
		return
	}
	if taskList != nil {
		patchScanTask := taskList[0]
		if patchScanTask.TaskStatus == model.TaskFailed || patchScanTask.TaskStatus == model.TaskSuccess {
			agentTask := rest.AgentTaskRest{
				TaskType:   model.PATCH_SCAN.String(),
				AgentId:    id,
				TaskStatus: "ready_to_deploy",
			}
			_, err = agentTaskService.Create(agentTask)
			if err != nil {
				logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
			}
		}
	} else {
		agentTask := rest.AgentTaskRest{
			TaskType:   model.PATCH_SCAN.String(),
			AgentId:    id,
			TaskStatus: "ready_to_deploy",
		}
		_, err = agentTaskService.Create(agentTask)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
		}
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
		return
	}
}

func (handler AssetPatchHandler) CreateBulkAssetPatchScanCommand(w http.ResponseWriter, r *http.Request) {
	var request []int64
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateBulkAssetPatchScanCommand]", err)
		}
		return
	}

	if request != nil {
		for _, id := range request {
			agentTaskService := service.NewAgentTaskService()
			taskList, err := agentTaskService.Repository.GetByAgentIdAndTaskType(int64(model.PATCH_SCAN), int64(id), 0, 1)
			if err != nil {
				logger.ServiceLogger.Error("[CreateBulkAssetPatchScanCommand]", err)
			}
			if taskList != nil {
				patchScanTask := taskList[0]
				if patchScanTask.TaskStatus == model.TaskFailed || patchScanTask.TaskStatus == model.TaskSuccess {
					agentTask := rest.AgentTaskRest{
						TaskType:   model.PATCH_SCAN.String(),
						AgentId:    int64(id),
						TaskStatus: "ready_to_deploy",
					}
					_, err = agentTaskService.Create(agentTask)
					if err != nil {
						logger.ServiceLogger.Error("[CreateBulkAssetPatchScanCommand]", err)
					}
				}
			} else {
				agentTask := rest.AgentTaskRest{
					TaskType:   model.PATCH_SCAN.String(),
					AgentId:    int64(id),
					TaskStatus: "ready_to_deploy",
				}
				_, err = agentTaskService.Create(agentTask)
				if err != nil {
					logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
				}
			}
		}
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAssetPatchScanCommand]", err)
		return
	}
}

func (handler AssetPatchHandler) GetAssetPatchScanCommandHistory(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{}
	response["lastScanTime"] = 0
	response["isScanRunning"] = false
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while parsing request parameter  : %d", id), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAssetPatchScanCommandHistory]", err)
		}
		return
	}

	agentTaskService := service.NewAgentTaskService()
	taskList, _ := agentTaskService.Repository.GetByAgentIdAndTaskType(int64(model.PATCH_SCAN), id, 0, 1)
	if taskList != nil {
		patchScanTask := taskList[0]
		if patchScanTask.TaskStatus == model.TaskFailed || patchScanTask.TaskStatus == model.TaskSuccess {
			response["lastScanTime"] = patchScanTask.UpdatedTime
		} else {
			response["isScanRunning"] = true
		}
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAssetPatchScanCommandHistory]", err)
		return
	}
}

func (handler AssetPatchHandler) ProcessRemoveSupersededPatch(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}

	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while parsing request parameter"), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ProcessRemoveSupersededPatch]", err)
		}
		return
	}
	finalUuidMap := map[string]bool{}
	if len(request) > 0 {
		patchUuidMap := map[string]rest.WindowsPatchRest{}
		uuidToRemove := map[string]bool{}
		thirdPartyPatchUuidMap := map[string]bool{}
		if fileListData, ok := request["fileList"]; ok {
			if fileList, ok := fileListData.([]interface{}); ok {
				for _, file := range fileList {
					pch, err := service.NewWindowsPatchService().GetWindowsPatchByUuid(strings.ToUpper(file.(string)))
					thirdPartyPatch, err1 := service.NewThirdPartyPackageService().GetThirdPartyPatchByUuid(file.(string))
					if err1 == nil && thirdPartyPatch.Id > 0 {
						thirdPartyPatchUuidMap[file.(string)] = true
					} else if err == nil && pch.Id > 0 {
						patchUuidMap[strings.ToUpper(file.(string))] = pch
						finalUuidMap[strings.ToUpper(file.(string))] = pch.CabExist
					}
				}

				for _, file := range fileList {
					pch, err := service.NewWindowsPatchService().GetWindowsPatchByUuid(strings.ToUpper(file.(string)))
					if err == nil && pch.Id > 0 {
						service.NewAssetPatchProcessorService().RemoveSupersededFromMap(pch, patchUuidMap, &uuidToRemove)
					}
				}

				for key := range uuidToRemove {
					delete(finalUuidMap, key)
				}
			}
		}
		for key := range thirdPartyPatchUuidMap {
			finalUuidMap[key] = false
		}
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": finalUuidMap,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[ProcessRemoveSupersededPatch]", err)
		return
	}
}

func (handler AssetPatchHandler) ProcessDiscoveredPatchData(w http.ResponseWriter, r *http.Request) {
	var scannedPatchData rest.ScannedPatchData
	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}

	asset, err := service.NewAgentService().GetAssetMetaDataById(assetId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting asset by assetId for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ProcessDiscoveredPatchData]", err)
			return
		}
		return
	}

	scannedPatchData, err = rest.ConvertJsonToScannedPatchData(w, r, scannedPatchData)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while parsing payload for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ProcessDiscoveredPatchData]", err)
			return
		}
		return
	}

	marshal, _ := json.Marshal(scannedPatchData)
	logger.ServiceLogger.Info("Patch data : ", string(marshal))

	switch strings.ToLower(asset.Platform) {
	case "windows":
		service.NewAssetPatchProcessorService().ProcessWindowsPatch(assetId, scannedPatchData)
	case "darwin":
		service.NewAssetPatchProcessorService().ProcessMacPatches(assetId, scannedPatchData)
	default:
		service.NewAssetPatchProcessorService().ProcessLinuxPatches(assetId, scannedPatchData)
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[ProcessDiscoveredPatchData]", err)
		return
	}
}

func (handler AssetPatchHandler) GetAllApplicabilityRuleFileList(w http.ResponseWriter, r *http.Request) {
	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}

	_, err := service.NewAgentService().GetAssetMetaDataById(assetId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting asset by assetId for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllApplicabilityRuleFileList]", err)
			return
		}
		return
	}

	categoryFileMap, err := handler.Service.GetAllApplicabilityRuleFileList(assetId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while GetAllApplicabilityRuleFileList for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllApplicabilityRuleFileList]", err)
			return
		}
		return
	}
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": categoryFileMap,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllApplicabilityRuleFileList]", err)
		return
	}

}

func (handler AssetPatchHandler) DownloadApplicabilityRuleFileByFileName(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
			return
		}
		return
	}

	filePath := filepath.Join(common.PatchXmlPath(), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		jsonData, _ := common.RestToJson(w, common.Error("File not found : "+filename, http.StatusNotFound))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
			return
		}
		return
	}

	//Get FileInfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file: "+filename, http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
			return
		}
		return
	}

	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	file, err := os.Open(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error opening file: "+filename, http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
			return
		}
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadApplicabilityRuleFileByFileName]", err)
		return
	}
}

func (handler AssetPatchHandler) DownloadCabFileByFileName(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	uuid, _ := vars["filename"]
	if uuid == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+uuid, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
			return
		}
		return
	}
	uuid = strings.ToUpper(uuid)
	filePath := service.NewPatchSyncService().ExecuteDownloadCab(uuid)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		jsonData, _ := common.RestToJson(w, common.Error("failed to download file : "+uuid, http.StatusNotFound))
		w.WriteHeader(http.StatusNotFound)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
			return
		}
		return
	}

	//Get FileInfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("failed to access file : "+uuid, http.StatusNotFound))
		w.WriteHeader(http.StatusNotFound)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
			return
		}
		return
	}

	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", uuid+".7z"))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(uuid+".7z")))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	file, err := os.Open(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("failed to open file : "+uuid, http.StatusNotFound))
		w.WriteHeader(http.StatusNotFound)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
			return
		}
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadCabFileByFileName]", err)
		return
	}
}

func (handler AssetPatchHandler) ProcessLinuxInstalledPatchData(w http.ResponseWriter, r *http.Request) {
	startTime := time2.Now().UnixMilli()
	logger.ServiceLogger.Info("[ProcessLinuxInstalledPatchData] api request received")
	var scannedPatchData rest.ScannedPatchData
	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}

	_, err := service.NewAgentService().GetAssetMetaDataById(assetId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting asset by assetId for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ProcessLinuxInstalledPatchData]", err)
			return
		}
		return
	}

	scannedPatchData, err = rest.ConvertJsonToScannedPatchData(w, r, scannedPatchData)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while parsing payload for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ProcessLinuxInstalledPatchData]", err)
			return
		}
		return
	}

	installedPkgList := service.NewAssetPatchProcessorService().ProcessLinuxInstalledPatches(assetId, scannedPatchData)

	logger.ServiceLogger.Info("[ProcessLinuxInstalledPatchData] api response received : ", time2.Now().UnixMilli()-startTime)
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": installedPkgList,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[ProcessLinuxInstalledPatchData]", err)
		return
	}
}

func (handler AssetPatchHandler) GetLatestPackageInfoByInstalledList(w http.ResponseWriter, r *http.Request) {
	startTime := time2.Now()
	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}
	logger.ServiceLogger.Info("[GetLatestPackageInfoByInstalledList] api request received for asset ", assetId)
	asset, err := service.NewAgentService().GetAssetMetaDataById(assetId)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error while getting asset by assetId for asset  : %d", assetId), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetLatestPackageInfoByInstalledList]", err)
			return
		}
		return
	}

	fileName := service.NewAssetPatchProcessorService().PrepareLatestPackageInfoByInstalledList(asset)
	result := map[string]interface{}{
		"result": map[string]string{
			"fileName": fileName,
		},
	}
	if fileName == "" {
		result = map[string]interface{}{
			"result": "",
		}
	}
	logger.ServiceLogger.Info("[GetLatestPackageInfoByInstalledList] api response received : ", time2.Since(startTime))
	jsonData, _ := common.RestToJson(w, result)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetLatestPackageInfoByInstalledList]", err)
		return
	}
}

func (handler AssetPatchHandler) DownloadMacDistFiles(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	var fileName string
	osVersion, _ := vars["osVersion"]
	if osVersion == "" {
		jsonData, _ := common.RestToJson(w, common.Error("osVersion is null ", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
			return
		}
		return
	}

	macRepo := filepath.Join(common.PatchDbPath(), "mac")
	if _, err := os.Stat(macRepo); os.IsNotExist(err) {
		jsonData, _ := common.RestToJson(w, common.Error("File not found for os version : "+osVersion, http.StatusNotFound))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
			return
		}
		return
	}

	switch {
	case strings.HasPrefix(osVersion, "10"):
		fileName = "10.15.7z"
	case strings.HasPrefix(osVersion, "11"):
		fileName = "10.16.7z"
	case strings.HasPrefix(osVersion, "12"):
		fileName = "12.7z"
	case strings.HasPrefix(osVersion, "13"):
		fileName = "13.7z"
	case strings.HasPrefix(osVersion, "14"):
		fileName = "14.7z"
	case strings.HasPrefix(osVersion, "15"):
		fileName = "15.7z"

	default:
		fileName = osVersion + ".7z"
	}

	filePath := filepath.Join(macRepo, fileName)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		jsonData, _ := common.RestToJson(w, common.Error("File not found for os version : "+osVersion, http.StatusNotFound))
		w.WriteHeader(http.StatusNotFound)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
			return
		}
		return
	}

	//Get FileInfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file for os version : "+osVersion, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
			return
		}
		return
	}

	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(fileName)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	file, err := os.Open(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error opening file for os version : "+osVersion, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
			return
		}
		return
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
		}
	}(file)

	_, err = io.Copy(w, file)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadMacDistFiles]", err)
		return
	}
}
