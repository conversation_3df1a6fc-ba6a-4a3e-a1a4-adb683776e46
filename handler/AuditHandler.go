package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"net/http"
)

type AuditHandler struct {
	Service *service.AuditService
}

func NewAuditHandler() *AuditHandler {
	return &AuditHandler{
		Service: service.NewAuditService(),
	}
}

func (handler AuditHandler) GetAllAuditByEventModelHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	auditRest, err := service.NewAuditService().GetAllByAuditEventModel(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAuditByEventModelHandler]", err.Error())
		}
		return
	}

	jsonData, _ := common.RestToJson(w, auditRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAuditByEventModelHandler]", err.Error())
	}
}
