package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type AgentTaskHandler struct {
	Service *service.AgentTaskService
}

func NewAgentTaskHandler() *AgentTaskHandler {
	return &AgentTaskHandler{
		Service: service.NewAgentTaskService(),
	}
}

func (handler AgentTaskHandler) GetAgentTaskHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	agentTaskRest, err := handler.Service.GetAgentTaskById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAgentTaskHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, agentTaskRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAgentTaskHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) GetAgentTask(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	agentTaskRest, err := handler.Service.GetAgentTaskById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusNotFound))
		w.WriteHeader(http.StatusNotFound)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAgentTask]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": agentTaskRest,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAgentTask]", err)
		return
	}
}

func (handler AgentTaskHandler) CreateAgentTaskHandler(w http.ResponseWriter, r *http.Request) {
	var agentTaskRest rest.AgentTaskRest
	agentTaskRest, err := convertJsonToAgentTaskRest(w, r, agentTaskRest)
	if err != nil {
		return
	}

	//TODO Handle Before Create

	id, err := handler.Service.Create(agentTaskRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating deployment : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAgentTaskHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAgentTaskHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) GetAgentTaskByPkgIdsHandler(w http.ResponseWriter, r *http.Request) {
	var requestByIds rest.RequestByIdsAndModel
	requestByIds, err := service.ConvertJsonToRequestByIdsRest(w, r, requestByIds)
	if err != nil {
		return
	}
	agentTaskRest, err := handler.Service.GetAgentTaskByRefIdsAndRefModel(requestByIds)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAgentTaskByPkgIdsHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, agentTaskRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAgentTaskByPkgIdsHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) GetAgentTaskByDeploymentIdsHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}
	agentTaskRest, err := handler.Service.GetAllAgentTaskBySearch(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAgentTaskByDeploymentIdsHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, agentTaskRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAgentTaskByDeploymentIdsHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) GetAllAgentTasksFromViewHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}
	agentTaskRest, err := handler.Service.GetAllAgentTaskByView(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAgentTasksFromViewHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, agentTaskRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAgentTasksFromViewHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) UpdateAgentTaskHandler(w http.ResponseWriter, r *http.Request) {
	var agentTaskRest rest.AgentTaskRest
	agentTaskRest, err := convertJsonToAgentTaskRest(w, r, agentTaskRest)
	if err != nil {
		return
	}

	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	isUpdated, customErr := handler.Service.Update(id, agentTaskRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateAgentTaskHandler]", err)
			return
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateAgentTaskHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) GetAllAgentTasksHandler(w http.ResponseWriter, r *http.Request) {
	var offset = 0
	var size = 0
	var agentId int64 = 0

	agentIdParam := r.URL.Query().Get("agentId")
	if agentIdParam != "" {
		var err error
		agentId, err = common.ConvertToLong(agentIdParam)
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[GetAllAgentTasksHandler]", err)
				return
			}
			return
		}
	}

	offsetParam := r.URL.Query().Get("offset")
	if offsetParam != "" {
		var err error
		offset, err = common.ConvertToInt(offsetParam)
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[GetAllAgentTasksHandler]", err)
				return
			}
			return
		}
	}

	sizeParam := r.URL.Query().Get("size")
	if sizeParam != "" {
		var err error
		size, err = common.ConvertToInt(sizeParam)
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[GetAllAgentTasksHandler]", err)
				return
			}
			return
		}
	}

	agentTaskRest, err := handler.Service.GetAllAgentTasks(0, agentId, offset, size)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAgentTasksHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, agentTaskRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAgentTasksHandler]", err)
		return
	}
}

func (handler AgentTaskHandler) BulkUpdateAgentTaskHandler(w http.ResponseWriter, r *http.Request) {
	var bulkUpdateRequest rest.BulkUpdateRequest
	bulkActionResponse := rest.BulkActionResponse{}
	updateRequest, err := service.ConvertJsonToBulkUpdateRequest(w, r, bulkUpdateRequest)
	if err != nil {
		return
	}

	var agentTaskRest rest.AgentTaskRest
	payload, _ := json.Marshal(updateRequest.PatchMap)
	agentTaskRest, err = convertToAgentTask(w, payload, agentTaskRest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range updateRequest.Ids {
		isUpdated, customErr := handler.Service.Update(objId, agentTaskRest)
		if customErr.Message != "" {
			failedIdMap[objId] = customErr.Message
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkUpdateAgentTaskHandler]", err)
		return
	}
}

func convertJsonToAgentTaskRest(w http.ResponseWriter, r *http.Request, agentTaskRest rest.AgentTaskRest) (rest.AgentTaskRest, error) {
	body := common.GetRequestBody(r)
	return convertToAgentTask(w, body, agentTaskRest)
}

func convertToAgentTask(w http.ResponseWriter, body []byte, agentTaskRest rest.AgentTaskRest) (rest.AgentTaskRest, error) {
	err := json.Unmarshal(body, &agentTaskRest)
	v := validator.New()
	err = v.Struct(agentTaskRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertToAgentTask]", err)
				return rest.AgentTaskRest{}, err
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertToAgentTask]", err)
		return rest.AgentTaskRest{}, err
	}
	agentTaskRest.PatchMap = patchMap
	return agentTaskRest, err
}
