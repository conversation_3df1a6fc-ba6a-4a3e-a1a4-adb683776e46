package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/go-playground/validator/v10"
)

type PatchDeclinePolicyHandler struct {
	service *service.PatchDeclinePolicyService
}

func NewPatchDeclinePolicyHandler() *PatchDeclinePolicyHandler {
	return &PatchDeclinePolicyHandler{
		service: service.NewPatchDeclinePolicyService(),
	}
}

func (handler *PatchDeclinePolicyHandler) GetPatchDeclinePolicyHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPatchDeclinePolicyHandler]", err)
		}
		return
	}

	policy, err := handler.service.GetById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPatchDeclinePolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: policy})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetPatchDeclinePolicyHandler]", err)
	}
}

func (handler *PatchDeclinePolicyHandler) CreatePatchDeclinePolicyHandler(w http.ResponseWriter, r *http.Request) {
	var policyRest rest.PatchDeclinePolicyRest
	policyRest, err := convertJsonToPatchDeclinePolicyRest(w, r, policyRest)
	if err != nil {
		return
	}

	id, err := handler.service.Create(policyRest)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePatchDeclinePolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreatePatchDeclinePolicyHandler]", err)
	}
}

func (handler *PatchDeclinePolicyHandler) UpdatePatchDeclinePolicyHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchDeclinePolicyHandler]", err)
		}
		return
	}

	var policyRest rest.PatchDeclinePolicyRest
	policyRest, err := convertJsonToPatchDeclinePolicyRest(w, r, policyRest)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, err)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchDeclinePolicyHandler]", err)
		}
		return
	}

	isUpdated, updateError := handler.service.Update(id, policyRest)
	if updateError.Message != "" {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, updateError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchDeclinePolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: isUpdated})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdatePatchDeclinePolicyHandler]", err)
	}
}

func (handler *PatchDeclinePolicyHandler) DeletePatchDeclinePolicyHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePatchDeclinePolicyHandler]", err)
		}
		return
	}

	success, err := handler.service.DeleteById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePatchDeclinePolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: success})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeletePatchDeclinePolicyHandler]", err)
	}
}

func (handler *PatchDeclinePolicyHandler) GetAllPatchDeclinePoliciesHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchDeclinePoliciesHandler]", err)
		}
		return
	}

	policies, err := handler.service.GetAllPolicies(searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchDeclinePoliciesHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, policies)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPatchDeclinePoliciesHandler]", err)
	}
}

func convertJsonToPatchDeclinePolicyRest(w http.ResponseWriter, r *http.Request, policyRest rest.PatchDeclinePolicyRest) (rest.PatchDeclinePolicyRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &policyRest)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToPatchDeclinePolicyRest]", err)
		}
		return policyRest, err
	}

	v := validator.New()
	err = v.Struct(policyRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			w.WriteHeader(http.StatusBadRequest)
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusBadRequest))
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToPatchDeclinePolicyRest]", err)
			}
			break
		}
		return policyRest, err
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToPatchDeclinePolicyRest]", err)
		}
		return policyRest, err
	}
	policyRest.PatchMap = patchMap
	return policyRest, nil
}
