package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type SystemActionHandler struct {
	service *service.SystemActionService
}

func NewSystemActionHandler() *SystemActionHandler {
	return &SystemActionHandler{
		service: service.NewSystemActionService(),
	}
}

func (handler SystemActionHandler) GetSystemActionHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)

	action, err := handler.service.GetById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetSystemActionHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: action})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetSystemActionHandler]", err)
	}
}

func (handler SystemActionHandler) CreateSystemActionHandler(w http.ResponseWriter, r *http.Request) {
	var actionRest rest.SystemActionRest
	actionRest, err := convertJsonToSystemActionRest(w, r, actionRest)
	if err != nil {
		return
	}

	id, err := handler.service.Create(actionRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateSystemActionHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateSystemActionHandler]", err)
	}
}

func (handler SystemActionHandler) UpdateSystemActionHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	var actionRest rest.SystemActionRest
	actionRest, err := convertJsonToSystemActionRest(w, r, actionRest)
	if err != nil {
		return
	}

	isUpdated, updateErr := handler.service.Update(id, actionRest)
	if updateErr.Message != "" {
		jsonData, _ := common.RestToJson(w, updateErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateSystemActionHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateSystemActionHandler]", err)
	}
}

func (handler SystemActionHandler) DeleteSystemActionHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)

	success, err := handler.service.DeleteById(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteSystemActionHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteSystemActionHandler]", err)
	}
}

func (handler SystemActionHandler) GetAllSystemActionsHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pkgRest, err := handler.service.GetAllSystemActions(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllSystemActionsHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllSystemActionsHandler]", err)
	}
}

func convertJsonToSystemActionRest(w http.ResponseWriter, r *http.Request, sysActionRest rest.SystemActionRest) (rest.SystemActionRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &sysActionRest)
	v := validator.New()
	err = v.Struct(sysActionRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToSystemActionRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		return rest.SystemActionRest{}, err
	}
	sysActionRest.PatchMap = patchMap
	return sysActionRest, err
}
