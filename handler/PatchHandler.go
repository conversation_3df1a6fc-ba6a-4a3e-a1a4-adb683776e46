package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
)

type PatchHandler struct {
	service *service.PatchService
}

func NewPatchHandler() *PatchHandler {
	return &PatchHandler{
		service: service.NewPatchService(),
	}
}

func (handler PatchHandler) GetPatchHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting patch with id : "+idString, http.StatusInternalServerError))
		w.Write<PERSON>eader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPatchHandler]", err.Error())
			return
		}
		return
	}

	intValue, err := strconv.ParseInt(idString, 10, 64)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid patch id : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPatchHandler]", err.Error())
			return
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	patchRest, err := handler.service.GetPatch(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPatchHandler]", err.Error())
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: patchRest})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetPatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) CreatePatchHandler(w http.ResponseWriter, r *http.Request) {
	var patchRest rest.PatchRest
	patchRest, err := rest.ConvertJsonToPatchRest(w, r, patchRest)
	if err != nil {
		return
	}
	patchRest.Source = model.Manually.String()
	id, createErr := handler.service.Create(patchRest, model.Missing)
	if createErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating patch : "+createErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePatchHandler]", err.Error())
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreatePatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) UpdatePatchHandler(w http.ResponseWriter, r *http.Request) {
	var patchRest rest.PatchRest
	patchRest, err := rest.ConvertJsonToPatchRest(w, r, patchRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting patch with id : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchHandler]", err.Error())
			return
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchHandler]", err.Error())
			return
		}
		return
	}

	isUpdated, customErr := handler.service.Update(id, patchRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdatePatchHandler]", err.Error())
			return
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdatePatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) DeletePatchHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting patch with id : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePatchHandler]", err.Error())
			return
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid patch id : "+idString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePatchHandler]", err.Error())
			return
		}
		return
	}

	success, err := handler.service.DeletePatch(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePatchHandler]", err.Error())
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeletePatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) GetAllPatchHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	patchRest, err := handler.service.GetAllPatchOptimized(searchFilter, true, true)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchHandler]", err.Error())
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, patchRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) GetAllPatchByPatchStateHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	vars := mux.Vars(r)
	patchStateString, _ := vars["patch_state"]
	if patchStateString == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting patchStateString from api path  : "+patchStateString, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchByPatchStateHandler]", err.Error())
			return
		}
		return
	}
	if searchFilter.Size == 0 {
		searchFilter.Size = 20
	}

	if searchFilter.Qualification != nil {
		searchFilter.Qualification = append(searchFilter.Qualification, rest.Qualification{Column: "patch_state", Operator: "equals", Value: patchStateString, Condition: "and", Type: "enum", Reference: "patchState"})
	}

	patchRest, err := handler.service.GetAllPatchOptimized(searchFilter, true, true)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchHandler]", err.Error())
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, patchRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPatchHandler]", err.Error())
		return
	}

	/*relationList, err := service.NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: qualifications})
	if err == nil && len(relationList) > 0 {
		var pchIds []int64
		for _, relation := range relationList {
			pchIds = append(pchIds, relation.PatchId)
		}

		var patchQualifications []rest.Qualification
		patchQualifications = append(patchQualifications, rest.Qualification{
			Column:    "id",
			Operator:  "in",
			Value:     pchIds,
			Condition: "and",
		})

		patchQualifications = append(patchQualifications, searchFilter.Qualification...)
		searchFilter.Qualification = patchQualifications

		patchRest, err := handler.service.GetAllPatch(searchFilter, false)
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err = fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[GetAllPatchByPatchStateHandler]", err.Error())
				return
			}
			return
		}

		jsonData, _ := common.RestToJson(w, patchRest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchByPatchStateHandler]", err.Error())
			return
		}
	} else {
		jsonData, _ := common.RestToJson(w, rest.ListResponseRest{})
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchByPatchStateHandler]", err.Error())
			return
		}
	}*/
}

func (handler PatchHandler) BulkDeletePatchHandler(w http.ResponseWriter, r *http.Request) {
	var bulkDeleteRequest rest.BulkDeleteRequest
	bulkActionResponse := rest.BulkActionResponse{}
	deleteRequest, err := service.ConvertJsonToBulkDeleteRequest(w, r, bulkDeleteRequest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range deleteRequest.Ids {
		isUpdated, err := handler.service.DeletePatch(objId)
		if err != nil {
			failedIdMap[objId] = err.Error()
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeletePatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) DeleteAllPatchWithRelationHandler(w http.ResponseWriter, r *http.Request) {
	_, err := service.NewPatchService().Repository.DeleteAll()
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAllPatchWithRelationHandler]", err.Error())
	}
	_, err = service.NewAssetPatchRelationService().Repository.DeleteAll()
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAllPatchWithRelationHandler]", err.Error())
	}
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAllPatchWithRelationHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) GetSupersededPatchHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	uuid, _ := vars["uuid"]
	supersededList := service.NewPatchService().GetSupersededPatchListByPchId(uuid)
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": supersededList,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetSupersededPatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) GetSupersededByPatchHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	uuid, _ := vars["uuid"]
	supersededList := service.NewPatchService().GetSupersededByPatchListByPchId(uuid)
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": supersededList,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetSupersededByPatchHandler]", err.Error())
		return
	}
}

func (handler PatchHandler) UpdateDownloadStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	patchName, _ := vars["id"]
	var patchFileDownloadStatus model.PatchFileDownloadStatus
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &patchFileDownloadStatus)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDownloadStatus]", err.Error())
			return
		}
		return
	}

	service.NewPatchService().UpdatePatchDownloadStatus(patchName, patchFileDownloadStatus)
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateDownloadStatus]", err.Error())
		return
	}
}
