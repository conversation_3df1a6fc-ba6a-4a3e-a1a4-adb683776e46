package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/service"
	"fmt"
	"net/http"
)

type PatchSyncHandler struct {
	Service *service.PatchSyncService
}

func NewPatchSyncHandler() *PatchSyncHandler {
	return &PatchSyncHandler{Service: service.NewPatchSyncService()}
}

func (handler <PERSON>SyncHandler) ExecutePatchSync(w http.ResponseWriter, r *http.Request) {
	pchPreference, _ := service.NewPatchPreferenceService().Get()
	if pchPreference.EnablePatching || pchPreference.EnableThirdPartyPatching {
		go handler.Service.ExecutePatchSync()
	}
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[ExecutePatchSync]", err)
	}
}
