package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
	"net/http"
	"strconv"
)

type DeploymentHandler struct {
	Service *service.DeploymentService
}

func NewDeploymentHandler() *DeploymentHandler {
	return &DeploymentHandler{
		Service: service.NewDeploymentService(),
	}
}

func (handler DeploymentHandler) GetDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting deployment with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHandler]", err)
		}
		return
	}

	intValue, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid deployment id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHandler]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	deploymentRest, err := service.NewDeploymentService().GetDeployment(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, deploymentRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetDeploymentHandler]", err)
	}
}

func (handler DeploymentHandler) CreateDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	var deploymentRest rest.DeploymentRest
	deploymentRest, err := convertJsonToDeploymentRest(w, r, deploymentRest)
	if err != nil {
		return
	}

	//TODO Handle Before Create

	id, err := service.NewDeploymentService().Create(deploymentRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating deployment : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateDeploymentHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateDeploymentHandler]", err)
	}
}

func (handler DeploymentHandler) UpdateDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	var deploymentRest rest.DeploymentRest
	deploymentRest, err := convertJsonToDeploymentRest(w, r, deploymentRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting deployment with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentHandler]", err)
		}
		return
	}

	isUpdated, customErr := service.NewDeploymentService().Update(id, deploymentRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateDeploymentHandler]", err)
	}
}

func (handler DeploymentHandler) DeleteDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting deployment with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid deployment id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentHandler]", err)
		}
		return
	}

	success, err := service.NewDeploymentService().DeleteDeployment(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteDeploymentHandler]", err)
	}
}

func (handler DeploymentHandler) GetAllDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	deploymentRest, err := service.NewDeploymentService().GetAllDeployment(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllDeploymentHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, deploymentRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllDeploymentHandler]", err)
	}
}

func (handler DeploymentHandler) GetDeploymentHitMapHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting deployment hit map with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHitMapHandler]", err)
		}
		return
	}

	intValue, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid deployment id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHitMapHandler]", err)
		}
		return
	}

	var searchFilter rest.SearchFilter
	searchFilter, err = rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid filter ", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHitMapHandler]", err)
		}
		return
	}

	hitMap, err := service.NewDeploymentService().GetDeploymentHeatMap(intValue, searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentHitMapHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, hitMap)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetDeploymentHitMapHandler]", err)
	}

}

func convertJsonToDeploymentRest(w http.ResponseWriter, r *http.Request, deploymentRest rest.DeploymentRest) (rest.DeploymentRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &deploymentRest)
	v := validator.New()
	err = v.Struct(deploymentRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToDeploymentRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertJsonToDeploymentRest]", err)
	}
	deploymentRest.PatchMap = patchMap
	return deploymentRest, err
}
