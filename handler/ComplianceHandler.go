package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

type ComplianceHandler struct {
	Service *service.ComplianceService
}

func NewComplianceHandler() *ComplianceHandler {
	return &ComplianceHandler{
		Service: service.NewComplianceService(),
	}
}

func (handler ComplianceHandler) GetCompliance(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting Compliance with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetCompliance]", err)
		}
		return
	}

	intValue, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid Compliance id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetCompliance]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	ComplianceRest, err := service.NewComplianceService().GetCompliance(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetCompliance]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: ComplianceRest})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetCompliance]", err)
	}
}

func (handler ComplianceHandler) CreateCompliance(w http.ResponseWriter, r *http.Request) {
	var complianceRest rest.ComplianceRest
	complianceRest, err := convertJsonToComplianceRest(w, r, complianceRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating Compliance : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateCompliance]", err)
		}
		return
	}

	id, err := service.NewComplianceService().Create(complianceRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating Compliance : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateCompliance]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateCompliance]", err)
	}
}

func (handler ComplianceHandler) TestCompliance(w http.ResponseWriter, r *http.Request) {
	var complianceRest map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &complianceRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while test Compliance : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[TestCompliance]", err)
		}
		return
	}

	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}

	agentTask := rest.AgentTaskRest{}
	agentTask.AgentId = assetId
	agentTask.TaskStatus = model.TaskReadyToDeploy.String()
	agentTask.TaskType = model.COMPLIANCE_TEST.String()
	agentTask.CustomTaskDetails = complianceRest
	taskId, _ := NewAgentTaskHandler().Service.Create(agentTask)

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": map[string]interface{}{
			"id": taskId,
		},
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[TestCompliance]", err)
	}
}

func (handler ComplianceHandler) UpdateCompliance(w http.ResponseWriter, r *http.Request) {
	var complianceRest rest.ComplianceRest
	complianceRest, err := convertJsonToComplianceRest(w, r, complianceRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting Compliance with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateCompliance]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateCompliance]", err)
		}
		return
	}

	isUpdated, customErr := service.NewComplianceService().Update(id, complianceRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateCompliance]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateCompliance]", err)
	}
}

func (handler ComplianceHandler) BulkDeleteCompliance(w http.ResponseWriter, r *http.Request) {
	var bulkDeleteRequest rest.BulkDeleteRequest
	bulkActionResponse := rest.BulkActionResponse{}
	deleteRequest, err := service.ConvertJsonToBulkDeleteRequest(w, r, bulkDeleteRequest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range deleteRequest.Ids {
		isUpdated, err := handler.Service.DeleteCompliance(objId, deleteRequest.IsPermanentDelete)
		if err != nil {
			failedIdMap[objId] = err.Error()
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeleteCompliance]", err)
	}
}

func (handler ComplianceHandler) DeleteCompliance(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting Compliance with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteCompliance]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid Compliance id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteCompliance]", err)
		}
		return
	}

	success, err := service.NewComplianceService().DeleteCompliance(id, false)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteCompliance]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteCompliance]", err)
	}
}

func (handler ComplianceHandler) GetAllCompliance(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	complianceRest, err := service.NewComplianceService().GetAllCompliance(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllCompliance]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, complianceRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllCompliance]", err)
	}
}

func convertJsonToComplianceRest(w http.ResponseWriter, r *http.Request, complianceRest rest.ComplianceRest) (rest.ComplianceRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &complianceRest)
	v := validator.New()
	err = v.Struct(complianceRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToComplianceRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		return rest.ComplianceRest{}, err
	}
	complianceRest.PatchMap = patchMap
	return complianceRest, err
}
