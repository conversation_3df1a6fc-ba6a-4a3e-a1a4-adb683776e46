package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

type PackageHandler struct {
	service *service.PackageService
}

func NewPackageHandler() *PackageHandler {
	return &PackageHandler{
		service: service.NewPackageService(),
	}
}

func (handler PackageHandler) GetPackageHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting package with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPackageHandler]", err)
		}
		return
	}

	intValue, err := strconv.ParseInt(idString, 10, 64)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid package id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPackageHandler]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	pkgRest, err := handler.service.GetPackage(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetPackageHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetPackageHandler]", err)
	}
}

func (handler PackageHandler) CreatePackageHandler(w http.ResponseWriter, r *http.Request) {
	var pkgRest rest.PackageRest
	pkgRest, err := convertJsonToPackageRest(w, r, pkgRest)
	if err != nil {
		return
	}

	id, customErr := handler.service.Create(pkgRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating package : "+customErr.Message, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePackageHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreatePackageHandler]", err)
	}
}

func (handler PackageHandler) UpdatePackageHandler(w http.ResponseWriter, r *http.Request) {
	var pkgRest rest.PackageRest
	pkgRest, err := convertJsonToPackageRest(w, r, pkgRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting package with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePackageHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePackageHandler]", err)
		}
		return
	}

	isUpdated, customErr := handler.service.Update(id, pkgRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePackageHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreatePackageHandler]", err)
	}
}

func (handler PackageHandler) DeletePackageHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting package with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePackageHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid package id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePackageHandler]", err)
		}
		return
	}

	var permanentDelete = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("permanentDelete")); err == nil {
		permanentDelete = isVisibleParam
	}

	success, err := handler.service.DeletePackage(id, permanentDelete)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeletePackageHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeletePackageHandler]", err)
	}
}

func (handler PackageHandler) GetAllPackageHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPackageHandler]", err)
		}
		return
	}

	pkgRest, err := handler.service.GetAllPackage(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPackageHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPackageHandler]", err)
	}
}

func (handler PackageHandler) BulkDeletePackageHandler(w http.ResponseWriter, r *http.Request) {
	var bulkDeleteRequest rest.BulkDeleteRequest
	bulkActionResponse := rest.BulkActionResponse{}
	deleteRequest, err := service.ConvertJsonToBulkDeleteRequest(w, r, bulkDeleteRequest)
	if err != nil {
		return
	}
	var successIds []int64
	failedIdMap := map[int64]string{}
	for _, objId := range deleteRequest.Ids {
		isUpdated, err := handler.service.DeletePackage(objId, deleteRequest.IsPermanentDelete)
		if err != nil {
			failedIdMap[objId] = err.Error()
			continue
		}
		if isUpdated {
			successIds = append(successIds, objId)
		}
	}

	bulkActionResponse.FailedIdMap = failedIdMap
	bulkActionResponse.SuccessIds = successIds

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": bulkActionResponse,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[BulkDeletePackageHandler]", err)
	}
}

func (handler PackageHandler) GetTagFilterHandler(w http.ResponseWriter, r *http.Request) {
	response := handler.service.GetTagFilterWithCount()
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetTagFilterHandler]", err)
	}
}

func convertJsonToPackageRest(w http.ResponseWriter, r *http.Request, pkgRest rest.PackageRest) (rest.PackageRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &pkgRest)
	v := validator.New()
	err = v.Struct(pkgRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToPackageRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertJsonToPackageRest]", err)
	}
	pkgRest.PatchMap = patchMap
	return pkgRest, err
}
