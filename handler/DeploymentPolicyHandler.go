package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

type DeploymentPolicyHandler struct {
	service *service.DeploymentPolicyService
}

func NewDeploymentPolicyHandler() *DeploymentPolicyHandler {
	return &DeploymentPolicyHandler{
		service: service.NewDeploymentPolicyService(),
	}
}

func (handler DeploymentPolicyHandler) GetDeploymentPolicyHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting deployment policy with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentPolicyHandler]", err)
		}
		return
	}

	intValue, err := strconv.ParseInt(idString, 10, 64)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid deployment policy id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentPolicyHandler]", err)
		}
		return
	}

	var includeArchive = false
	if isVisibleParam, err := strconv.ParseBool(r.URL.Query().Get("includeArchive")); err == nil {
		includeArchive = isVisibleParam
	}

	policyRest, err := service.NewDeploymentPolicyService().GetDeploymentPolicy(intValue, includeArchive)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetDeploymentPolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, policyRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetDeploymentPolicyHandler]", err)
	}
}

func (handler DeploymentPolicyHandler) CreateDeploymentPolicyHandler(w http.ResponseWriter, r *http.Request) {
	var policyRest rest.DeploymentPolicyRest
	policyRest, err := convertJsonToDeploymentPolicyRest(w, r, policyRest)
	if err != nil {
		return
	}

	//TODO Add Before create

	id, err := service.NewDeploymentPolicyService().Create(policyRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating deployment policy: "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateDeploymentPolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: true,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateDeploymentPolicyHandler]", err)
	}
}

func (handler DeploymentPolicyHandler) UpdateDeploymentPolicyHandler(w http.ResponseWriter, r *http.Request) {
	var policyRest rest.DeploymentPolicyRest
	policyRest, err := convertJsonToDeploymentPolicyRest(w, r, policyRest)
	if err != nil {
		return
	}

	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting deployment policy with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentPolicyHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentPolicyHandler]", err)
		}
		return
	}

	isUpdated, customErr := service.NewDeploymentPolicyService().Update(id, policyRest)
	if customErr.Message != "" {
		jsonData, _ := common.RestToJson(w, customErr)
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateDeploymentPolicyHandler]", err)
		}
		return
	}
	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: isUpdated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateDeploymentPolicyHandler]", err)
	}
}

func (handler DeploymentPolicyHandler) DeleteDeploymentPolicyHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while deleting deployment policy with id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentPolicyHandler]", err)
		}
		return
	}

	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid deployment policy id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentPolicyHandler]", err)
		}
		return
	}

	success, err := service.NewDeploymentPolicyService().DeleteDeploymentPolicy(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteDeploymentPolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{
		Id:      id,
		Success: success,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteDeploymentPolicyHandler]", err)
	}
}

func convertJsonToDeploymentPolicyRest(w http.ResponseWriter, r *http.Request, policyRest rest.DeploymentPolicyRest) (rest.DeploymentPolicyRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &policyRest)
	v := validator.New()
	err = v.Struct(policyRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToDeploymentPolicyRest]", err)
			}
			break
		}
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertJsonToDeploymentPolicyRest]", err)
	}
	policyRest.PatchMap = patchMap
	return policyRest, err
}

func (handler DeploymentPolicyHandler) GetAllDeploymentPolicyHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	policyRest, err := service.NewDeploymentPolicyService().GetAllDeploymentPolicy(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllDeploymentPolicyHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, policyRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllDeploymentPolicyHandler]", err)
	}
}
