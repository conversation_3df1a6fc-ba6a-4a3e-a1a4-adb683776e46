package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"fmt"
	"net/http"
)

type PatchOsApplicationHandler struct {
	Service *service.PatchOsApplicationService
}

func NewPatchOsApplicationHandler() *PatchOsApplicationHandler {
	return &PatchOsApplicationHandler{
		Service: service.NewPatchOsApplicationService(),
	}
}

func (handler PatchOsApplicationHandler) CreateHandler(w http.ResponseWriter, r *http.Request) {
	var osApplicationRest rest.PatchOsApplicationRest
	osApplicationRest, err := rest.ConvertJsonToPatchOsApplicationRest(w, r, osApplicationRest)
	if err != nil {
		return
	}

	id, err := handler.Service.CreatePatchOsApplication(osApplicationRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while creating package : "+err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateHandler]", err)
	}
}

func (handler PatchOsApplicationHandler) GetAllPatchOsApplication(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pkgRest, err := handler.Service.GetAllPatchOsApplications(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllPatchOsApplication]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pkgRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllPatchOsApplication]", err)
	}
}
