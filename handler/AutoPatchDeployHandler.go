package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/go-playground/validator/v10"
)

type AutoPatchDeployHandler struct {
	service *service.AutoPatchDeployService
}

func NewAutoPatchDeployHandler() *AutoPatchDeployHandler {
	return &AutoPatchDeployHandler{
		service: service.NewAutoPatchDeployService(),
	}
}
func (handler *AutoPatchDeployHandler) GetAutoPatchDeployHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAutoPatchDeployHandler]", err)
		}
		return
	}

	deploy, err := handler.service.GetById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAutoPatchDeployHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: deploy})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAutoPatchDeployHandler]", err)
	}
}

func (handler *AutoPatchDeployHandler) CreateAutoPatchDeployHandler(w http.ResponseWriter, r *http.Request) {
	var deployRest rest.AutoPatchDeployRest
	deployRest, err := convertJsonToAutoPatchDeployRest(w, r, deployRest)
	if err != nil {
		return
	}

	id, err := handler.service.Create(deployRest)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAutoPatchDeployHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAutoPatchDeployHandler]", err)
	}
}

func (handler *AutoPatchDeployHandler) UpdateAutoPatchDeployHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateAutoPatchDeployHandler]", err)
		}
		return
	}

	var deployRest rest.AutoPatchDeployRest
	deployRest, err := convertJsonToAutoPatchDeployRest(w, r, deployRest)
	if err != nil {
		return
	}

	isUpdated, updateError := handler.service.Update(id, deployRest)
	if updateError.Message != "" {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, updateError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateAutoPatchDeployHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: isUpdated})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateAutoPatchDeployHandler]", err)
	}
}

func (handler *AutoPatchDeployHandler) DeleteAutoPatchDeployHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteAutoPatchDeployHandler]", err)
		}
		return
	}

	success, err := handler.service.DeleteById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteAutoPatchDeployHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: success})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAutoPatchDeployHandler]", err)
		return
	}
}

func (handler *AutoPatchDeployHandler) GetAllAutoPatchDeploysHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAutoPatchDeploysHandler]", err)
		}
		return
	}

	deploys, err := handler.service.GetAllDeploys(searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAutoPatchDeploysHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, deploys)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAutoPatchDeploysHandler]", err)
	}
}

func convertJsonToAutoPatchDeployRest(w http.ResponseWriter, r *http.Request, deployRest rest.AutoPatchDeployRest) (rest.AutoPatchDeployRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &deployRest)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToAutoPatchDeployRest]", err)
		}
		return deployRest, err
	}

	v := validator.New()
	err = v.Struct(deployRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			w.WriteHeader(http.StatusBadRequest)
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values: %s", err.Field(), err.Param()), http.StatusBadRequest))
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToAutoPatchDeployRest]", err)
			}
			break
		}
		return deployRest, err
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToAutoPatchDeployRest]", err)
		}
		return deployRest, err
	}
	deployRest.PatchMap = patchMap
	return deployRest, nil
}
