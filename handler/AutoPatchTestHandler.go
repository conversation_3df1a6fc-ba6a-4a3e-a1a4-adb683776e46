package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/go-playground/validator/v10"
)

type AutoPatchTestHandler struct {
	service *service.AutoPatchTestService
}

func NewAutoPatchTestHandler() *AutoPatchTestHandler {
	return &AutoPatchTestHandler{
		service: service.NewAutoPatchTestService(),
	}
}

func (handler *AutoPatchTestHandler) GetAutoPatchTestHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAutoPatchTestHandler]", err)
		}
		return
	}

	test, err := handler.service.GetById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAutoPatchTestHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.ResponseRest{Object: test})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAutoPatchTestHandler]", err)
	}
}

func (handler *AutoPatchTestHandler) CreateAutoPatchTestHandler(w http.ResponseWriter, r *http.Request) {
	var testRest rest.AutoPatchTestRest
	testRest, err := convertJsonToAutoPatchTestRest(w, r, testRest)
	if err != nil {
		return
	}

	id, err := handler.service.Create(testRest)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreateAutoPatchTestHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: true})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[CreateAutoPatchTestHandler]", err)
	}
}

func (handler *AutoPatchTestHandler) UpdateAutoPatchTestHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateAutoPatchTestHandler]", err)
		}
		return
	}

	var testRest rest.AutoPatchTestRest
	testRest, err := convertJsonToAutoPatchTestRest(w, r, testRest)
	if err != nil {
		return
	}

	isUpdated, updateError := handler.service.Update(id, testRest)
	if updateError.Message != "" {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, updateError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateAutoPatchTestHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: isUpdated})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateAutoPatchTestHandler]", err)
	}
}

func (handler *AutoPatchTestHandler) DeleteAutoPatchTestHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid ID", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteAutoPatchTestHandler]", err)
		}
		return
	}

	success, err := handler.service.DeleteById(id)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[DeleteAutoPatchTestHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, rest.IdResponseRest{Id: id, Success: success})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[DeleteAutoPatchTestHandler]", err)
	}
}

func (handler *AutoPatchTestHandler) GetAllAutoPatchTestsHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAutoPatchTestsHandler]", err)
		}
		return
	}

	tests, err := handler.service.GetAllTests(searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllAutoPatchTestsHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, tests)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllAutoPatchTestsHandler]", err)
	}
}

func convertJsonToAutoPatchTestRest(w http.ResponseWriter, r *http.Request, testRest rest.AutoPatchTestRest) (rest.AutoPatchTestRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &testRest)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToAutoPatchTestRest]", err)
		}
		return testRest, err
	}

	v := validator.New()
	err = v.Struct(testRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			w.WriteHeader(http.StatusBadRequest)
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values: %s", err.Field(), err.Param()), http.StatusBadRequest))
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[convertJsonToAutoPatchTestRest]", err)
			}
			break
		}
		return testRest, err
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		jsonData, _ := common.RestToJson(w, common.Error("Invalid request payload", http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[convertJsonToAutoPatchTestRest]", err)
		}
		return testRest, err
	}
	testRest.PatchMap = patchMap
	return testRest, nil
}
