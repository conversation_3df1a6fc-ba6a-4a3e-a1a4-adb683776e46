package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/service"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
)

type ThirdPartyPackageHandler struct {
	service *service.ThirdPartyPackageService
}

func NewThirdPartyPackageHandler() *ThirdPartyPackageHandler {
	return &ThirdPartyPackageHandler{
		service: service.NewThirdPartyPackageService(),
	}
}

// GetThirdPartyPackageMetadataHandler handles GET requests to retrieve third-party package metadata
func (handler *ThirdPartyPackageHandler) GetThirdPartyPackageMetadataHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	osType, _ := vars["osType"]
	if osType == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting package with id : "+osType, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CreatePackageHandler]", err)
		}
		return
	}
	logger.ServiceLogger.Info("Processing request to get third party package metadata ", osType)
	metadata, err := handler.service.GetThirdPartyPackageMetadata(osType)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting third party package metadata: %s", err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error("[GetThirdPartyPackageMetadataHandler]", writeErr)
		}
		return
	}

	// Return successful response
	jsonData, _ := common.RestToJson(w, metadata)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetThirdPartyPackageMetadataHandler]", err)
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Successfully returned %d third party package metadata records", len(metadata)))
}
