package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
	"reflect"
)

type DeploymentBundleService struct {
	Repository *repository.DeploymentBundleRepository
}

func NewDeploymentBundleService() *DeploymentBundleService {
	return &DeploymentBundleService{Repository: repository.NewDeploymentBundleRepository()}
}

func (service DeploymentBundleService) convertToModel(restModel rest.DeploymentBundleRest) (*model.DeploymentBundle, error) {
	var osType common.OsType
	var complianceType model.ComplianceType

	if restModel.Os != "" {
		osType = osType.ToOsType(restModel.Os)
	}

	complianceType = complianceType.ToComplianceType(restModel.ComplianceType)

	return &model.DeploymentBundle{
		BaseEntityModel: ConvertToBaseEntityModel(restModel.BaseEntityRest),
		Description:     restModel.Description,
		ReferenceIds:    restModel.ReferenceIds,
		RefModel:        restModel.RefModel,
		Os:              osType,
		ComplianceType:  complianceType,
		IconFile:        rest.ConvertToFileMetaData(restModel.IconFile),
	}, nil
}

func (service DeploymentBundleService) convertToRest(domainModel model.DeploymentBundle) rest.DeploymentBundleRest {
	return rest.DeploymentBundleRest{
		BaseEntityRest: ConvertToBaseEntityRest(domainModel.BaseEntityModel),
		Description:    domainModel.Description,
		ReferenceIds:   domainModel.ReferenceIds,
		Os:             domainModel.Os.String(),
		RefModel:       domainModel.RefModel,
		ComplianceType: domainModel.ComplianceType.String(),
		IconFile:       rest.ConvertToFileMetaDataRest(domainModel.IconFile),
	}
}

func (service DeploymentBundleService) convertListToRest(bundles []model.DeploymentBundle) []rest.DeploymentBundleRest {
	var bundleRests []rest.DeploymentBundleRest
	if len(bundles) != 0 {
		for _, pkg := range bundles {
			bundleRest := service.convertToRest(pkg)
			bundleRests = append(bundleRests, bundleRest)
		}
	}
	return bundleRests
}

func (service DeploymentBundleService) Create(restModel rest.DeploymentBundleRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to create deployment bundle"))
	restModel.CreatedTime = common.CurrentMillisecond()
	restModel.CreatedById = common.GetUserFromCallContext()
	bundle, err := service.convertToModel(restModel)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating bundle ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(bundle)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating bundle ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Create package process completed successfully"))
	return id, common.CustomError{}
}

func (service DeploymentBundleService) Update(id int64, restModel rest.DeploymentBundleRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update bundle with id - %v", id))
	bundle, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating bundel for id - %v ,Error : %s ", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	bundle.UpdatedTime = common.CurrentMillisecond()
	bundle.UpdatedById = common.GetUserFromCallContext()
	_, isUpdatable := service.performPartialUpdate(&bundle, restModel)
	if isUpdatable {
		_, err := service.Repository.Update(&bundle)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating bundle for id - %v ,Error : %s ", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
	} else {
		logger.ServiceLogger.Info(fmt.Sprintf("No fields need to updated"))
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update bundle with id - %v", id))
	return isUpdatable, common.CustomError{}
}

func (service DeploymentBundleService) GetById(id int64, includeArchive bool) (rest.DeploymentBundleRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get bundle for id %v", id))
	var restModel rest.DeploymentBundleRest
	bundle, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get bundle for id - %v, Error : %s ", id, err.Error()))
		return restModel, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get bundle for id %v", id))
	return service.convertToRest(bundle), nil
}

func (service DeploymentBundleService) DeletePackage(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info("Process started to delete bundle for id - ", id)

	var err error
	success := false

	if permanentDelete {
		success, err = service.Repository.PermanentDeleteById(id)
		if err != nil {
			logger.ServiceLogger.Error("[DeploymentBundleService][DeletePackage]", err)
		}
		_, err = NewComplianceService().Repository.PermanentDeleteComplianceTaskResult(0, 0, 0, id)
		if err != nil {
			logger.ServiceLogger.Error("[DeploymentBundleService][DeletePackage]", err)
		}
	} else {
		success, err = service.Repository.DeleteById(id)
		if err != nil {
			logger.ServiceLogger.Error("[DeploymentBundleService][DeletePackage]", err)
		}
		_, err = NewComplianceService().Repository.PermanentDeleteComplianceTaskResult(0, 0, 0, id)
		if err != nil {
			logger.ServiceLogger.Error("[DeploymentBundleService][DeletePackage]", err)
		}
	}

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while delete bundle for id - %v, Error : %s ", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info("Process Completed to delete bundle for id - ", id)
	return true, nil
}

func (service DeploymentBundleService) performPartialUpdate(domainModel *model.DeploymentBundle, restModel rest.DeploymentBundleRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchMap["description"] != nil && domainModel.Description != restModel.Description {
		common.PrepareInDiffMap("description", domainModel.Description, restModel.Description, &diffMap)
		domainModel.Description = restModel.Description
	}

	if restModel.PatchMap["referenceIds"] != nil && !reflect.DeepEqual(domainModel.ReferenceIds, restModel.ReferenceIds) {
		common.PrepareInDiffMap("reference_ids", domainModel.ReferenceIds, restModel.ReferenceIds, &diffMap)
		domainModel.ReferenceIds = restModel.ReferenceIds
	}

	if restModel.PatchMap["complianceType"] != nil && restModel.ComplianceType != domainModel.ComplianceType.String() {
		common.PrepareInDiffMap("compliance_type", domainModel.ReferenceIds, restModel.ReferenceIds, &diffMap)
		domainModel.ReferenceIds = restModel.ReferenceIds
	}

	if _, ok := restModel.PatchMap["iconFile"]; ok && restModel.IconFile.RefName != domainModel.IconFile.RefName {
		common.PrepareInDiffMap("icon_file", domainModel.IconFile, restModel.IconFile, &diffMap)
		domainModel.IconFile = rest.ConvertToFileMetaData(restModel.IconFile)
	}
	return diffMap, len(diffMap) != 0
}

func (service DeploymentBundleService) GetAllBundles(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	queryCondition := ""
	qualifications := filter.Qualification
	if len(qualifications) > 1 {
		// need to handle this because search and ref model passed through qualifications
		var searchQualifications []rest.Qualification
		for _, qualification := range qualifications {
			if qualification.Column == "refModel" {
				queryCondition = "\"" + common.ToSnakeCase(qualification.Column) + "\" = '" + qualification.Value.(string) + "' "
			} else {
				searchQualifications = append(searchQualifications, qualification)
			}
		}
		filter.Qualification = searchQualifications
	}
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.DEPLOYMENT_BUNDLE.String(), true, queryCondition)
	var responsePage rest.ListResponseRest
	var bundlesList []model.DeploymentBundle
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.DEPLOYMENT_BUNDLE.String(), false, queryCondition)
		bundlesList, err = service.Repository.GetAllBundles(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(bundlesList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
