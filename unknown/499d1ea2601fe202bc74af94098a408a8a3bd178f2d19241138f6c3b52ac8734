package rest

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
	"strings"
	"time"
)

type SearchFilter struct {
	Offset          int             `json:"offset"`
	Size            int             `json:"size"`
	IncludeArchived bool            `json:"archived"`
	SortBy          string          `json:"sortBy"`
	GroupBy         string          `json:"groupBy"`
	Columns         string          `json:"columns"`
	Distinct        bool            `json:"distinct,omitempty"`        // Apply DISTINCT to all columns
	DistinctColumns string          `json:"distinctColumns,omitempty"` // Apply DISTINCT to specific columns
	Qualification   []Qualification `json:"qualification"`
	Timeline        Timeline        `json:"timeline"`
	Joins           []JoinClause    `json:"joins,omitempty"`
}

type JoinClause struct {
	Type      string `json:"type"`      // INNER, LEFT, RIGHT, FULL
	Table     string `json:"table"`     // Table name to join
	<PERSON><PERSON>     string `json:"alias"`     // Optional table alias
	Condition string `json:"condition"` // JOIN condition (e.g., "main.id = joined.main_id")
}

type Qualification struct {
	Column     string      `json:"column"`
	ColumnList []string    `json:"columns"`
	Operator   string      `json:"operator"`
	Value      interface{} `json:"value"`
	Condition  string      `json:"condition"`
	Type       string      `json:"type"`
	Reference  string      `json:"reference"`
}

type Timeline struct {
	Timeline string `json:"timeline"`
	From     string `json:"from"`
	To       string `json:"to"`
}

func BuildQualification(column, operator string, value interface{}, condition string) Qualification {
	return Qualification{
		Column:    column,
		Operator:  operator,
		Value:     value,
		Condition: condition,
	}
}

// BuildDistinctFilter creates a SearchFilter with DISTINCT support
func BuildDistinctFilter(columns string, distinct bool, distinctColumns string) SearchFilter {
	return SearchFilter{
		Columns:         columns,
		Distinct:        distinct,
		DistinctColumns: distinctColumns,
		IncludeArchived: false,
	}
}

// AddDistinct adds DISTINCT support to an existing SearchFilter
func (filter *SearchFilter) AddDistinct(distinct bool, distinctColumns string) {
	filter.Distinct = distinct
	filter.DistinctColumns = distinctColumns
}

func BuildEnumQualification(column, operator string, value interface{}, condition, refrence string) Qualification {
	return Qualification{
		Column:    column,
		Operator:  operator,
		Value:     value,
		Condition: condition,
		Type:      "enum",
		Reference: refrence,
	}
}

func PrepareTupleQueryFromSearchFilter(columnList []string, tableName string) string {
	query := "select "
	for i, col := range columnList {
		query += col
		if i != len(columnList)-1 {
			query += ", "
		}
	}
	query += " from deployment." + tableName
	return query
}

func PrepareQueryFromSearchFilter(filter SearchFilter, tableName string, isCountQual bool, queryCondition string) string {
	qualifications := filter.Qualification
	columnSelection := "*"
	if isCountQual {
		columnSelection = "count(1)"
	} else if filter.Columns != "" {
		columnSelection = filter.Columns
	}
	query := fmt.Sprintf("select %s from deployment.%s ", columnSelection, tableName)
	if !filter.IncludeArchived {
		query += fmt.Sprintf(" where removed = false ")
	} else if len(qualifications) > 0 {
		query += fmt.Sprintf(" where ")
	}

	var conditions []string
	var orConditions []string

	for _, qualification := range qualifications {
		column := common.ToSnakeCase(qualification.Column)
		qualificationValue := qualification.Value
		condition := qualification.Condition
		operator := qualification.Operator
		condStr := ""
		value := ""
		if boolean, ok := qualificationValue.(bool); ok {
			value = fmt.Sprint(boolean)
		} else if str, ok := qualificationValue.(int64); ok {
			value = fmt.Sprintf("%d", str)
		} else if str, ok := qualificationValue.(string); ok {
			value = str
		} else {
			if operator != "contains_with_in" {
				listVal, ok := qualificationValue.([]interface{})
				intValueList, ok2 := qualificationValue.([]int64)
				listVal1, ok1 := qualificationValue.([]string)
				if ok2 {
					stringSlice := make([]string, len(intValueList))
					for i, v := range intValueList {
						stringSlice[i] = fmt.Sprint(v)
					}
					value = "'" + strings.Join(stringSlice, "','") + "'"
				} else if ok {
					stringSlice := make([]string, len(listVal))
					for i, v := range listVal {
						if val, ok := v.(string); ok {
							stringSlice[i] = val
						} else if digit, ok := v.(float64); ok {
							stringSlice[i] = fmt.Sprintf("%d", int64(digit))
						}
					}
					value = "'" + strings.Join(stringSlice, "','") + "'"
				} else if ok1 {
					stringSlice := make([]string, len(listVal1))
					for i, v := range listVal1 {
						stringSlice[i] = strings.TrimSpace(v)
					}
					value = "'" + strings.Join(stringSlice, "','") + "'"
				}
			}
		}

		if "tags" == strings.ToLower(column) {
			if !strings.Contains(value, "'") {
				value = "'" + value + "'"
			}
			items := strings.Split(value, ",")
			var conditions []string
			if strings.EqualFold("not_equals", strings.ToLower(qualification.Operator)) {
				for _, item := range items {
					conditions = append(conditions, " NOT tags @> jsonb_build_array("+strings.TrimSpace(item)+")")
				}
			} else {
				for _, item := range items {
					conditions = append(conditions, " tags @> jsonb_build_array("+strings.TrimSpace(item)+")")
				}
			}
			condStr += strings.Join(conditions, " OR ")
		} else if "affected_products" == strings.ToLower(column) {
			if !strings.Contains(value, "'") {
				value = "'" + value + "'"
			}
			items := strings.Split(value, ",")
			var conditions []string
			if strings.EqualFold("not_equals", strings.ToLower(qualification.Operator)) {
				for _, item := range items {
					conditions = append(conditions, " NOT affected_products @> jsonb_build_array("+strings.TrimSpace(item)+")")
				}
			} else {
				for _, item := range items {
					conditions = append(conditions, " affected_products @> jsonb_build_array("+strings.TrimSpace(item)+")")
				}
			}
			condStr += strings.Join(conditions, " OR ")
		} else if "impact" == strings.ToLower(column) {
			var impact model.ComplianceImpact
			complianceImpact := impact.ToComplianceImpact(strings.ToLower(value))
			if complianceImpact.String() != "" {
				condStr += " \"impact\" = " + fmt.Sprintf("%d", complianceImpact) + " "
			} else {
				condStr += " CAST(\"impact\" AS TEXT) ILIKE '%" + value + "%' "
			}
		} else if "score" == strings.ToLower(column) || "epss_probability" == strings.ToLower(column) || "cvss3_base_score" == strings.ToLower(column) || "cvss2_base_score" == strings.ToLower(column) {
			condStr += " CAST(\"" + column + "\" AS TEXT) ILIKE '%" + value + "%' "
		} else if "rule_type" == strings.ToLower(column) {
			var ruleType model.ComplianceRuleType
			complianceRuleType := ruleType.ToComplianceRuleType(strings.ToLower(value))
			if complianceRuleType.String() != "" {
				condStr += " \"rule_type\" = " + fmt.Sprintf("%d", complianceRuleType) + " "
			} else {
				condStr += " CAST(\"rule_type\" AS TEXT) ILIKE '%" + value + "%' "
			}
		} else if "patch_approval_status" == strings.ToLower(column) {
			var approvalStatus model.PatchApprovalStatus
			patchApprovedStatus := approvalStatus.ToPatchApprovedStatus(strings.ToLower(value))
			if patchApprovedStatus.String() != "" {
				condStr += " \"patch_approval_status\" = " + fmt.Sprintf("%d", patchApprovedStatus) + " "
			} else {
				condStr += " CAST(\"patch_approval_status\" AS TEXT) ILIKE '%" + value + "%' "
			}
		} else if "patch_test_status" == strings.ToLower(column) {
			var testStatus model.PatchTestStatus
			patchTestStatus := testStatus.ToPatchTestStatus(strings.ToLower(value))
			if patchTestStatus.String() != "" {
				condStr += " \"patch_test_status\" = " + fmt.Sprintf("%d", patchTestStatus) + " "
			} else {
				condStr += " CAST(\"patch_test_status\" AS TEXT) ILIKE '%" + value + "%' "
			}
		} else if "enum" == strings.ToLower(qualification.Type) {
			if "os" == strings.ToLower(qualification.Reference) {
				var osType common.OsType
				os := osType.ToOsType(strings.ToLower(value))
				if os.String() != "" {
					condStr += " \"" + column + "\" = " + fmt.Sprintf("%d", os) + " "
				} else {
					condStr += " CAST(\"" + column + "\" AS TEXT) ILIKE '%" + value + "%' "
				}
			} else if "patchstate" == strings.ToLower(qualification.Reference) {
				var patchState model.PatchState
				state := patchState.ToPatchState(strings.ToLower(value))
				if state.String() != "" {
					condStr += " \"" + column + "\" = " + fmt.Sprintf("%d", state) + " "
				} else {
					condStr += " CAST(\"" + column + "\" AS TEXT) ILIKE '%" + value + "%' "
				}
			} else if "taskType" == qualification.Column {
				var taskType model.AgentTaskType
				task := taskType.ToTaskType(strings.ToLower(value))
				if task.String() != "" {
					condStr += " \"" + column + "\" = " + fmt.Sprintf("%d", task) + " "
				} else {
					condStr += " CAST(\"" + column + "\" AS TEXT) ILIKE '%" + value + "%' "
				}
			}
		} else if "os" == strings.ToLower(column) || "platform" == strings.ToLower(column) || "os_platform" == strings.ToLower(column) {
			if strings.EqualFold("in", strings.ToLower(qualification.Operator)) {
				condStr += "\"" + column + "\" IN (" + value + ") "
			} else {
				var osType common.OsType
				os := osType.ToOsType(strings.ToLower(value))
				if os.String() != "" {
					condStr += " \"" + column + "\" = " + fmt.Sprintf("%d", os) + " "
				} else {
					condStr += " CAST(\"" + column + "\" AS TEXT) ILIKE '%" + value + "%' "
				}
			}
		} else if strings.EqualFold("contains", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" ILIKE '%" + value + "%' "
		} else if strings.EqualFold("not_contains", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" NOT ILIKE '%" + value + "%' "
		} else if strings.EqualFold("equals", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" = '" + value + "' "
		} else if strings.EqualFold("not_equals", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" != '" + value + "' "
		} else if strings.EqualFold("in", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" IN (" + value + ") "
		} else if strings.EqualFold("not_in", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" Not IN (" + value + ") "
		} else if strings.EqualFold(">=", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" >= '" + value + "' "
		} else if strings.EqualFold("<=", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" <= '" + value + "' "
		} else if strings.EqualFold("is_not_null", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" IS NOT NULL '"
		} else if strings.EqualFold("is_null", strings.ToLower(qualification.Operator)) {
			condStr += "\"" + column + "\" IS NULL '"
		} else if strings.EqualFold("contains_with_in", strings.ToLower(qualification.Operator)) {
			if listVal, ok := qualificationValue.([]string); ok {
				condStr += "\"" + column + "\" ILIKE ANY (ARRAY[" + strings.Join(listVal, ", ") + "])"
			}
		} else if strings.EqualFold("field_list_contains", strings.ToLower(qualification.Operator)) {
			condStr += "("
			for i, column := range qualification.ColumnList {
				condStr += "\"" + common.ToSnakeCase(column) + "\" ILIKE '%" + value + "%' "
				if len(qualification.ColumnList)-1 > i {
					condStr += " or "
				}
			}
			condStr += ") "
		}

		if strings.EqualFold(condition, "OR") || condition == "" {
			orConditions = append(orConditions, condStr)
		} else {
			// Flush existing OR conditions before adding a new AND condition
			if len(orConditions) > 0 {
				conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(orConditions, " OR ")))
				orConditions = []string{}
			}
			conditions = append(conditions, condStr)
		}
	}

	if len(orConditions) > 0 {
		conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(orConditions, " OR ")))
	}

	if len(conditions) > 0 {
		if !filter.IncludeArchived {
			query += " AND " + strings.Join(conditions, " AND ")
		} else {
			query += strings.Join(conditions, " AND ")
		}
	}

	if queryCondition != "" {
		query += " AND " + queryCondition
	}

	if filter.GroupBy != "" {
		query += " GROUP BY " + filter.GroupBy
	}

	if !isCountQual {
		if filter.SortBy != "" {
			by := filter.SortBy
			direction := "DESC"
			if strings.Contains(by, "-") {
				direction = "ASC"
				by = strings.ReplaceAll(by, "-", "")
			}
			by = common.ToSnakeCase(by)
			query += fmt.Sprintf(" order by \"%s\" %s ", by, direction)
		} else {
			query += fmt.Sprintf(" order by \"%s\" %s ", "id", "DESC")
		}

		if filter.Size > 0 {
			query += fmt.Sprintf(" offset %v limit %v ", filter.Offset, filter.Size)
		}
	}
	query += ";"
	logger.ServiceLogger.Trace(query)
	return query
}

func ExtractTimelineToMillisecond(timeline Timeline) (from, to int64) {
	from = int64(0)
	to = int64(0)
	switch timeline.Timeline {
	case "custom":
		layout := "2006-01-02 15:04:05"
		t1, err := time.Parse(layout, timeline.From)
		if err != nil {
			break
		}
		t2, err := time.Parse(layout, timeline.From)
		if err != nil {
			break
		}
		from = t1.UnixMilli()
		to = t2.UnixMilli()
	case "This Minute":
		from, to = thisMinute()
	case "This Hour":
		from, to = thisHour()
	case "Today":
		from, to = today()
	case "This Week":
		from, to = thisWeek()
	case "This Month":
		from, to = thisMonth()
	case "This Quarter":
		from, to = thisQuarter()
	case "This Year":
		from, to = thisYear()
	case "Previous Day":
		from, to = previousDay()
	case "Previous Minute":
		from, to = previousMinute()
	case "Previous Hour":
		from, to = previousHour()
	case "Previous 24 Hour":
		from, to = previous24Hour()
	case "Previous Week":
		from, to = previousWeek()
	case "Previous Month":
		from, to = previousMonth()
	case "Previous Quarter":
		from, to = previousQuarter()
	case "Previous Year":
		from, to = previousYear()
	}

	return from, to
}

func thisMinute() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
	to := from.Add(time.Minute)
	return from.UnixMilli(), to.UnixMilli()
}

func thisHour() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	to := from.Add(time.Hour)
	return from.UnixMilli(), to.UnixMilli()
}

func today() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	to := from.AddDate(0, 0, 1)
	return from.UnixMilli(), to.UnixMilli()
}

func thisWeek() (int64, int64) {
	now := time.Now()
	weekday := int(now.Weekday())
	from := now.AddDate(0, 0, -weekday)
	from = time.Date(from.Year(), from.Month(), from.Day(), 0, 0, 0, 0, from.Location())
	to := from.AddDate(0, 0, 7)
	return from.UnixMilli(), to.UnixMilli()
}

func thisMonth() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	nextMonth := now.AddDate(0, 1, 0)
	to := time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, nextMonth.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func thisQuarter() (int64, int64) {
	now := time.Now()
	quarterMonth := (now.Month()-1)/3*3 + 1
	from := time.Date(now.Year(), quarterMonth, 1, 0, 0, 0, 0, now.Location())
	nextQuarter := time.Date(now.Year(), time.Month(quarterMonth)+3, 1, 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), nextQuarter.UnixMilli()
}

func thisYear() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	nextYear := time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), nextYear.UnixMilli()
}

func previousDay() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
	to := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previousMinute() (int64, int64) {
	now := time.Now()
	from := now.Add(-time.Minute)
	from = time.Date(from.Year(), from.Month(), from.Day(), from.Hour(), from.Minute(), 0, 0, from.Location())
	to := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previousHour() (int64, int64) {
	now := time.Now()
	from := now.Add(-time.Hour)
	from = time.Date(from.Year(), from.Month(), from.Day(), from.Hour(), 0, 0, 0, from.Location())
	to := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previous24Hour() (int64, int64) {
	now := time.Now()
	from := now.Add(-24 * time.Hour)
	from = time.Date(from.Year(), from.Month(), from.Day(), 0, 0, 0, 0, from.Location())
	to := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previousWeek() (int64, int64) {
	now := time.Now()
	weekday := int(now.Weekday())
	from := now.AddDate(0, 0, -7-weekday+1)
	from = time.Date(from.Year(), from.Month(), from.Day(), 0, 0, 0, 0, from.Location())
	to := from.AddDate(0, 0, 7)
	return from.UnixMilli(), to.UnixMilli()
}

func previousMonth() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())
	to := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previousQuarter() (int64, int64) {
	now := time.Now()
	quarterMonth := (now.Month()-1)/3*3 + 1
	from := time.Date(now.Year(), time.Month(quarterMonth)-3, 1, 0, 0, 0, 0, now.Location())
	to := time.Date(now.Year(), quarterMonth, 1, 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func previousYear() (int64, int64) {
	now := time.Now()
	from := time.Date(now.Year()-1, 1, 1, 0, 0, 0, 0, now.Location())
	to := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	return from.UnixMilli(), to.UnixMilli()
}

func ConvertJsonToSearchFilter(w http.ResponseWriter, r *http.Request, searchFilter SearchFilter) (SearchFilter, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &searchFilter)
	v := validator.New()
	err = v.Struct(searchFilter)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				return SearchFilter{}, err
			}
			break
		}
	}
	return searchFilter, err
}

// PrepareSecureQueryFromSearchFilter creates a parameterized SQL query from SearchFilter with DISTINCT and JOIN support to prevent SQL injection
func PrepareSecureQueryFromSearchFilter(filter SearchFilter, tableName string, isCountQual bool, queryCondition string) QueryResult {
	return PrepareParameterizedQueryFromSearchFilter(filter, tableName, isCountQual, queryCondition)
}
