    <!DOCTYPE html>
    <html lang="en" >
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
        
        <meta name="Author" content="VideoLAN" />
        <meta name="Keywords" content=
        "VideoLAN, VLC, VLC player, VLC media player, download, media player, player download, codec, encoder, media converter, video, video player,
        multimedia, multicast, x262, x264, x265, DVBlast, Windows, Linux, Unix, BeOS, BSD, MacOS, MacOS X, OSX, Android, Streaming,
        DVD, Matroska, Blu-Ray, FLV, Xvid, MPEG, MPEG2, MPEG4, H264, DivX, MKV, m2ts, open source, free software, floss, free" />

        <meta name="Description" content="Downloads" />
        <meta name="apple-itunes-app" content="app-id=650377962"/>
        <title>Downloads - VideoLAN</title>

        <meta http-equiv="refresh" content="5;URL='https://mirror.nag.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe'" />
                <link rel="shortcut icon" type="image/x-icon" href="//images.videolan.org/images/favicon.ico" />

                <link rel="alternate" type="application/rss+xml" title="RSS - VideoLAN News" href="//images.videolan.org/videolan-news.rss" />
        <link rel="alternate" type="application/rss+xml" title="RSS - Developers Blog" href="//planet.videolan.org/rss20.xml" />

        <!-- /index -->

        <link rel="stylesheet" type="text/css" href="//images.videolan.org/style/bootstrap.min.css" />
        <link rel="stylesheet" type="text/css" href="//images.videolan.org/style/style.min.css" />
                <link rel="dns-prefetch" href="//get.videolan.org" />

                <link rel="stylesheet" type="text/css" href="/download.css" />
        <!--[if lt IE 9]>
            <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
            <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
        <![endif]-->
        <!--[if lte IE 7]>
            * {
                behavior: url("/style/box-sizing.htc");
            }
        <![endif]-->
        <!--[if lt IE 7]>
           <style type="text/css">
              @media screen{ body{behavior:url("/width.htc");} }
                /* PNG support for IE */
                img {  behavior: url("/png.htc");}
                .DXImageTransformed { display: inline-block; }
              </style>
        <![endif]-->


                        <script src="//images.videolan.org/js/jquery.min.js" type='text/javascript'></script>
        <script async="async" src="//images.videolan.org/js/bootstrap.min.js" type="text/javascript"></script>
        
                <script type="text/javascript">
            (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
            })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

            ga('create', 'UA-38853043-1', 'auto');
            ga('set', 'anonymizeIp', true);
            ga('send', 'pageview');

            function isTouchDevice() {
                return 'ontouchstart' in window || navigator.maxTouchPoints;
            };

            $(document).ready(function() {
                //Early return if touch device
                if (isTouchDevice()) {
                    return;
                }
                //Open dropdown menu on hover if not touch device & navbar-toggle not visible
                $('#nav ul.nav li.dropdown').hover(function() {
                    if (!$('.navbar-toggle').is(':visible') && !$(this).hasClass('open')) {
                        $('.dropdown-toggle', this).trigger('click');
                    }
                }, function() {
                    if (!$('.navbar-toggle').is(':visible') && $(this).hasClass('open')) {
                        $('.dropdown-toggle', this).trigger('click');
                    }
                });
            });

        </script>
    </head>
<body class='new-design'><div id='bodyInner' class='orange'>    <nav id="nav" class="navbar navbar-default navbar-fixed-top">
 <div class="container">
    <div class="navbar-header">
        <a class="navbar-brand" href="//www.videolan.org/">
        <img src='//images.videolan.org/images/logoOrange.png' alt='VideoLAN association' />
        </a>
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main-navbar" aria-expanded="false">
            <span class="sr-only">Toggle navigation</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
    </div>
    <div id="main-navbar" class="navbar-collapse collapse">
      <ul class="nav navbar-nav">
        <li class="dropdown ">
          <a href="//www.videolan.org/videolan/" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">VideoLAN <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="//www.videolan.org/videolan/">Team &amp; Organization</a></li>
            <li><a href="//www.videolan.org/videolan/partners.html">Consulting Services &amp; Partners</a></li>
            <li><a href="//www.videolan.org/videolan/events/">Events</a></li>
            <li><a href="//www.videolan.org/legal.html">Legal</a></li>
            <li><a href="//www.videolan.org/press/">Press center</a></li>
            <li><a href="//www.videolan.org/contact.html">Contact us</a></li>
          </ul>
        </li>
        <li class="dropdown  active ">
          <a href="//www.videolan.org/vlc/" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">VLC <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="//www.videolan.org/vlc/">Download</a></li>
            <li><a href="//www.videolan.org/vlc/features.html">Features</a></li>
            <li><a href="//www.videolan.org/vlc/skins.html">Customize</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="//www.videolan.org/goodies.html">Get Goodies</a></li>
          </ul>
        </li>
        <li class="dropdown ">
          <a href="//www.videolan.org/projects/" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Projects <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="//www.videolan.org/projects/dvblast.html">DVBlast</a></li>
            <li><a href="//www.videolan.org/developers/x264.html">x264</a></li>
            <li><a href="//www.videolan.org/developers/x262.html">x262</a></li>
            <li><a href="//www.videolan.org/developers/x265.html">x265</a></li>
            <li><a href="//www.videolan.org/projects/multicat.html">multicat</a></li>
            <li><a href="//www.videolan.org/projects/dav1d.html">dav1d</a></li>
            <li><a href="//www.videolan.org/vlc/skineditor.html">VLC Skin Editor</a></li>
            <li><a href="//www.videolan.org/vlc/">VLC media player</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="//www.videolan.org/developers/libdvdcss.html">libdvdcss</a></li>
            <li><a href="//www.videolan.org/developers/libdvdnav.html">libdvdnav</a></li>
            <li><a href="//www.videolan.org/developers/libdvdnav.html">libdvdread</a></li>
            <li><a href="//www.videolan.org/developers/libbluray.html">libbluray</a></li>
            <li><a href="//www.videolan.org/developers/libdvbpsi.html">libdvbpsi</a></li>
            <li><a href="//www.videolan.org/developers/libaacs.html">libaacs</a></li>
            <li><a href="//www.videolan.org/developers/libdvbcsa.html">libdvbcsa</a></li>
            <li><a href="//www.videolan.org/developers/bitstream.html">biTStream</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="//www.videolan.org/projects/">All Projects</a></li>
          </ul>
        </li>
        <li class="dropdown ">
          <a href="//www.videolan.org/contribute.html" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Contribute <span class="caret"></span></a>
          <ul class="dropdown-menu">
            <li><a href="//www.videolan.org/contribute.html">Getting started</a></li>
            <li><a href="//www.videolan.org/contribute.html#money">Donate</a></li>
            <li><a href="//www.videolan.org/support/#bugs">Report a bug</a></li>
          </ul>
        </li>
        <li>
          <a href="//www.videolan.org/support/">Support</a>
        </li>
      </ul>
                           <div class="navbar-header pull-right">
                <div id="friends">
                    <a href="https://shrirangkahale.com/" target="_blank" class="sponsor-img"><img src="//get.videolan.org/sponsors/albony.png" /></a>                </div>
            </div>
            </div>
 </div>
</nav>

<!-- header -->

    <div class="header container">
        <div id="nonprofitOrganizationDiv2" class="center-block">
                VideoLAN, a project and a <a href="//www.videolan.org/videolan/" class="noUnderline">non-profit organization.</a>         </div>
            </div>
<script>
$(document).ready(function() {
    $('#alt_link').click(function(e) {
        e.preventDefault();
        $('#alt_links').show();
    });
    //Hide title
    if (!$('#pub_friend iframe').width()) {
        $('.pub_text').hide();
    }

    var ckConsentSettings = {
        cookieName: 'videolan_cookie_consent',
        cookieValue: 'accepted',
        expireIn: 395417
    };
    function acceptCookies() {
        var today = new Date();
        var expire = new Date();
        expire.setTime(today.getTime() + 3600000 * 24 * ckConsentSettings.expireIn);
        document.cookie = ckConsentSettings.cookieName + "=" + escape(ckConsentSettings.cookieValue)+ ";expires=" + expire.toGMTString() + "; path=/";
    }
    function deleteCookie() {
        document.cookie = ckConsentSettings.cookieName + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/';
    }
    function isCookiesAccepted() {
        var cookieName = ckConsentSettings.cookieName;
        var cookie = ' ' + document.cookie;
        var ind = cookie.indexOf(' ' + cookieName + '=');
        if (ind == -1) ind = cookie.indexOf(';' + cookieName + '=');
        if (ind ==-1 || cookieName == '') return '';
        var ind1 = cookie.indexOf(';', ind + 1);
        if (ind1 == -1) ind1 = cookie.length;
        // Returns true if the versions match
        return ckConsentSettings.cookieValue == unescape(cookie.substring(ind + cookieName.length + 2, ind1));
    }
    function dismissConsent() {
        $('#uecookies').removeClass('display');
        hideAds();
    }
    if (!isCookiesAccepted()) {
        $('#uecookies').addClass('display');
    }
    $('#agree-ck').on('click', function() {
        acceptCookies();
        dismissConsent();
    });
    $('#close-ck').on('click', function() {
        dismissConsent();
    });
});
</script>


<script language="javascript">
$(function() {
  /*
  var count = 4;
  countdown = setInterval(function(){
    $("#counter").html(count);
    if (count == 0) {
        clearInterval(countdown);
    }
    count--;
  }, 1000);
  */
    $('#checksum #display-title').bind('click', function() {
        $(this).hide();
        $('#checksum-value').show();
    });
});
</script>

<div class="container" id="mirrors">
    <section id="download-detail">
        <h1 class="downloading-title">Downloading VLC 3.0.20 for Windows 64 bits</h1>
        <div class="downloading-message">
            <p><b>Thanks!</b> Your download will start in few seconds...<br>
            <span> If not, <a href="https://mirror.nag.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe" id="alt_link">click here</a>.
                <span id='checksum'><span id='display-title'>Display checksum.</span><span id='checksum-value' style='font-size:10px; display: none;'>SHA-256 checksum: d8055b6643651ca5b9ad58c438692a481483657f3f31624cdfa68b92e8394a57</span></span>            </span></p>
            <div id="alt_links" style="display: none">
                <ul>
                <li><a href="https://mirror.del.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe">https://mirror.del.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe</a></li><li><a href="https://mirrors.in.sahilister.net/videolan/vlc/3.0.20/win64/vlc-3.0.20-win64.exe">https://mirrors.in.sahilister.net/videolan/vlc/3.0.20/win64/vlc-3.0.20-win64.exe</a></li><li><a href="https://mirror.ajl.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe">https://mirror.ajl.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe</a></li><li><a href="https://mirror.bom2.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe">https://mirror.bom2.albony.in/videolan-ftp/vlc/3.0.20/win64/vlc-3.0.20-win64.exe</a></li>                </ul>
            </div>
        </div>
    </section>
    <hr>
    <div class="row">
        <div id="blabla" class="col-md-8 col-md-offset-2">
            <h2>VLC media player</h2>
            <p><a href="//www.videolan.org/vlc/">VLC</a> a free and open source cross-platform multimedia player and framework that plays most multimedia files as well as DVD, Audio CD, VCD, and various streaming protocols.</p>
        </div>
    </div>
    <hr>
    <!-- country: IN -->    <div id="full" class="row">
        <div class="col-md-8 col-md-offset-2">
            <!-- <div id="pub_friend">
        <div class="pub_text">Free Software link</div>
          <a href="http://shop.borgodoro.it/products/vlc-chocolate">
          <img src='/ads/vlc_chocolate.png' alt='VLC Chocolate'/>
          </a>
      </div> -->
      <div id="pub_friend">
        <!--<div class="pub_text">Sponsored link</div>-->
        <div class="pub_image">
                    <div id="why_donate">
          <div class="audienceCallout">Why donate?</div>
          <div>
              <div class="productDescription">
                  VideoLAN is a non-profit organization.<br> All our costs are met by donations we receive from our users. If you enjoy using a VideoLAN product, please donate to support us.<br>
              </div>
          </div>
        </div>
                <script type="text/javascript"><!--
                google_ad_client = "ca-pub-8306248384796934";
                /* Display Ad test */
                google_ad_slot = "3123023001";
                google_ad_width = 300;
                google_ad_height = 250;
                //-->
            </script>
            <script type="text/javascript"
            src="//pagead2.googlesyndication.com/pagead/show_ads.js">
            </script>
        </div>
      </div>
      <div id="pub_adsense">
        <!--<div class="pub_text">Sponsored link</div>-->
            <div id="donate">
        <div class="ltrFloatLeft" style="font-size: 14px; color: #909090; padding-top: 5px;">
            <span style="text-transform: uppercase;">donate</span></div>
            <img src="//images.videolan.org/images/paypal.png" class="ltrFloatRight" alt="paypal">
            <form style="clear: both; padding-top: 10px;" action="https://www.paypal.com/en_US/cgi-bin/webscr" method="post">
                <p>
                    <input name="cmd" value="_xclick" type="hidden">
                    <input name="business" value="<EMAIL>" type="hidden">
                    <input name="item_name" value="Development and communication of VideoLAN" type="hidden">
                    <input name="no_note" value="0" type="hidden">
                    <input name="currency_code" value="EUR" type="hidden">
                    <input name="tax" value="0" type="hidden">
                    <input name="lc" value="GB" type="hidden">
                    <input name="no_shipping" value="1" type="hidden">
                    <input type="hidden" name="hosted_button_id" value="P36JTE2HGG82J">
                    <input type="hidden" name="item_number" value="42">
                    <input name="return" value="http://www.videolan.org/thank_you.html" type="hidden">
                    <input class="text" type="text" name="amount" value="4.00" style="background: #fff url(&quot;//images.videolan.org/images/euro.png&quot;) no-repeat 65px 2px;">
                    <button class="button" type="submit">donate</button>
                </p>
            </form>
            <form style="clear: both; padding-top: 6px;" action="https://www.paypal.com/en_US/cgi-bin/webscr" method="post">
                <p>
                    <input name="cmd" value="_xclick" type="hidden">
                    <input name="business" value="<EMAIL>" type="hidden">
                    <input name="item_name" value="Development and communication of VideoLAN" type="hidden">
                    <input name="no_note" value="0" type="hidden">
                    <input name="currency_code" value="USD" type="hidden">
                    <input name="tax" value="0" type="hidden">
                    <input name="lc" value="US" type="hidden">
                    <input name="no_shipping" value="1" type="hidden">
                    <input type="hidden" name="hosted_button_id" value="P36JTE2HGG82J">
                    <input type="hidden" name="item_number" value="43">
                    <input name="return" value="http://www.videolan.org/thank_you.html" type="hidden">
                    <input id="dtext" class="text" type="text" name="amount" value="5.00" style="background: #fff url(&quot;//images.videolan.org/images/dollar.png&quot;) no-repeat 0 3px; padding-right: 10px; width: 75px;">
                    <button class="button" type="submit">donate</button>
                </p>
            </form>
        </div>
            <div class="pub_image">
          <script type="text/javascript"><!--
            google_ad_client = "ca-pub-8306248384796934";
            /* VideoLAN Medium */
            google_ad_slot = "3663267801";
            google_ad_width = 300;
            google_ad_height = 250;
            //-->
          </script>
          <script type="text/javascript"
          src="//pagead2.googlesyndication.com/pagead/show_ads.js">
          </script>
        </div>
      </div>
          </div>
    </div>
    <hr>
    <section id="original-picture" class="original-picture">
    <div><img src='https://images.videolan.org/images/goodies/thumbnails/cone_FQCw.png'></div>    </section>
    <div class="row">
        <div id="needhelp" class="col-md-8 col-md-offset-2">
            <h2>Need some help?</h2>
            <br>
            <p>Do you need some Help? Or do you want to submit a Suggestion? </p>
            <p>Do you have a Feature Idea for a VideoLAN Product?</p>
            <p>Or maybe you just want to share some kind words to the developers?</p>
            <br>
            <p>You can access our Official Documentation for any assistance about basic or specific usages of VLC.</p>
            <p>VideoLAN Documentation for Users : <a href="https://docs.videolan.me/vlc-user/">User documentation</a></p>
            <p>VideoLAN Documentation for Developers: <a href="https://epirat.videolan.me/devdocs/">Developer Documentation</a></p>
            <br>
            <p>If you cannot find the answers you're looking for, feel free to Join our Community Forums to find all the assistance, discussions and community threads, for any platform you use!</p>
            <p>VideoLAN Community Forums: <a href="https://forum.videolan.org/">VideoLAN forum</a></p>
            <p>We are also on Reddit! Share your thoughts and feedbacks on the dedicated Subreddit: <a href="https://www.reddit.com/r/VLC">VLC Subreddit</a></p>
            <p><i>Note: VideoLAN is free and open source software; and is only supported by donations. Developers are mostly volunteers.</i></p>
	    <p><i>Therefore, please remember that every user support is mostly provided by volunteers doing it in their free time.</i></p>
	    <br>
        </div>
	<div class="commu-picture"><img class="commu-pic" src="https://images.videolan.org/images/events/vdd18.jpg"></div>
    </div>
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <h2>Come and talk to us during tech events!</h2>
            <br>
	    <p>If you really want to:</p>
	    <p>
		- try the next features in development on VLC<br>
         	- have a little chat with us<br>
            	- or just try to get some fancy goodies<br>
	    </p>
	    <p>Don't hesitate to come at the events where VideoLAN is present! We are always pleased to hear some feedback from you.</p>
            <br>
	    <p>The main tech events where VideoLAN is present are usually:</p>
	    <p>
		- <a href="https://fosdem.org">FOSDEM</a>, one of the biggest open-source focused software engineering conference in Europe, at Brussels, Belgium. We usually have a booth there, and some technical talks.<br>
            	- <a href="https://www.socallinuxexpo.org">SCaLE</a>, the largest community-run open-source and free software conference in North America, in the greater Los Angeles area.<br>
            	- <a href="https://summit.fossasia.org/">FOSSASIA Summit</a>, Asia's Leading Open Source Conference.<br>
            	- We are event sometimes at <a ref="https://www.ces.tech/">CES</a>!<br>
	    </p>
        </div>
    </div>

</div>
<!-- Do not remove: nagios check -->
    </div> <!-- BodyInner -->
    <div class="clearfix"></div>
    <div id='footer' class="">
        <div class="container">
            <div class="row">
                <div class="col-sm-12 col-md-3">
                    <a href="//www.videolan.org/">
                        <img src='//images.videolan.org/images/logoGrey.png' alt='Association VideoLAN' class='center-xs center-sm' />
                    </a>
                    <div class="social-box">
                        <a class="social" href="http://www.facebook.com/vlc.media.player">
                            <i class="icon-facebook"></i>
                        </a>
                        <a class="social" href="http://www.twitter.com/videolan">
                            <i class="icon-twitter"></i>
                        </a>
                    </div>
                </div>
                <div class="col-xs-6 col-md-2">
                    <div class='footerHeading'>VLC media player</div>
                    <ul>
                        <li><a href="//www.videolan.org/vlc/">VLC</a></li>
                        <li><a href="//www.videolan.org/vlc/download-windows.html">VLC for Windows</a></li>
                        <li><a href="//www.videolan.org/vlc/download-macosx.html">VLC for Mac OS X</a></li>
                        <li><a href="//www.videolan.org/vlc/download-ubuntu.html">VLC for Ubuntu</a></li>
                        <li><a href="//www.videolan.org/vlc/download-android.html">VLC for Android</a></li>
                        <li><a href="//www.videolan.org/vlc/download-ios.html">VLC for iOS</a></li>
                        <li><a href="//www.videolan.org/vlc/skins.html">Skins</a></li>
                        <li><a href='https://addons.videolan.org/browse/cat/323/ord/latest/'>Extensions</a></li>
                        <li><a href="//www.videolan.org/vlc/features.html">Features</a></li>
                        <li><a href="//www.videolan.org/vlc/screenshots.html">Screenshots</a></li>
                        <li><a href="//www.videolan.org/vlc/skineditor.html">VLC Skin Editor</a></li>
                    </ul>
                </div>
                <div class="col-xs-6 col-md-2">
                    <div class='footerHeading'>All Projects</div>
                    <ul>
                        <li><a href="//www.videolan.org/vlmc/">VideoLan Movie Creator</a></li>
                        <li><a href="//www.videolan.org/projects/dvblast.html">DVBlast</a></li>
                        <li><a href="//www.videolan.org/developers/x264.html">x264</a></li>
                        <li><a href="//www.videolan.org/developers/x262.html">x262</a></li>
                        <li><a href="//www.videolan.org/developers/x265.html">x265</a></li>
                        <li><a href="//www.videolan.org/projects/multicat.html">multicat</a></li>
                        <li><a href="//www.videolan.org/projects/vlma/">VLMa</a></li>
                        <li><a href="//www.videolan.org/developers/libdvdcss.html">libdvdcss</a></li>
                        <li><a href="//www.videolan.org/developers/libdvdnav.html">libdvdnav</a></li>
                        <li><a href="//www.videolan.org/developers/libdvdnav.html">libdvdread</a></li>
                        <li><a href="//www.videolan.org/developers/libbluray.html">libbluray</a></li>
                        <li><a href="//www.videolan.org/developers/libdvbpsi.html">libdvbpsi</a></li>
                        <li><a href="//www.videolan.org/developers/libaacs.html">libaacs</a></li>
                        <li><a href="//www.videolan.org/developers/libdvbcsa.html">libdvbcsa</a></li>
                        <li><a href="//www.videolan.org/developers/bitstream.html">biTStream</a></li>
                    </ul>
                </div>
                <div class="col-xs-6 col-md-2">
                    <div class='footerHeading'>Community</div>
                    <ul>
                        <li><a href='https://wiki.videolan.org/'>Wiki</a></li>
                        <li><a href='https://forum.videolan.org/'>Forums</a></li>
                        <li><a href="//www.videolan.org/support/lists.php">Mailing-Lists</a></li>
                        <li><a href="//www.videolan.org/support/faq.html">FAQ</a></li>
                        <li><a href="//www.videolan.org/contribute.html#money">Donate money</a></li>
                        <li><a href="//www.videolan.org/contribute.html">Donate time</a></li>
                        <li><a href="//www.videolan.org/goodies.html">Get Goodies</a></li>
                    </ul>
                </div>
                <div class="col-xs-6 col-md-2">
                    <div class='footerHeading'>VideoLAN</div>
                    <ul>
                        <li><a href="//www.videolan.org/videolan/">Project and Organization</a></li>
                        <li><a href="//www.videolan.org/videolan/team/">Team</a></li>
                        <li><a href="//www.videolan.org/legal.html">Legal</a></li>
                        <li><a href="//www.videolan.org/contact.html">Contact us</a></li>
                        <li><a href="//www.videolan.org/videolan/partners.html">Partners</a></li>
                        <li><a href="//www.videolan.org/videolan/mirrors.html">Mirrors</a></li>
                        <li><a href="//www.videolan.org/press/">Press center</a></li>
                        <li><a href="//www.videolan.org/videolan/events/">Events</a></li>
                        <li><a href="//www.videolan.org/security/">Security center</a></li>
                        <li><a href="//www.videolan.org/contribute.html">Get Involved</a></li>
                        <li><a href="//www.videolan.org/news.html">News</a></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div id='translation' class="col-md-12" style="font-size: 9px;">
                                </div>
            </div>
            <div class="row">
                <div class="col-md-12" style='text-align: center; line-height: 14px; font-size: 9px; color: #999;'>
                    <p>
                        <a href='/legal.html'>Legal</a>
                        <span dir="ltr" lang="en">
                            | <a href='/contact.html'>Report Trademark Abuse</a><br />

                            VideoLAN, VLC, VLC media player and x264 are trademarks internationally registered by the <a href="/videolan/">VideoLAN non-profit organization.</a><br />
                            VideoLAN software is licensed under various open-source licenses: use and distribution are defined by each software license.
                        </span>
                    </p>
                    <p>
                        <span dir="ltr" lang="en">
                            Design by <a href="http://www.madebyargon.com">Made By Argon</a>. Some icons are licensed under the <a href="https://creativecommons.org/licenses/by-sa/3.0/us/">CC BY-SA 3.0+</a>. <br />
                            The VLC cone icon was designed by Richard Øiestad. Icons for VLMC, DVBlast and x264 designed by <a href='http://cuberto.com/'>Roman Khramov</a>.
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    </body>
</html>
<div id="uecookies">Google uses cookies to analyze advertisements of this site. Information about your use of our site is shared with Google for that purpose.    <a id="agree-ck">ok</a>
</div>
