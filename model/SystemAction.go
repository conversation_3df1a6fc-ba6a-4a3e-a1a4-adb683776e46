package model

import "github.com/uptrace/bun"

type SystemAction struct {
	bun.BaseModel `bun:"deployment.system_actions"`
	BaseEntityModel
	Description    string
	Disabled       bool
	WindowsCommand []SystemActionConfig
	LinuxCommand   []SystemActionConfig
	MacCommand     []SystemActionConfig
}

type SystemActionConfig struct {
	OrderId     int
	CommandType CommandType
	Command     string
	Platform    string
}
