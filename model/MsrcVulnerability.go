package model

import "github.com/uptrace/bun"

type MsrcVulnerability struct {
	bun.BaseModel   `bun:"deployment.msrc_vulnerabilities"`
	Id              int64  `bun:",pk,autoincrement"`
	Name            string `bun:"type:varchar(1000)"`
	CreatedById     int64
	CreatedTime     int64
	UpdatedById     int64
	UpdatedTime     int64
	OOB             bool
	Removed         bool
	CVE             string           `bun:"type:varchar(100)" json:"cve"`
	Description     string           `json:"description"`
	FAQs            []string         `bun:"faqs" json:"faqs"`
	Tag             string           `bun:"type:varchar(1000)" json:"tag"`
	CNA             string           `json:"cna"`
	ExploitStatus   string           `bun:"type:varchar(1000)" json:"exploitStatus"`
	Mitigation      string           `json:"mitigation"`
	Workaround      string           `json:"workaround"`
	Products        []Product        `json:"products"`
	URL             string           `bun:"type:varchar(1000)" json:"url"`
	Acknowledgments []Acknowledgment `json:"acknowledgments"`
	Revisions       []Revision       `json:"revisions"`
}

type Product struct {
	ProductID string    `json:"productID"`
	Name      string    `json:"name"`
	Impact    string    `json:"impact"`
	Severity  string    `json:"severity"`
	ScoreSet  *ScoreSet `json:"scoreSet"`
	KBs       []KB      `json:"kbs"`
}

type ScoreSet struct {
	BaseScore     string `json:"baseScore"`
	TemporalScore string `json:"temporalScore"`
	Vector        string `json:"vector"`
}

type KB struct {
	Article         string `json:"article"`
	RestartRequired string `json:"restartRequired"`
	SubType         string `json:"subType"`
	FixedBuild      string `json:"fixedBuild"`
	ArticleURL      string `json:"articleURL"`
	DownloadURL     string `json:"downloadURL"`
}

type Acknowledgment struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

type Revision struct {
	Number      string `json:"number"`
	Date        string `json:"date"`
	Description string `json:"description"`
}
