package model

import "github.com/uptrace/bun"

type AutoPatchDeploy struct {
	bun.BaseModel `bun:"deployment.auto_patch_deployment"`
	BaseEntityModel
	DisplayName         string `bun:"type:varchar(100)"`
	Description         string
	PatchCategories     []PatchUpdateCategory
	PatchSeverities     []PatchSeverity
	Platform            string
	ApplicationType     APApplicationType
	ProductIds          []int64
	ComputerGroupFilter ComputerGroupFilter
	DeploymentPolicyId  int64
	NotifyTo            []string
	DeploymentScheduler SchedulerConfig
}

type APApplicationType int

const (
	AllApplication APApplicationType = iota + 1
	IncludeApplication
	ExcludeApplication
)

func (t APApplicationType) String() string {
	return [...]string{"", "all", "include", "exclude"}[t]
}

func (t APApplicationType) ToAPApplicationType(appType string) APApplicationType {
	switch appType {
	case "all":
		return AllApplication
	case "include":
		return IncludeApplication
	case "exclude":
		return ExcludeApplication
	default:
		return AllApplication
	}
}
