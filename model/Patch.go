package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type Patch struct {
	bun.BaseModel `bun:"deployment.patch"`
	BaseEntityModel
	Title                      string
	OsPlatform                 common.OsType
	OsArch                     common.OsArchitecture
	OsApplicationId            int64
	VendorId                   int64
	DownloadStatus             PatchDownloadStatus
	DownloadError              string
	DownloadSize               int64
	PatchApprovalStatus        PatchApprovalStatus
	PatchTestStatus            PatchTestStatus
	ApprovedOn                 int64
	ApprovedBy                 int64
	Description                string
	AffectedProducts           []int64
	Tags                       []string
	DownloadOn                 int64
	BulletinId                 string
	CVENumber                  string
	KbId                       string
	PatchSeverity              PatchSeverity
	PatchUpdateCategory        PatchUpdateCategory
	SupportUrl                 string
	LanguageSupported          []int64
	RebootBehaviour            RebootBehaviour
	IsUninstallable            bool
	HasSupersededUpdates       bool
	IsSuperseded               bool
	DownloadFileDetails        []PatchFileData
	UUID                       string `bun:"type:varchar(500)"`
	Status                     PatchStatus
	Source                     PatchSource
	ReleaseDate                int64
	ProductType                string `bun:"type:varchar(100)"`
	InstallCommand             string
	UninstallCommand           string
	UpgradeCommand             string
	AtLeastOneFileInstallation bool
	PackageNames               []string
}

type PatchStatus int

const (
	PCH_DRAFT PatchStatus = iota + 1
	PCH_PUBLISH
)

func (s PatchStatus) String() string {
	switch s {
	case PCH_DRAFT:
		return "draft"
	case PCH_PUBLISH:
		return "publish"
	default:
		return "draft"
	}
}

func (s PatchStatus) ToPatchStatus(status string) PatchStatus {
	switch status {
	case "draft":
		return PCH_DRAFT
	case "publish":
		return PCH_PUBLISH
	default:
		return PCH_DRAFT
	}
}

type PatchSource int

const (
	Scanning PatchSource = iota + 1
	Manually
)

func (s PatchSource) String() string {
	return [...]string{"scanning", "manually"}[s-1]
}

func (s PatchSource) ToPatchSource(source string) PatchSource {
	switch source {
	case "scanning":
		return Scanning
	case "manually":
		return Manually
	default:
		return Scanning
	}
}

type PatchDownloadStatus int

const (
	None PatchDownloadStatus = iota + 1
	PENDING
	IN_PROGRESS
	SUCCESS
	FAILED
)

func (p PatchDownloadStatus) String() string {
	return [...]string{"none", "pending", "in_progress", "success", "failed"}[p-1]
}

func (p PatchDownloadStatus) ToPatchDownloadStatus(status string) PatchDownloadStatus {
	switch status {
	case "none":
		return None
	case "pending":
		return PENDING
	case "in_progress":
		return IN_PROGRESS
	case "success":
		return SUCCESS
	case "failed":
		return FAILED
	default:
		return None
	}
}

type PatchApprovalStatus int

const (
	NOT_APPROVED PatchApprovalStatus = iota + 1
	APPROVED
	DECLINE
	PENDING_UPLOAD
)

func (p PatchApprovalStatus) String() string {
	return [...]string{"not_approved", "approved", "decline", "pending_upload"}[p-1]
}

func (p PatchApprovalStatus) ToPatchApprovedStatus(status string) PatchApprovalStatus {
	switch status {
	case "approved":
		return APPROVED
	case "decline":
		return DECLINE
	case "not_approved":
		return NOT_APPROVED
	case "pending_upload":
		return PENDING_UPLOAD
	default:
		return NOT_APPROVED
	}

}

type PatchTestStatus int

const (
	NOT_TESTED PatchTestStatus = iota + 1
	IN_TESTING
	TEST_SUCCESS
	TEST_FAILED
)

func (p PatchTestStatus) String() string {
	return [...]string{"not_tested", "in_testing", "test_success", "test_failed"}[p-1]
}

func (p PatchTestStatus) ToPatchTestStatus(status string) PatchTestStatus {
	switch status {
	case "not_tested":
		return NOT_TESTED
	case "in_testing":
		return IN_TESTING
	case "test_success":
		return TEST_SUCCESS
	case "test_failed":
		return TEST_FAILED
	default:
		return NOT_TESTED
	}
}

type PatchSeverity int

const (
	PS_UNSPECIFIED PatchSeverity = iota + 1
	PS_CRITICAL
	PS_IMPORTANT
	PS_MODERATE
	PS_LOW
)

func (p PatchSeverity) String() string {
	return [...]string{"unspecified", "critical", "important", "moderate", "low"}[p-1]
}

func (p PatchSeverity) ToPatchSeverity(severity string) PatchSeverity {
	switch severity {
	case "unspecified":
		return PS_UNSPECIFIED
	case "critical":
		return PS_CRITICAL
	case "important":
		return PS_IMPORTANT
	case "moderate":
		return PS_MODERATE
	case "low":
		return PS_LOW
	default:
		return PS_UNSPECIFIED
	}
}

type PatchUpdateCategory int

const (
	UPDATES PatchUpdateCategory = iota + 1
	TOOLS
	FEATURE_PACKS
	SERVICE_PACKS
	UPDATE_ROLLUPS
	DEFINITION_UPDATES
	CRITICAL_UPDATES
	SECURITY_UPDATES
	HOTFIX
	UPGRADES
	DRIVER_UPDATES
	THIRD_PARTY
)

func (p PatchUpdateCategory) String() string {
	return [...]string{
		"updates",
		"tools",
		"feature packs",
		"service packs",
		"update rollups",
		"definition updates",
		"critical updates",
		"security updates",
		"hotfix",
		"upgrades",
		"driver updates",
		"third party",
	}[p-1]
}

func (p PatchUpdateCategory) ToPatchCategory(category string) PatchUpdateCategory {
	switch category {
	case "updates":
		return UPDATES
	case "tools":
		return TOOLS
	case "feature packs":
		return FEATURE_PACKS
	case "service packs":
		return SERVICE_PACKS
	case "update rollups":
		return UPDATE_ROLLUPS
	case "definition updates":
		return DEFINITION_UPDATES
	case "critical updates":
		return CRITICAL_UPDATES
	case "security updates":
		return SECURITY_UPDATES
	case "hotfix":
		return HOTFIX
	case "upgrades":
		return UPGRADES
	case "driver updates":
		return DRIVER_UPDATES
	case "third party":
		return THIRD_PARTY
	default:
		return UPDATES
	}
}

type RebootBehaviour int

const (
	MAY_BE RebootBehaviour = iota + 1
	YES
	NO
)

func (r RebootBehaviour) String() string {
	return [...]string{"may_be", "yes", "no"}[r-1]
}

func (r RebootBehaviour) ToRebootBehaviour(behaviour string) RebootBehaviour {
	switch behaviour {
	case "may_be":
		return MAY_BE
	case "yes":
		return YES
	case "no":
		return NO
	default:
		return MAY_BE
	}
}
