package model

import "github.com/uptrace/bun"

type Audit struct {
	bun.BaseModel   `bun:"deployment.audits"`
	Id              int64 `bun:",pk,autoincrement"`
	DisplayName     string
	RefId           int64
	CreatedById     int64
	CreatedTime     int64
	PerformerId     int64
	AuditString     string
	AuditEventType  string
	AuditEventModel string
}

type AuditEventType int

const (
	CreateAudit AuditEventType = iota + 1
	UpdateAudit
	DeleteAudit
)

func (r AuditEventType) String() string {
	switch r {
	case CreateAudit:
		return "create"
	case UpdateAudit:
		return "update"
	case DeleteAudit:
		return "delete"
	default:
		return ""
	}
}
