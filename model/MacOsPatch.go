package model

import "github.com/uptrace/bun"

type MacOsPatch struct {
	bun.BaseModel `bun:"deployment.mac_patches"`
	BaseEntityModel
	OsVersion            string          `json:"osVersion" bun:"type:varchar(100)"`
	ProductKey           string          `json:"productKey" bun:"type:varchar(100)"`
	ReleaseDate          int64           `json:"releaseDate"`
	Description          string          `json:"description"`
	Version              string          `json:"version" bun:"type:varchar(100)"`
	DistributionFileName string          `json:"distributionFileName"`
	ProductType          string          `json:"productType" bun:"type:varchar(100)"`
	Packages             []PatchFileData `json:"packages"`
}
