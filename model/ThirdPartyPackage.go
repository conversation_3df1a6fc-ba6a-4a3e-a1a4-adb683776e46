package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type ThirdPartyPackage struct {
	bun.BaseModel `bun:"deployment.third_party_packages"`
	BaseEntityModel
	Description      string                `json:"description"`
	Version          string                `json:"version"`
	Os               common.OsType         `json:"os"`
	Arch             common.OsArchitecture `json:"arch"`
	LanguageCode     string                `json:"languageCode"`
	PkgFileData      []PatchFileData       `json:"pkgFileData"`
	LatestPackageUrl string                `json:"latestPackageUrl"`
	Publisher        string                `json:"publisher"`
	SupportUrl       string                `json:"supportUrl"`
	ReleaseNote      string                `json:"releaseNote"`
	ReleaseDate      int64                 `json:"releaseDate"`
	Application      string                `json:"application"`
	CveDetails       []CveDetails          `json:"cveDetails"`
	Uuid             string                `json:"uuid"`
	OsVersion        string                `json:"osVersion"`
	ProductCode      string                `json:"productCode"`
	InstallCommand   string                `json:"installCommand"`
	UnInstallCommand string                `json:"unInstallCommand"`
	DetectionScript  string                `bun:"type:varchar(1000)" json:"detectionScript"`
}

type CveDetails struct {
	CveId       string `json:"cveId"`
	Impact      string `json:"impact"`
	Description string `json:"description"`
	References  string `json:"references"`
}
