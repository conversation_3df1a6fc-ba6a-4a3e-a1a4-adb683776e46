package model

import "github.com/uptrace/bun"

type WindowsPatch struct {
	bun.BaseModel `bun:"deployment.windows_patches"`
	BaseEntityModel
	TempData                                bool
	ReplaceById                             int64
	ReplaceByUuid                           string
	RevisionId                              int64
	UUID                                    string `bun:",notnull,unique"`
	Title                                   string
	KbId                                    string
	Description                             string
	HasReplacement                          bool
	Leaf                                    bool
	Declined                                bool
	PublishState                            string
	UpdateType                              string
	BulletinId                              string
	InstallationCanRequestUserInput         bool
	InstallationRequiresNetworkConnectivity bool
	CanUninstall                            bool
	InstallationImpact                      string
	RestartBehaviour                        string
	SupportedLanguage                       string
	LastUpdatedTime                         int64
	WsusLastUpdatedTime                     int64
	FileDetails                             []PatchFileData
	SupersedesString                        string
	Company                                 string
	ProductFamily                           string
	Products                                string
	ProductsUuid                            []string
	Classification                          string
	CveNumber                               string
	Severity                                string
	MoreInfoUrl                             string
	DownloadUrl                             []string
	ReleaseDate                             int64
	Arch                                    string
	OsName                                  string
	OsArch                                  string
	ProductId                               int64
	MsrcPatchId                             int64
	AffectedProduct                         []string
	Rules                                   []map[string]interface{}
	KbIdToBeInstalled                       string
	CabExist                                bool
	AtLeastOneFileInstallation              bool
	PackageNames                            []string
}
