package model

import (
	"errors"
	"fmt"

	"github.com/uptrace/bun"
)

type DeploymentPolicy struct {
	bun.BaseModel `bun:"deployment.deployment_policies"`
	BaseEntityRefModel
	DisplayName string `bun:",notnull,type:varchar(1000)"`
	Description string
	Type        DeploymentPolicyType

	//"DeploymentPolicyType = schedule" related fields
	InitiateDeploymentOn DeploymentStartOn
	DeploymentDays       []string
	DeploymentTimeFrom   int64
	DeploymentTimeTo     int64
	AfterEveryTimeUnit   string `bun:"type:varchar(100)"`
	AfterEveryTime       int64
	RestartType          RestartType
}

type RestartType int

const (
	WARN_RESTART RestartType = iota + 1
	NO_RESTART
	FORCE_RESTART
	PROMPT_RESTART
)

func (t RestartType) String() string {
	switch t {
	case WARN_RESTART:
		return "warn_restart"
	case NO_RESTART:
		return "no_restart"
	case FORCE_RESTART:
		return "force_restart"
	case PROMPT_RESTART:
		return "prompt_restart"
	default:
		return "no_restart"
	}
}

func (t RestartType) ToRestartType(restartType string) RestartType {
	switch restartType {
	case "warn_restart":
		return WARN_RESTART
	case "no_restart":
		return NO_RESTART
	case "force_restart":
		return FORCE_RESTART
	case "prompt_restart":
		return PROMPT_RESTART
	default:
		return NO_RESTART
	}
}

type DeploymentStartOn int

const (
	OnSystemStartUp DeploymentStartOn = iota + 1
	OnNextCycle
	Recurring
)

func (d DeploymentStartOn) String() string {
	switch d {
	case OnNextCycle:
		return "on_next_cycle"
	case OnSystemStartUp:
		return "on_system_start_up"
	case Recurring:
		return "recurring"
	default:
		return ""
	}
}

func (d DeploymentStartOn) ToDeploymentStartOn(deploymentStartOn string) (DeploymentStartOn, error) {
	switch deploymentStartOn {
	case "on_next_cycle":
		return OnNextCycle, nil
	case "on_system_start_up":
		return OnSystemStartUp, nil
	case "recurring":
		return Recurring, nil
	default:
		return OnNextCycle, errors.New(fmt.Sprintf("invalid deployment start on value '%s'", deploymentStartOn))
	}
}

type DeploymentPolicyType int

const (
	Instant DeploymentPolicyType = iota + 1
	Schedule
)

func (policyType DeploymentPolicyType) String() string {
	switch policyType {
	case Schedule:
		return "schedule"
	case Instant:
		return "instant"
	default:
		return ""
	}
}

func (policyType DeploymentPolicyType) ToDeploymentPolicyType(deploymentPolicyType string) (DeploymentPolicyType, error) {
	switch deploymentPolicyType {
	case "schedule":
		return Schedule, nil
	case "instant":
		return Instant, nil
	default:
		return Instant, errors.New(fmt.Sprintf("invalid deployment policy type value '%s'", deploymentPolicyType))
	}
}

type DeploymentDays int

const (
	Monday DeploymentDays = iota + 1
	Tuesday
	Wednesday
	Thursday
	Friday
	Saturday
	Sunday
)

func (days DeploymentDays) String() string {
	switch days {
	case Monday:
		return "monday"
	case Tuesday:
		return "tuesday"
	case Wednesday:
		return "wednesday"
	case Thursday:
		return "thursday"
	case Friday:
		return "friday"
	case Saturday:
		return "saturday"
	case Sunday:
		return "sunday"
	default:
		return ""
	}
}

func (days DeploymentDays) ToDeploymentDays(deploymentDay string) (DeploymentDays, error) {
	switch deploymentDay {
	case "monday":
		return Monday, nil
	case "tuesday":
		return Tuesday, nil
	case "wednesday":
		return Wednesday, nil
	case "thursday":
		return Thursday, nil
	case "friday":
		return Friday, nil
	case "saturday":
		return Saturday, nil
	case "sunday":
		return Sunday, nil
	default:
		return Monday, errors.New(fmt.Sprintf("invalid deployment day value '%s'", deploymentDay))
	}
}
