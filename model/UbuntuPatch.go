package model

import "github.com/uptrace/bun"

type UbuntuPatch struct {
	bun.BaseModel `bun:"deployment.ubuntu_patches"`
	BaseEntityModel
	UUID              string
	OsVersion         string
	Channel           string
	Repo              string
	PackageName       string
	Arch              string
	Version           string
	Priority          string
	Section           string
	Origin            string
	Depends           string
	Breaks            string
	FileName          string
	DownloadUrl       string
	Size              int64
	Sha1              string
	PkgAndVersion     string
	PkgNameWithDistro string
	Downloadable      bool
	ReleaseDate       int64
}
