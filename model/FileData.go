package model

import (
	"deployment/common"

	"github.com/uptrace/bun"
)

type FileData struct {
	bun.BaseModel `bun:"deployment.file_data"`
	BaseEntityModel
	RealName string `bun:"type:varchar(250)"`
	RefName  string `bun:",notnull,type:varchar(250)"`
	CheckSum string `bun:"type:varchar(1000)"`
	Size     int64
}

type FileMetaData struct {
	RealName   string `json:"realName"`
	RefName    string `json:"refName"`
	Url        string `json:"url"`
	LocationId int64  `json:"locationId"`
}

type FileDetails struct {
	OsName       string                `json:"osName"`
	OsArch       common.OsArchitecture `json:"osArch"`
	FileName     string                `json:"fileName"`
	RefName      string                `json:"refName"`
	Url          string                `json:"url"`
	Size         int64                 `json:"size"`
	LanguageCode int                   `json:"languageCode"`
}
