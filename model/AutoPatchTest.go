package model

import "github.com/uptrace/bun"

type AutoPatchTest struct {
	bun.BaseModel `bun:"deployment.auto_patch_test"`
	BaseEntityModel
	DisplayName         string `bun:"type:varchar(500)"`
	Description         string
	PatchCategories     []PatchUpdateCategory
	PatchSeverities     []PatchSeverity
	Platform            string
	ApplicationType     APApplicationType
	ProductIds          []int64
	ComputerGroupFilter ComputerGroupFilter
	DeploymentPolicyId  int64
	NotifyTo            []string
}
