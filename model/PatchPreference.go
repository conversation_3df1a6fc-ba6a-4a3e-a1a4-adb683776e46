package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type PatchPreference struct {
	bun.BaseModel `bun:"deployment.patch_preference"`
	BaseEntityModel
	PatchApprovalPolicy PatchApprovalPolicy
	//Patch Setting
	EnablePatching           bool
	EnableThirdPartyPatching bool
	EnableScheduling         bool
	Schedule                 PatchSchedule
	PatchApprovalSchedule    PatchSchedule
	ZeroTouchSchedule        PatchSchedule
	EnabledCategoryList      []PatchUpdateCategory
	EnabledPatchOs           []common.OsType
	LastSyncDate             int64
	IsScanRunning            bool
	//System Health Related fields
	//Criteria for tagging system as "Highly Vulnerable"
	HighlyVulnerableCriticalPatch    int
	HighlyVulnerableImportantPatches int
	HighlyVulnerableModeratePatch    int
	HighlyVulnerableLowPatch         int
	//Criteria for tagging system as "Vulnerable"
	VulnerableCriticalPatch    int
	VulnerableImportantPatches int
	VulnerableModeratePatch    int
	VulnerableLowPatch         int
	//Consider Only Approved Patch For system health
	OnlyApprovedPatch bool
	//Patch Sync Status
	IsPatchSyncRunning bool
	LastPatchSyncTime  int64
}

type PatchApprovalPolicy int

const (
	PRE_APPROVED PatchApprovalPolicy = iota + 1
	MANUAL_APPROVED
	TEST_AND_APPROVED
)

func (r PatchApprovalPolicy) String() string {
	return [...]string{"pre_approved", "manual_approved", "test_and_approve"}[r-1]
}

func (r PatchApprovalPolicy) ToRebootBehaviour(behaviour string) PatchApprovalPolicy {
	switch behaviour {
	case "pre_approved":
		return PRE_APPROVED
	case "manual_approved":
		return MANUAL_APPROVED
	case "test_and_approve":
		return TEST_AND_APPROVED
	default:
		return PRE_APPROVED
	}
}

type PatchSchedule struct {
	Type  ScheduleType `json:"type"`  // INTERVAL or DAILY
	Value int64        `json:"value"` // for INTERVAL type: 2, 4, 6 hours
}
