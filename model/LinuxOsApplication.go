package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type LinuxOsApplication struct {
	bun.BaseModel `bun:"deployment.linux_os_application"`
	BaseEntityModel
	Version         string `bun:"type:varchar(100)"`
	Distribution    string `bun:"type:varchar(500)"`
	Description     string
	Arch            string `bun:"type:varchar(100)"`
	Hidden          bool
	ProductType     PatchProductType
	NameWithDistro  string `bun:"type:varchar(500)"`
	NameWithVersion string `bun:"type:varchar(500)"`
	Platform        common.OsType
}
