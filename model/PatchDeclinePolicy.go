package model

import (
	"deployment/common"

	"github.com/uptrace/bun"
)

type PatchDeclinePolicy struct {
	bun.BaseModel `bun:"deployment.patch_decline_policy"`
	BaseEntityModel
	ComputerGroupFilter
	Description             string
	Platform                common.OsType
	ApplicationPatchSetting []ApplicationPatchSetting
}

type ApplicationPatchSetting struct {
	ProductId    int64
	SeverityList []PatchSeverity
}
