package model

import (
	"deployment/common"

	"github.com/uptrace/bun"
)

type DeploymentBundle struct {
	bun.BaseModel `bun:"deployment.deployment_bundles"`
	BaseEntityModel
	Description    string
	ReferenceIds   []int64
	Os             common.OsType
	RefModel       string `bun:"type:varchar(250)"`
	IconFile       FileMetaData
	ComplianceType ComplianceType
}

type ComplianceType int

const (
	CIS ComplianceType = iota + 1
	HIPPA
	PCI
	FIMA
	ISO
	RBI
	SOC_2
)

func (c ComplianceType) String() string {
	switch c {
	case CIS:
		return "cis"
	case HIPPA:
		return "hippa"
	case PCI:
		return "pci"
	case FIMA:
		return "fima"
	case ISO:
		return "iso"
	case RBI:
		return "rbi"
	case SOC_2:
		return "soc_2"
	default:
		return ""
	}
}

func (c ComplianceType) ToComplianceType(complianceType string) ComplianceType {
	switch complianceType {
	case "cis":
		return CIS
	case "hippa":
		return HIPPA
	case "pci":
		return PCI
	case "fima":
		return FIMA
	case "iso":
		return ISO
	case "rbi":
		return RBI
	case "soc_2":
		return SOC_2
	default:
		return 0
	}
}
