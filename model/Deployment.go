package model

import (
	"errors"
	"fmt"
	"github.com/uptrace/bun"
)

type Deployment struct {
	bun.BaseModel `bun:"deployment.deployments"`
	BaseEntityRefModel
	AgentScopeFilter
	DisplayName     string `bun:",notnull,type:varchar(1000)"`
	Description     string
	DeploymentType  DeploymentType
	DeploymentStage DeploymentStage
	StartTime       int64
	// RefIds Will be package or patch id
	RefIds                  []int64
	ComputerIds             []int64
	ComputerGroupIds        []int64
	DeploymentPolicyId      int64
	NotifyEmailIds          []string
	RetryCount              int
	IsPkgSelectAsBundle     bool
	IsSelfServiceDeployment bool
	NextExecutionTime       int64
	LastExecutionTime       int64
	Origin                  DeploymentOrigin
}

type DeploymentOrigin int

const (
	MANUAL DeploymentOrigin = iota + 1
	AUTO_PATCH_DEPLOY
	AUTO_PATCH_TEST
)

func (d DeploymentOrigin) String() string {
	switch d {
	case MANUAL:
		return "manual"
	case AUTO_PATCH_DEPLOY:
		return "patch_deploy"
	case AUTO_PATCH_TEST:
		return "patch_test"
	default:
		return "manual"
	}
}

func (d DeploymentOrigin) ToDeploymentOrigin(origin string) DeploymentOrigin {
	switch origin {
	case "manual":
		return MANUAL
	case "patch_deploy":
		return AUTO_PATCH_DEPLOY
	case "patch_test":
		return AUTO_PATCH_TEST
	default:
		return MANUAL
	}
}

type DeploymentStage int

const (
	Initiated DeploymentStage = iota + 1
	InProgress
	Completed
	Draft
	Cancelled
	Idle
	Pause
	Resume
)

func (s DeploymentStage) String() string {
	switch s {
	case Initiated:
		return "initiated"
	case InProgress:
		return "in_progress"
	case Completed:
		return "completed"
	case Draft:
		return "draft"
	case Cancelled:
		return "cancelled"
	case Idle:
		return "idle"
	case Pause:
		return "pause"
	case Resume:
		return "resume"
	}
	return "initiated"
}

func (s DeploymentStage) ToDeploymentStage(stage string) DeploymentStage {
	switch stage {
	case "initiated":
		return Initiated
	case "in_progress":
		return InProgress
	case "completed":
		return Completed
	case "draft":
		return Draft
	case "cancelled":
		return Cancelled
	case "idle":
		return Idle
	case "pause":
		return Pause
	case "resume":
		return Resume
	default:
		return Initiated
	}
}

type DeploymentType int

const (
	Install DeploymentType = iota + 1
	Uninstall
	Upgrade
)

func (dt DeploymentType) String() string {
	switch dt {
	case Install:
		return "install"
	case Uninstall:
		return "uninstall"
	case Upgrade:
		return "upgrade"
	default:
		return ""
	}
}

func (dt DeploymentType) ToDeploymentType(deploymentTypeStr string) (DeploymentType, error) {
	switch deploymentTypeStr {
	case "install":
		return Install, nil
	case "uninstall":
		return Uninstall, nil
	case "upgrade":
		return Upgrade, nil
	default:
		return Install, errors.New(fmt.Sprintf("invalid deployment type value '%s'", deploymentTypeStr))
	}
}
