package model

type PatchFileData struct {
	FileName       string `json:"fileName"`
	RefName        string `json:"refName"`
	Url            string `json:"url"`
	DownloadUrl    string `json:"downloadUrl"`
	Size           int64  `json:"size"`
	ReleaseDate    int64  `json:"releaseDate"`
	Language       int64  `json:"language"`
	ChecksumSHA256 string `json:"checksumSHA256"`
	LocationId     int64  `json:"locationId"`
	FetchFromMSU   bool   `json:"fetchFromMSU"`
}

type PatchFileDownloadStatus struct {
	PatchFileData    []PatchFileData
	PkgFileData      []FileMetaData
	IsDownloadFailed bool
	Error            string
}
