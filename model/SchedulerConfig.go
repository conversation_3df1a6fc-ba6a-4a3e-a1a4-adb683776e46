package model

type SchedulerConfig struct {
	StartAt                 int64
	ScheduleType            ScheduleType
	RecurringStartingHour   int64
	RecurringStartingMinute int64
	SpecificInterval        int64
	DaysOfMonth             []int64
	DaysOfWeek              []int64
}

type ScheduleType int

const (
	DAILY ScheduleType = iota + 1
	INTERVAL
	MONTHLY
	ONCE
	WEEKLY
)

func (s ScheduleType) String() string {
	return [...]string{"daily", "interval", "monthly", "once", "weekly"}[s-1]
}

func (s ScheduleType) ToSchedulerType(schedulerType string) ScheduleType {
	switch schedulerType {
	case "daily":
		return DAILY
	case "interval":
		return INTERVAL
	case "monthly":
		return MONTHLY
	case "once":
		return ONCE
	case "weekly":
		return WEEKLY
	default:
		return ONCE
	}
}
