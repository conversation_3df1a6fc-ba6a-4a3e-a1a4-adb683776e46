package model

type AgentScope int

const (
	AllAssets AgentScope = iota + 1
	SpecificAssets
	SpecificDepartment
	Windows
	Linux
	Mac
)

type AgentScopeFilter struct {
	Scope            int64
	Assets           []int64
	PlatformVersions []string
}

func Int64ToAgentScope(value int64) AgentScope {
	switch value {
	case int64(AllAssets):
		return AllAssets
	case int64(SpecificAssets):
		return SpecificAssets
	case int64(SpecificDepartment):
		return SpecificDepartment
	case int64(Windows):
		return Windows
	case int64(Linux):
		return Linux
	case int64(Mac):
		return Mac
	default:
		return 0
	}
}

func (a AgentScope) String() string {
	switch a {
	case AllAssets:
		return "All Endpoints"
	case SpecificAssets:
		return "Specific Endpoints"
	case SpecificDepartment:
		return "Specific Department"
	case Windows:
		return "Windows"
	case Linux:
		return "Linux"
	case Mac:
		return "Mac"
	default:
		return ""
	}
}

type ComputerGroupFilter struct {
	AgentScopeFilter
	CGFilterType     CGFilterType
	ComputerGroupIds []int64
}

type CGFilterType int

const (
	AllCg CGFilterType = iota + 1
	SpecificCg
	Scope
)

func (r CGFilterType) String() string {
	return [...]string{"all_cg", "specific_cg", "scope"}[r-1]
}

func (r CGFilterType) ToCGFilterType(behaviour string) CGFilterType {
	switch behaviour {
	case "all_cg":
		return AllCg
	case "specific_cg":
		return SpecificCg
	case "scope":
		return Scope
	default:
		return Scope
	}
}
