package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type PatchOsApplication struct {
	bun.BaseModel `bun:"deployment.patch_os_applications"`
	BaseEntityModel
	ProductFamily     string
	CompanyName       string
	ProductFamilyUUID string
	UUID              string
	ProductType       PatchProductType
	Vendor            string
	Platform          common.OsType
	CategoryType      string
	Description       string
	Hidden            bool
	GroupName         string
	ServicePack       string
}

type PatchProductType int

const (
	OS PatchProductType = iota + 1
	APPLICATION
)

func (t PatchProductType) String() string {
	switch t {
	case OS:
		return "os"
	case APPLICATION:
		return "application"
	default:
		return "os"
	}
}

func (t PatchProductType) ToPatchProductType(productType string) PatchProductType {
	switch productType {
	case "os":
		return OS
	case "application":
		return APPLICATION
	default:
		return OS
	}
}
