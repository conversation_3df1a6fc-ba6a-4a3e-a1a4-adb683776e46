package model

import (
	"deployment/constant"
	"github.com/uptrace/bun"
)

type Compliance struct {
	bun.BaseModel `bun:"deployment.compliances"`
	BaseEntityModel
	DisplayName   string `bun:",notnull,type:varchar(1000)"`
	Bindings      string
	RuleType      ComplianceRuleType
	Description   string
	Audit         string
	Remediation   string
	Impact        ComplianceImpact
	Scope         int64
	Rules         []ComplianceRule
	Tags          []string
	Disable       bool
	ExecutionType ExecutionType
}

type ComplianceRuleType int

const (
	AAA ComplianceRuleType = iota + 1
	CONFIGURATION
	NETWORKING
	PATCHING
	SECURITY
)

func (c ComplianceRuleType) String() string {
	switch c {
	case AAA:
		return "aaa"
	case CONFIGURATION:
		return "configuration"
	case NETWORKING:
		return "networking"
	case PATCHING:
		return "patching"
	case SECURITY:
		return "security"
	default:
		return ""
	}
}

func (c ComplianceRuleType) ToComplianceRuleType(rule string) ComplianceRuleType {
	switch rule {
	case "aaa":
		return AAA
	case "configuration":
		return CONFIGURATION
	case "networking":
		return NETWORKING
	case "patching":
		return PATCHING
	case "security":
		return SECURITY
	default:
		return 0
	}
}

type ComplianceImpact int

const (
	HIGH ComplianceImpact = iota + 1
	MEDIUM
	LOW
)

func (i ComplianceImpact) String() string {
	switch i {
	case HIGH:
		return "high"
	case MEDIUM:
		return "medium"
	case LOW:
		return "low"
	default:
		return ""
	}
}

func (i ComplianceImpact) ToComplianceImpact(impact string) ComplianceImpact {
	switch impact {
	case "high":
		return HIGH
	case "medium":
		return MEDIUM
	case "low":
		return LOW
	default:
		return 0
	}
}

type RuleCondition int

const (
	Equal RuleCondition = iota + 1
	NotEqual
	GreaterThan
	GreaterThanEqual
	LessThan
	LessThanEqual
	Contains
	NotContains
	StartWith
	EndWith
)

func (i RuleCondition) String() string {
	switch i {
	case Equal:
		return "Equal"
	case NotEqual:
		return "NotEqual"
	case GreaterThan:
		return "GreaterThan"
	case GreaterThanEqual:
		return "GreaterThanEqual"
	case LessThan:
		return "LessThan"
	case LessThanEqual:
		return "LessThanEqual"
	case Contains:
		return "Contains"
	case NotContains:
		return "NotContains"
	case StartWith:
		return "StartWith"
	case EndWith:
		return "EndWith"
	default:
		return ""
	}
}

func (i RuleCondition) ToRuleCondition(condition string) RuleCondition {
	switch condition {
	case "Equal":
		return Equal
	case "NotEqual":
		return NotEqual
	case "GreaterThan":
		return GreaterThan
	case "GreaterThanEqual":
		return GreaterThanEqual
	case "LessThan":
		return LessThan
	case "LessThanEqual":
		return LessThanEqual
	case "Contains":
		return Contains
	case "NotContains":
		return NotContains
	case "StartWith":
		return StartWith
	case "EndWith":
		return EndWith
	default:
		return 0
	}
}

type ComplianceRule struct {
	OrderId                  int
	CommandType              CommandType
	Command                  string
	Key                      string
	ComplianceRuleConditions []ComplianceRuleConditions
	Condition                constant.Condition
	ScriptFile               FileMetaData
}

type ComplianceRuleConditions struct {
	ConditionValue string
	RuleCondition  RuleCondition
	Condition      constant.Condition
}
