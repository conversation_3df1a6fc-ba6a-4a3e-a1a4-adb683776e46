package model

import "github.com/uptrace/bun"

type AssetPatchRelation struct {
	bun.BaseModel `bun:"deployment.asset_patch_relation"`
	BaseEntityModel
	PatchId         int64      `bun:"patch_id"`
	AssetId         int64      `bun:"asset_id"`
	PatchState      PatchState `bun:"patch_state"`
	IsOld           bool
	IsDeclined      bool
	ExceptionType   ExceptionType
	ExceptionReason string
}

type ExceptionType int

const (
	AcceptableRisk ExceptionType = iota + 1
	FalsePositive
)

func (s ExceptionType) String() string {
	return [...]string{"", "acceptable_risk", "false_positive"}[s]
}

func (s ExceptionType) ToExceptionType(extype string) ExceptionType {
	switch extype {
	case "acceptable_risk":
		return AcceptableRisk
	case "false_positive":
		return FalsePositive
	default:
		return AcceptableRisk
	}
}

type PatchState int

const (
	Installed PatchState = iota + 1
	Missing
	Ignored
)

func (s PatchState) String() string {
	return [...]string{"installed", "missing", "ignored"}[s-1]
}

func (s PatchState) ToPatchState(patchState string) PatchState {
	switch patchState {
	case "installed":
		return Installed
	case "missing":
		return Missing
	case "ignored":
		return Ignored
	default:
		return Installed
	}
}
