package model

import (
	"errors"
	"fmt"

	"github.com/uptrace/bun"
)

type AgentTask struct {
	bun.BaseModel `bun:"deployment.agent_tasks"`
	BaseEntityRefModel
	AgentId              int64
	DeploymentId         int64
	TaskType             AgentTaskType
	CustomTaskDetails    map[string]interface{}
	TaskStatus           AgentTaskStatus
	TaskResult           string
	HasMultipleExecution bool
}

type AgentTaskStatus int

const (
	TaskWaiting AgentTaskStatus = iota + 1
	TaskReadyToDeploy
	TaskInitiated
	TaskInProgress
	TaskSuccess
	TaskFailed
	TaskCancelled
	TaskRebootRequired
)

func (s AgentTaskStatus) String() string {
	switch s {
	case TaskWaiting:
		return "waiting"
	case TaskReadyToDeploy:
		return "ready_to_deploy"
	case TaskInitiated:
		return "initiated"
	case TaskInProgress:
		return "in_progress"
	case TaskSuccess:
		return "success"
	case TaskFailed:
		return "failed"
	case TaskCancelled:
		return "cancelled"
	case TaskRebootRequired:
		return "reboot_required"
	}
	return ""
}

func (s AgentTaskStatus) ToTaskStatus(status string) (AgentTaskStatus, error) {
	switch status {
	case "waiting":
		return TaskWaiting, nil
	case "ready_to_deploy":
		return TaskReadyToDeploy, nil
	case "initiated":
		return TaskInitiated, nil
	case "in_progress":
		return TaskInProgress, nil
	case "success":
		return TaskSuccess, nil
	case "failed":
		return TaskFailed, nil
	case "cancelled":
		return TaskCancelled, nil
	case "reboot_required":
		return TaskRebootRequired, nil
	default:
		return TaskWaiting, errors.New(fmt.Sprintf("invalid task status value '%s'", status))
	}
}

func AgentTaskStatusFromInt(value int) (AgentTaskStatus, error) {
	switch value {
	case int(TaskWaiting):
		return TaskWaiting, nil
	case int(TaskReadyToDeploy):
		return TaskReadyToDeploy, nil
	case int(TaskInitiated):
		return TaskInitiated, nil
	case int(TaskInProgress):
		return TaskInProgress, nil
	case int(TaskSuccess):
		return TaskSuccess, nil
	case int(TaskFailed):
		return TaskFailed, nil
	case int(TaskCancelled):
		return TaskCancelled, nil
	case int(TaskRebootRequired):
		return TaskRebootRequired, nil
	default:
		return TaskWaiting, fmt.Errorf("invalid task status value '%d'", value)
	}
}

type AgentTaskType int

const (
	DEPLOYMENT AgentTaskType = iota + 1
	PATCH_SCAN
	QUERY_EXECUTION
	SYSTEM_ACTION
	HOLD_DEPLOYMENT_EXECUTION
	APPLICATION_CONTROL_POLICY
	COMPLIANCE_TEST
	QUICK_CHECK_TEST
)

func (t AgentTaskType) String() string {
	switch t {
	case DEPLOYMENT:
		return "deployment"
	case PATCH_SCAN:
		return "patch_scan"
	case QUERY_EXECUTION:
		return "query_execution"
	case SYSTEM_ACTION:
		return "system_action"
	case HOLD_DEPLOYMENT_EXECUTION:
		return "hold_deployment_execution"
	case APPLICATION_CONTROL_POLICY:
		return "application_control_policy"
	case COMPLIANCE_TEST:
		return "compliance_test"
	case QUICK_CHECK_TEST:
		return "quick_check_test"
	default:
		return "deployment"
	}
}

func (t AgentTaskType) ToTaskType(taskType string) AgentTaskType {
	switch taskType {
	case "deployment":
		return DEPLOYMENT
	case "patch_scan":
		return PATCH_SCAN
	case "query_execution":
		return QUERY_EXECUTION
	case "system_action":
		return SYSTEM_ACTION
	case "hold_deployment_execution":
		return HOLD_DEPLOYMENT_EXECUTION
	case "application_control_policy":
		return APPLICATION_CONTROL_POLICY
	case "compliance_test":
		return COMPLIANCE_TEST
	case "quick_check_test":
		return QUICK_CHECK_TEST
	default:
		return DEPLOYMENT
	}
}
