package model

import (
	"deployment/common"
	"fmt"

	"github.com/uptrace/bun"
)

type Configuration struct {
	bun.BaseModel `bun:"deployment.configurations"`
	BaseEntityModel
	DisplayName          string `bun:",notnull,type:varchar(1000)"`
	Description          string
	Os                   common.OsType
	Arch                 common.OsArchitecture
	ConfigurationType    ExecutionType
	ConfigurationActions []ConfigurationAction
	SelfServiceSupported bool
	IsRemediation        bool
	Tags                 []string
}

type ConfigurationAction struct {
	OrderId     int
	CommandType CommandType
	Command     string
	ScriptFile  FileMetaData
}

type ExecutionType int

const (
	COMMAND ExecutionType = iota + 1
	SCRIPT
)

type CommandType int

const (
	PS CommandType = iota + 1
	REG
	CMD
)

func (ct ExecutionType) String() string {
	switch ct {
	case COMMAND:
		return "command"
	case SCRIPT:
		return "script"
	default:
		return ""
	}
}

func (ct ExecutionType) ToExecutionType(s string) (ExecutionType, error) {
	switch s {
	case "command":
		return COMMAND, nil
	case "script":
		return SCRIPT, nil
	default:
		return -1, fmt.Errorf("invalid ExecutionType: %s", s)
	}
}

func (ct CommandType) String() string {
	switch ct {
	case PS:
		return "ps"
	case REG:
		return "reg"
	case CMD:
		return "cmd"
	default:
		return ""
	}
}

func (ct CommandType) ToCommandType(s string) (CommandType, error) {
	switch s {
	case "ps":
		return PS, nil
	case "reg":
		return REG, nil
	case "cmd":
		return CMD, nil
	default:
		return -1, fmt.Errorf("invalid CommandType: %s", s)
	}
}
