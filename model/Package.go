package model

import (
	"deployment/common"
	"github.com/uptrace/bun"
)

type Package struct {
	bun.BaseModel `bun:"deployment.packages"`
	BaseEntityModel
	DisplayName           string `bun:",notnull,type:varchar(1000)"`
	Description           string
	Os                    common.OsType
	Arch                  common.OsArchitecture
	Version               string
	PkgType               common.PkgType
	PkgLocation           common.PkgLocation
	PkgFilePath           FileMetaData
	PkgFilePathList       []FileMetaData
	InstallCommand        string
	UninstallCommand      string
	UpgradeCommand        string
	IconFile              FileMetaData
	SelfServiceSupported  bool
	UseUserDefinedCommand bool
	Tags                  []string
}
