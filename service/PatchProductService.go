package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type PatchProductService struct {
	Repository *repository.PatchProductRepository
}

func NewPatchProductService() *PatchProductService {
	return &PatchProductService{Repository: repository.NewPatchProductRepository()}
}

func (service PatchProductService) convertToModel(patchProductRest rest.PatchProductRest) (*model.PatchProduct, error) {
	return &model.PatchProduct{
		BaseEntityModel: ConvertToBaseEntityModel(patchProductRest.BaseEntityRest),
		UUID:            patchProductRest.UUID,
		ServicePack:     patchProductRest.ServicePack,
		OriginalText:    patchProductRest.OriginalText,
		DisplayName:     patchProductRest.DisplayName,
		Description:     patchProductRest.Description,
		SubCategory:     patchProductRest.SubCategory,
		SubCategoryUUID: patchProductRest.SubCategoryUUID,
	}, nil
}

func (service PatchProductService) convertToRest(patchProduct model.PatchProduct) rest.PatchProductRest {
	return rest.PatchProductRest{
		BaseEntityRest:  ConvertToBaseEntityRest(patchProduct.BaseEntityModel),
		UUID:            patchProduct.UUID,
		ServicePack:     patchProduct.ServicePack,
		OriginalText:    patchProduct.OriginalText,
		DisplayName:     patchProduct.DisplayName,
		Description:     patchProduct.Description,
		SubCategory:     patchProduct.SubCategory,
		SubCategoryUUID: patchProduct.SubCategoryUUID,
	}
}

func (service PatchProductService) BeforeCreate(productRest rest.PatchProductRest) common.CustomError {
	product, err := service.Repository.GetByUuid(productRest.UUID)
	if err == nil && product.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service PatchProductService) Create(productRest rest.PatchProductRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create patch product")
	if productRest.CreatedTime == 0 {
		productRest.CreatedTime = common.CurrentMillisecond()
	}
	productRest.CreatedById = common.GetUserFromCallContext()

	patchProduct, err := service.convertToModel(productRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch product: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(patchProduct)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch product: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create patch product process completed successfully")
	return id, common.CustomError{}
}

func (service PatchProductService) Update(id int64, restModel rest.PatchProductRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update patch product with id - %v", id))
	patchProduct, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating patch product for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	if restModel.UpdatedTime == 0 {
		patchProduct.UpdatedTime = common.CurrentMillisecond()
	} else {
		patchProduct.UpdatedTime = restModel.UpdatedTime
	}
	patchProduct.UpdatedById = common.GetUserFromCallContext()

	service.PerformPartialUpdate(restModel, patchProduct)

	_, err = service.Repository.Update(&patchProduct)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating patch product for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update patch product with id - %v", id))
	return true, common.CustomError{}
}

func (service PatchProductService) PerformPartialUpdate(restModel rest.PatchProductRest, patchProduct model.PatchProduct) {
	if restModel.UUID != "" {
		patchProduct.UUID = restModel.UUID
	}
	if restModel.ServicePack != "" {
		patchProduct.ServicePack = restModel.ServicePack
	}
	if restModel.OriginalText != "" {
		patchProduct.OriginalText = restModel.OriginalText
	}
	if restModel.DisplayName != "" {
		patchProduct.DisplayName = restModel.DisplayName
	}
	if restModel.Description != "" {
		patchProduct.Description = restModel.Description
	}
	if restModel.SubCategory != "" {
		patchProduct.SubCategory = restModel.SubCategory
	}
	if restModel.SubCategoryUUID != "" {
		patchProduct.SubCategoryUUID = restModel.SubCategoryUUID
	}
}

func (service PatchProductService) GetPatchProduct(id int64, includeArchive bool) (rest.PatchProductRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get patch product for id %v", id))
	var patchProductRest rest.PatchProductRest
	patchProduct, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting patch product for id - %v, Error: %s", id, err.Error()))
		return patchProductRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get patch product for id %v", id))
	return service.convertToRest(patchProduct), nil
}

func (service PatchProductService) DeletePatchProduct(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete patch product for id - %v", id))
	_, err := service.Repository.GetById(id, permanentDelete)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting patch product to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.PermanentDeleteById(id)

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting patch product for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete patch product for id - %v", id))
	return true, nil
}

func (service PatchProductService) BulkCreateOrUpdate(restList []rest.PatchProductRest) {
	if len(restList) > 0 {
		for _, patchProductRest := range restList {
			product, err := service.Repository.GetByUuid(patchProductRest.UUID)
			if err == nil && product.Id != 0 {
				if product.UpdatedTime != patchProductRest.UpdatedTime {
					service.Update(product.Id, patchProductRest)
				}
			} else {
				patchProductRest.Id = 0
				service.Create(patchProductRest)
			}
		}
	}
}

func (service PatchProductService) GetAllPatchProducts(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_PRODUCT.String(), true, "")
	var responsePage rest.ListResponseRest
	var patchProductPageList []model.PatchProduct
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_PRODUCT.String(), false, "")
		patchProductPageList, err = service.Repository.GetAllPatchProducts(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(patchProductPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service PatchProductService) convertListToRest(patchProductList []model.PatchProduct) []rest.PatchProductRest {
	var restList []rest.PatchProductRest
	for _, patchProduct := range patchProductList {
		restList = append(restList, service.convertToRest(patchProduct))
	}
	return restList
}
