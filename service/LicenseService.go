package service

import (
	"deployment/cache"
	"deployment/common"
	"deployment/logger"
	"encoding/json"
)

type LicenseService struct {
}

func NewLicenseService() *LicenseService {
	return &LicenseService{}
}

func (service LicenseService) ValidLicense() bool {
	license := service.getLicense()
	if license != nil {
		remainingDays := common.ConvertToInt64(license["remaining_days"])
		if remainingDays > 0 {
			cache.SetLicenseValid(true)
			cache.ProductCache.Set("product", license["license_product"].(string))
		} else {
			cache.SetLicenseValid(false)
		}
	}
	return cache.IsValidLicense()
}

func (service LicenseService) getLicense() map[string]interface{} {
	var payload []byte
	var result map[string]interface{}
	url := common.MainServerUrl()
	tokenUrl := url + "/api/token"
	credential := map[string]string{"username": "zirozen", "password": "Zir@zen2@24"}
	payload, _ = json.Marshal(credential)
	response, success := common.ExecutePostRequest(tokenUrl, payload, nil)
	if success && response != nil && len(response) > 0 {
		responseMap := make(map[string]interface{})
		err := json.Unmarshal(response, &responseMap)
		if err == nil && len(responseMap["access-token"].(string)) > 0 {
			token := responseMap["access-token"].(string)
			apiUrl := url + "/api/settings/license"
			response, success = common.ExecuteGetRequest(apiUrl, map[string]string{"Authorization": "Bearer " + token})
			if success && response != nil && len(response) > 0 {
				responseMap = make(map[string]interface{})
				err := json.Unmarshal(response, &responseMap)
				if err != nil {
					logger.ServiceLogger.Error("[getLicense]Failed to parse server response." + err.Error())
				}
				if status, ok := responseMap["status"]; ok {
					if status == "success" {
						if license, ok := responseMap["result"].(map[string]interface{}); ok {
							// Now you can use licenseMap like a regular map
							result = license
						} else {
							logger.ServiceLogger.Error("Failed to convert license to map[string]interface{}")
						}
					} else {
						logger.ServiceLogger.Error("Failed to retrieve license details..")
					}
				}
			}
		} else {
			logger.ServiceLogger.Debug("Failed to parse generated access token response.")
		}
	} else {
		logger.ServiceLogger.Debug("Failed to generate access token.")
	}
	return result
}
