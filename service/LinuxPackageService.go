package service

import (
	"deployment/common"
	"deployment/logger"
	pch_model "deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type LinuxPackageService struct {
	Repository *repository.LinuxPackageRepository
}

func NewLinuxPackageService() *LinuxPackageService {
	return &LinuxPackageService{Repository: repository.NewLinuxPackageRepository()}
}

func (service LinuxPackageService) convertToModel(restModel rest.LinuxPackageRest) *pch_model.LinuxPackage {
	return &pch_model.LinuxPackage{
		BaseEntityModel: ConvertToBaseEntityModel(restModel.BaseEntityRest),
		Description:     restModel.Description,
		Section:         restModel.Section,
		Distribution:    restModel.Distribution,
		OldVersion:      restModel.OldVersion,
		Arch:            restModel.Arch,
		ReleaseDate:     restModel.ReleaseDate,
		Version:         restModel.Version,
		NameWithDistro:  restModel.NameWithDistro,
	}
}

func (service LinuxPackageService) convertToRest(data pch_model.LinuxPackage) rest.LinuxPackageRest {
	return rest.LinuxPackageRest{
		BaseEntityRest: ConvertToBaseEntityRest(data.BaseEntityModel),
		Description:    data.Description,
		Section:        data.Section,
		Distribution:   data.Distribution,
		OldVersion:     data.OldVersion,
		Arch:           data.Arch,
		ReleaseDate:    data.ReleaseDate,
		Version:        data.Version,
		NameWithDistro: data.NameWithDistro,
	}
}

func (service LinuxPackageService) convertListToRest(patchList []pch_model.LinuxPackage) []rest.LinuxPackageRest {
	var patchRestList []rest.LinuxPackageRest
	if len(patchList) != 0 {
		for _, patch := range patchList {
			patchRest := service.convertToRest(patch)
			patchRestList = append(patchRestList, patchRest)
		}
	}
	return patchRestList
}

func (service LinuxPackageService) BulkCreateOrUpdate(restList []rest.LinuxPackageRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patchRest.NameWithDistro = patchRest.Name + "@_@" + patchRest.Distribution + "@_@" + patchRest.Version
			patch, err := service.Repository.GetByNameWithDistro(patchRest.NameWithDistro)
			if err == nil && patch.Id != 0 {
				if patch.UpdatedTime != patchRest.UpdatedTime {
					service.Update(patch.Id, patchRest)
				}
			} else {
				patchRest.Id = 0
				service.Create(patchRest)
			}
		}
	}
}

func (service LinuxPackageService) Create(rest rest.LinuxPackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create LinuxPackage")

	// Handle base entity fields
	if rest.CreatedTime == 0 {
		rest.CreatedTime = common.CurrentMillisecond()
	}
	rest.CreatedById = common.GetUserFromCallContext()
	// Convert rest to model
	data := service.convertToModel(rest)

	// Call repository create method
	id, err := service.Repository.Create(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating LinuxPackage: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create LinuxPackage process completed successfully")
	return id, common.CustomError{}
}

func (service LinuxPackageService) Update(id int64, rest rest.LinuxPackageRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update LinuxPackage with id - %v", id))

	// Retrieve existing data
	data, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating LinuxPackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Handle base entity fields
	if rest.UpdatedTime == 0 {
		data.UpdatedTime = common.CurrentMillisecond()
	} else {
		data.UpdatedTime = rest.UpdatedTime
	}
	data.UpdatedById = common.GetUserFromCallContext()
	// Update fields as needed
	PerformPartialUpdateForBase(&data.BaseEntityModel, rest.BaseEntityRest)
	data.Description = rest.Description
	data.Section = rest.Section
	data.Distribution = rest.Distribution
	data.OldVersion = rest.OldVersion
	data.Arch = rest.Arch
	data.ReleaseDate = rest.ReleaseDate
	data.Version = rest.Version

	// Call repository update method
	_, err = service.Repository.Update(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating LinuxPackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update LinuxPackage with id - %v", id))
	return true, common.CustomError{}
}

func (service LinuxPackageService) Delete(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete LinuxPackage for id - %v", id))

	// Check if the data exists
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting LinuxPackage to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	// Call repository delete method
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting LinuxPackage for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete LinuxPackage for id - %v", id))
	return true, nil
}

func (service LinuxPackageService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.LINUX_PACKAGES.String(), true, "")
	var responsePage rest.ListResponseRest
	var dataList []pch_model.LinuxPackage
	var err error

	// Count total records
	count := service.Repository.CountByQuery(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		// Fetch data based on search filter
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.LINUX_PACKAGES.String(), false, "")
		dataList, err = service.Repository.GetAllLinuxPackageByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(dataList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}

	responsePage.TotalCount = count
	return responsePage, nil
}
