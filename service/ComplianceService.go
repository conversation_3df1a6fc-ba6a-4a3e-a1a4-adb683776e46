package service

import (
	"deployment/common"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"github.com/google/uuid"
	"net/http"
	"reflect"
)

type ComplianceService struct {
	Repository *repository.ComplianceRepository
}

func NewComplianceService() *ComplianceService {
	return &ComplianceService{
		Repository: repository.NewComplianceRepository(),
	}
}

func (service ComplianceService) convertToModel(configRest rest.ComplianceRest) *model.Compliance {
	var ruleType model.ComplianceRuleType
	ruleType = ruleType.ToComplianceRuleType(configRest.RuleType)

	var impact model.ComplianceImpact
	impact = impact.ToComplianceImpact(configRest.Impact)

	var executionType model.ExecutionType
	executionType, _ = executionType.ToExecutionType(configRest.ExecutionType)

	configRest.Name = uuid.New().String()
	return &model.Compliance{
		BaseEntityModel: ConvertToBaseEntityModel(configRest.BaseEntityRest),
		DisplayName:     configRest.DisplayName,
		Bindings:        configRest.Bindings,
		RuleType:        ruleType,
		Description:     configRest.Description,
		Audit:           configRest.Audit,
		Remediation:     configRest.Remediation,
		Impact:          impact,
		Scope:           configRest.Scope,
		Rules:           rest.ToComplianceRuleList(configRest.Rules),
		Tags:            configRest.Tags,
		Disable:         configRest.Disable,
		ExecutionType:   executionType,
	}
}

func (service ComplianceService) convertToRest(configInfo model.Compliance) rest.ComplianceRest {
	ruleExecutionType := "cmd"
	if len(configInfo.Rules) > 0 {
		for _, rule := range configInfo.Rules {
			if rule.CommandType.String() == "reg" {
				ruleExecutionType = "reg"
				break
			}
		}
	}
	return rest.ComplianceRest{
		BaseEntityRest:    ConvertToBaseEntityRest(configInfo.BaseEntityModel),
		DisplayName:       configInfo.DisplayName,
		Bindings:          configInfo.Bindings,
		RuleType:          configInfo.RuleType.String(),
		Description:       configInfo.Description,
		Audit:             configInfo.Audit,
		Remediation:       configInfo.Remediation,
		Impact:            configInfo.Impact.String(),
		Scope:             configInfo.Scope,
		Rules:             rest.ToComplianceRuleRestList(configInfo.Rules),
		Tags:              configInfo.Tags,
		Disable:           configInfo.Disable,
		ExecutionType:     configInfo.ExecutionType.String(),
		RuleExecutionType: ruleExecutionType,
	}
}

func (service ComplianceService) performPartialUpdate(domainModel *model.Compliance, restModel rest.ComplianceRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchMap["description"] != nil && domainModel.Description != restModel.Description {
		common.PrepareInDiffMap("description", domainModel.Description, restModel.Description, &diffMap)
		domainModel.Description = restModel.Description
	}

	if restModel.PatchMap["displayName"] != nil && domainModel.DisplayName != restModel.DisplayName {
		common.PrepareInDiffMap("display_name", domainModel.DisplayName, restModel.DisplayName, &diffMap)
		domainModel.DisplayName = restModel.DisplayName
	}

	if restModel.PatchMap["bindings"] != nil && domainModel.Bindings != restModel.Bindings {
		common.PrepareInDiffMap("bindings", domainModel.Bindings, restModel.Bindings, &diffMap)
		domainModel.Bindings = restModel.Bindings
	}

	if restModel.PatchMap["ruleType"] != nil && domainModel.RuleType.String() != restModel.RuleType {
		var ruleType model.ComplianceRuleType
		common.PrepareInDiffMap("rule_type", domainModel.RuleType.String(), restModel.RuleType, &diffMap)
		domainModel.RuleType = ruleType.ToComplianceRuleType(restModel.RuleType)
	}

	if restModel.PatchMap["executionType"] != nil {
		executionType, _ := domainModel.ExecutionType.ToExecutionType(restModel.ExecutionType)
		if domainModel.ExecutionType != executionType {
			common.PrepareInDiffMap("execution_type", domainModel.ExecutionType.String(), restModel.ExecutionType, &diffMap)
			domainModel.ExecutionType = executionType
		}
	}

	if restModel.PatchMap["impact"] != nil && domainModel.Impact.String() != restModel.Impact {
		var impact model.ComplianceImpact
		common.PrepareInDiffMap("rule_type", domainModel.Impact.String(), restModel.Impact, &diffMap)
		domainModel.Impact = impact.ToComplianceImpact(restModel.Impact)
	}

	if restModel.PatchMap["audit"] != nil && domainModel.Audit != restModel.Audit {
		common.PrepareInDiffMap("audit", domainModel.Audit, restModel.Audit, &diffMap)
		domainModel.Audit = restModel.Audit
	}

	if restModel.PatchMap["remediation"] != nil && domainModel.Remediation != restModel.Remediation {
		common.PrepareInDiffMap("remediation", domainModel.Remediation, restModel.Remediation, &diffMap)
		domainModel.Remediation = restModel.Remediation
	}

	if restModel.PatchMap["scope"] != nil && domainModel.Scope != restModel.Scope {
		common.PrepareInDiffMap("scope", domainModel.Scope, restModel.Scope, &diffMap)
		domainModel.Scope = restModel.Scope
	}

	if restModel.PatchMap["rules"] != nil && !reflect.DeepEqual(domainModel.Rules, restModel.Rules) {
		common.PrepareInDiffMap("rules", domainModel.Rules, restModel.Rules, &diffMap)
		domainModel.Rules = rest.ToComplianceRuleList(restModel.Rules)
	}

	if restModel.PatchMap["tags"] != nil && !reflect.DeepEqual(domainModel.Tags, restModel.Tags) {
		common.PrepareInDiffMap("tags", domainModel.Tags, restModel.Tags, &diffMap)
		domainModel.Tags = restModel.Tags
	}

	if restModel.PatchMap["disable"] != nil && domainModel.Disable != restModel.Disable {
		common.PrepareInDiffMap("disable", domainModel.Disable, restModel.Disable, &diffMap)
		domainModel.Disable = restModel.Disable
	}

	return diffMap, len(diffMap) != 0
}

func (service ComplianceService) Create(rest rest.ComplianceRest) (int64, error) {
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	compliance := service.convertToModel(rest)
	id, err := service.Repository.Create(compliance)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service ComplianceService) CreateComplianceTaskResult(taskResults *model.ComplianceTaskResult) (int64, error) {
	taskResults.CreatedById = common.GetUserFromCallContext()
	id, err := service.Repository.CreateComplianceTaskResult(taskResults)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service ComplianceService) Update(id int64, restModel rest.ComplianceRest) (bool, common.CustomError) {
	compliance, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	_, isUpdatable := service.performPartialUpdate(&compliance, restModel)
	if isUpdatable {
		compliance.UpdatedTime = common.CurrentMillisecond()
		compliance.UpdatedById = common.GetUserFromCallContext()
		_, err := service.Repository.Update(&compliance)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		return true, common.CustomError{}
	} else {
		return isUpdatable, common.CustomError{}
	}
}

func (service ComplianceService) GetCompliance(id int64, includeArchived bool) (rest.ComplianceRest, error) {
	var complianceRest rest.ComplianceRest
	compliance, err := service.Repository.GetById(id, includeArchived)
	if err != nil {
		return complianceRest, err
	}
	return service.convertToRest(compliance), nil
}

func (service ComplianceService) DeleteCompliance(id int64, permanentDelete bool) (bool, error) {
	success := false
	var err error
	if permanentDelete {
		success, err = service.Repository.PermanentDeleteById(id)
		success, err = service.Repository.PermanentDeleteComplianceTaskResult(0, id, 0, 0)
	} else {
		success, err = service.Repository.DeleteById(id)
		success, err = service.Repository.PermanentDeleteComplianceTaskResult(0, id, 0, 0)
	}
	if err != nil {
		return success, err
	}
	return true, nil
}

func (service ComplianceService) GetAllCompliance(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var CompliancePageList []model.Compliance
	var err error
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.COMPLIANCES.String(), true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.COMPLIANCES.String(), false, "")
		CompliancePageList, err = service.Repository.GetAllCompliance(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.convertListToRestCompliance(CompliancePageList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service ComplianceService) convertListToRestCompliance(compliances []model.Compliance) []rest.ComplianceRest {
	var ComplianceRestList []rest.ComplianceRest
	if len(compliances) != 0 {
		for _, Compliance := range compliances {
			ComplianceRest := service.convertToRest(Compliance)
			ComplianceRestList = append(ComplianceRestList, ComplianceRest)
		}
	}
	return ComplianceRestList
}
