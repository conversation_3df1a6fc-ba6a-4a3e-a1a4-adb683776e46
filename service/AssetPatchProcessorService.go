package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/hashicorp/go-version"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
)

var onlyHotfixPattern = regexp.MustCompile(`(?m)KB\d+`)

var versionPattern = regexp.MustCompile(`\d+([.-]\d+)*`)

var defenderVersionPattern = regexp.MustCompile(`\((.*?)\)`)

type AssetPatchProcessorService struct {
}

func NewAssetPatchProcessorService() *AssetPatchProcessorService {
	return &AssetPatchProcessorService{}
}

func (processorService AssetPatchProcessorService) ProcessWindowsPatch(assetId int64, scannedPatchData rest.ScannedPatchData) {
	windowsPatchService := NewWindowsPatchService()

	asset, err := NewAgentService().GetAssetMetaDataById(assetId)
	if err == nil {
		processorService.createOrUpdateOsApplication(asset)
	}

	processorService.processPatchDataForWindowsPatch(assetId, &scannedPatchData)

	if scannedPatchData.PatchData == nil {
		patchAsset, _ := NewAssetService().GetAsset(assetId)

		var sysInfo common.SysInfo
		sysInfo = sysInfo.GetSysInfo(patchAsset.SysInfo)

		processorService.processDefenderInfo(&scannedPatchData, windowsPatchService, patchAsset)

		processorService.determineInstalledMissingPatches(&scannedPatchData, windowsPatchService, &sysInfo)
		processorService.determineInstalledMissingThirdPartyPatches(&scannedPatchData, &sysInfo)

		if sysInfo.MissingBaseIds == nil && sysInfo.InstalledBaseIds == nil && sysInfo.InstalledThirdPartyIds == nil && sysInfo.MissingThirdPartyIds == nil {
			return
		}

		processorService.getAgentPatchListFromSysInfo(assetId, &sysInfo, patchAsset.DisplayVersion)
	}

}

func (processorService AssetPatchProcessorService) determineInstalledMissingPatches(scannedPatchData *rest.ScannedPatchData, windowsPatchService *WindowsPatchService, sysInfo *common.SysInfo) {
	var installedUuids []string
	var installedWPatchIds []int64
	var missingWPatchIds []int64
	if len(scannedPatchData.InstalledList) > 0 {
		var installedPatchUuids []string
		for _, installedPatch := range scannedPatchData.InstalledList {
			if installedPatch.UUID != "" {
				installedPatchUuids = append(installedPatchUuids, strings.ToUpper(installedPatch.UUID))
			}
		}

		if len(installedPatchUuids) > 0 {
			filter := rest.SearchFilter{
				Qualification: []rest.Qualification{rest.BuildQualification("uuid", "in", installedPatchUuids, "")},
			}

			windowsPatchRests, err := windowsPatchService.GetAllWindowsPatches(filter)
			if err == nil && windowsPatchRests != nil && len(windowsPatchRests) > 0 {
				for _, patch := range windowsPatchRests {
					installedUuids = append(installedUuids, patch.UUID)
					installedWPatchIds = append(installedWPatchIds, patch.Id)
				}
			}
		}

		(*sysInfo).InstalledBaseIds = installedWPatchIds
	}

	if len(scannedPatchData.MissingList) > 0 {
		supersededUUIDMap := map[string]bool{}
		if len(installedUuids) > 0 {
			processorService.fetchAllSupersedeUuids(installedUuids, &supersededUUIDMap)
		}

		missingPatchUuidMap := map[string]bool{}
		for _, missingPatch := range scannedPatchData.MissingList {
			if missingPatch.UUID != "" {
				missingPatchUuidMap[strings.ToUpper(missingPatch.UUID)] = true
			}
		}

		if len(supersededUUIDMap) > 0 {
			for _, supersededUuid := range common.GetKeyList(supersededUUIDMap) {
				delete(missingPatchUuidMap, strings.ToUpper(supersededUuid))
			}
		}

		missingPatchUuids := common.GetKeyList(missingPatchUuidMap)

		if len(missingPatchUuids) > 0 {
			wPatchFilter := rest.SearchFilter{
				Qualification: []rest.Qualification{
					rest.BuildQualification("publishState", "not_equals", "Expired", constant.AND.String()),
					rest.BuildQualification("uuid", "in", missingPatchUuids, constant.AND.String()),
				},
			}

			missingPatchMap := map[string]rest.WindowsPatchRest{}
			tempMissingPatchMap := map[string]bool{}
			allWindowsPatch, _ := windowsPatchService.GetAllWindowsPatches(wPatchFilter)
			if allWindowsPatch != nil && len(allWindowsPatch) > 0 {
				logger.ServiceLogger.Info("Missing kb count :", len(allWindowsPatch))
				for _, wPatch := range allWindowsPatch {
					missingPatchMap[strings.ToUpper(wPatch.UUID)] = wPatch
					tempMissingPatchMap[strings.ToUpper(wPatch.UUID)] = true
				}
			}

			idsToRemove := map[string]bool{}
			for _, patchRest := range missingPatchMap {
				processorService.RemoveSupersededFromMap(patchRest, missingPatchMap, &idsToRemove)
			}
			logger.ServiceLogger.Info("superseded patch uuid count :", common.GetKeyList(idsToRemove))
			logger.ServiceLogger.Info("missing patch uuid count :", common.GetKeyList(tempMissingPatchMap))

			for uuidStr := range idsToRemove {
				delete(missingPatchMap, uuidStr)
				delete(tempMissingPatchMap, uuidStr)
			}

			logger.ServiceLogger.Info("final missing patch uuid count :", tempMissingPatchMap)

			for _, patchRest := range missingPatchMap {
				missingWPatchIds = append(missingWPatchIds, patchRest.Id)
			}
			(*sysInfo).MissingBaseIds = missingWPatchIds
		}
	}
}

func (processorService AssetPatchProcessorService) determineInstalledMissingThirdPartyPatches(scannedPatchData *rest.ScannedPatchData, sysInfo *common.SysInfo) {
	var installedUuids []string
	var installedPatchIds []int64
	var missingPatchIds []int64
	if len(scannedPatchData.InstalledList) > 0 {
		var installedPatchUuids []string
		for _, installedPatch := range scannedPatchData.InstalledList {
			if installedPatch.UUID != "" {
				installedPatchUuids = append(installedPatchUuids, strings.ToLower(installedPatch.UUID))
			}
		}

		if len(installedPatchUuids) > 0 {
			filter := rest.SearchFilter{
				Qualification: []rest.Qualification{rest.BuildQualification("uuid", "in", installedPatchUuids, "")},
			}

			thirdPartyPackages, err := NewThirdPartyPackageService().GetAllThirdPartyPackages(filter)
			if err == nil && len(thirdPartyPackages) > 0 {
				for _, patch := range thirdPartyPackages {
					installedUuids = append(installedUuids, patch.Uuid)
					installedPatchIds = append(installedPatchIds, patch.Id)
				}
			}
		}
		(*sysInfo).InstalledThirdPartyIds = installedPatchIds
	}

	if len(scannedPatchData.MissingList) > 0 {

		missingPatchUuidMap := map[string]bool{}
		for _, missingPatch := range scannedPatchData.MissingList {
			if missingPatch.UUID != "" {
				missingPatchUuidMap[strings.ToLower(missingPatch.UUID)] = true
			}
		}

		for _, installedUuid := range installedUuids {
			delete(missingPatchUuidMap, strings.ToLower(installedUuid))
		}

		if len(missingPatchUuidMap) > 0 {
			missingUuids := common.GetKeyList(missingPatchUuidMap)
			filter := rest.SearchFilter{
				Qualification: []rest.Qualification{rest.BuildQualification("uuid", "in", missingUuids, "")},
			}

			thirdPartyPackageList, err := NewThirdPartyPackageService().GetAllThirdPartyPackages(filter)
			if err == nil && len(thirdPartyPackageList) > 0 {
				for _, patch := range thirdPartyPackageList {
					missingPatchIds = append(missingPatchIds, patch.Id)
				}
			}
			(*sysInfo).MissingThirdPartyIds = missingPatchIds
		}
	}
}

func (processorService AssetPatchProcessorService) RemoveSupersededFromMap(patchRest rest.WindowsPatchRest, patchMap map[string]rest.WindowsPatchRest, idsToRemove *map[string]bool) {
	supersedesUuid := patchRest.SupersedesString
	if supersedesUuid != "" {
		supersedesUuidList := strings.Split(supersedesUuid, ",")
		for _, sUuid := range supersedesUuidList {
			if _, ok := (*idsToRemove)[strings.ToUpper(sUuid)]; !ok {
				if wPatchRest, ok := patchMap[strings.ToUpper(sUuid)]; ok {
					(*idsToRemove)[strings.ToUpper(sUuid)] = true
					processorService.RemoveSupersededFromMap(wPatchRest, patchMap, idsToRemove)
				} else {
					wPatchRest, err := NewWindowsPatchService().GetWindowsPatchByUuid(strings.ToUpper(sUuid))
					if err == nil {
						(*idsToRemove)[strings.ToUpper(sUuid)] = true
						processorService.RemoveSupersededFromMap(wPatchRest, patchMap, idsToRemove)
					}
				}
			}
		}
	}
}

func (processorService AssetPatchProcessorService) createOrUpdateOsApplication(asset rest.AssetMetaDataRest) {
	osApplicationService := NewPatchOsApplicationService()
	osApplication, err := osApplicationService.GetOsAppByName(asset.Name)
	if err == nil {
		if osApplication.Hidden {
			osApplication.ProductType = model.OS.String()
			osApplication.Hidden = false
			_, err = osApplicationService.UpdatePatchOsApplication(osApplication.Id, osApplication)
			if err != nil {
				logger.ServiceLogger.Error("[createOrUpdateOsApplication]", err)
			}
		}
	} else {
		osAppRest := rest.PatchOsApplicationRest{}
		osAppRest.ProductType = model.OS.String()
		osAppRest.Platform = common.Windows.String()
		osAppRest.Hidden = true
		osAppRest.Name = asset.Name
		_, err = osApplicationService.CreatePatchOsApplication(osAppRest)
		if err != nil {
			logger.ServiceLogger.Error("[createOrUpdateOsApplication]", err)
		}
	}
}

func (processorService AssetPatchProcessorService) processPatchDataForWindowsPatch(assetId int64, scannedPatchData *rest.ScannedPatchData) {
	patchAsset, _ := NewAssetService().GetAsset(assetId)
	patchAssetAppService := NewPatchAssetApplicationService()
	if scannedPatchData.PatchData != nil {
		patchData := scannedPatchData.PatchData
		var installedKbList []string

		if val, ok := patchData["software_list"]; ok {
			var installSoftwareList []rest.AssetInstallPkgMetadataRest
			err := json.Unmarshal([]byte(val.(string)), &installSoftwareList)
			if err == nil {
				patchAsset.InstalledSoftwareList = installSoftwareList
			}
		}

		if val, ok := patchData["displayVersion"]; ok {
			patchAsset.DisplayVersion = strings.TrimSpace(val.(string))
			if len(patchAsset.DisplayVersion) > 100 {
				patchAsset.DisplayVersion = ""
			}
		}

		if val, ok := patchData["sysInfo"]; ok {
			if patchAsset.SysInfo != val.(string) {
				patchAsset.SysInfo = val.(string)
			}
			allKbIds := extractAllKbs(val.(string))
			installedKbList = append(installedKbList, allKbIds...)
		}

		if val, ok := patchData["reg_patches"]; ok {
			allKbIds := extractAllKbs(fmt.Sprint(val))
			installedKbList = append(installedKbList, allKbIds...)
		}

		//if val, ok := patchData["reg_patches_cbs"]; ok {
		//	allKbIds := extractAllKbs(val.(string))
		//	installedKbList = append(installedKbList, allKbIds...)
		//}

		edge64, isEdge64 := patchData["edge_info_64"]
		edge32, isEdge32 := patchData["edge_info_32"]
		isEdgeDetected := false
		if isEdge32 || isEdge64 {
			edgeData := ""
			if edge32 != nil {
				edgeData += fmt.Sprint(edge32)
			}
			if edge64 != nil {
				edgeData += fmt.Sprint(edge64)
			}

			if strings.Contains(edgeData, constant.WindowsEdgeStableKey) {
				isEdgeDetected = true
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeStableApp, true)
			} else {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeStableApp, false)
			}

			if strings.Contains(edgeData, constant.WindowsEdgeBetaKey) {
				isEdgeDetected = true
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeBetaApp, true)
			} else {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeBetaApp, false)
			}

			if strings.Contains(edgeData, constant.WindowsEdgeDevKey) {
				isEdgeDetected = true
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeDevApp, true)
			} else {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeDevApp, false)
			}

			if strings.Contains(edgeData, constant.WindowsEdgeCanaryKey) {
				isEdgeDetected = true
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeCanaryApp, true)
			} else {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeCanaryApp, false)
			}

			if isEdgeDetected {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeApp, true)
			} else {
				patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsEdgeApp, false)
			}
		}

		if val, ok := patchData["mpcomputerstatus_info"]; ok {
			propList := strings.Split(val.(string), "\n")
			for _, prop := range propList {
				if strings.Contains(prop, ":") {
					fieldName := strings.Split(prop, ":")[0]
					fieldValue := strings.Split(prop, ":")[1]
					switch strings.ToLower(strings.TrimSpace(fieldName)) {
					case "amproductversion":
						patchAsset.AntiMalwareVersion = strings.TrimSpace(fieldValue)
					}
				}
			}
		}
		defenderInfo, isDefenderInfo := patchData["defender_info"]
		defenderPolicyInfo, isDefenderPolicyInfo := patchData["defender_policy_info"]
		if isDefenderInfo || isDefenderPolicyInfo {
			shouldCreateApp := false
			if isDefenderPolicyInfo {
				shouldCreateApp = isDefenderUpdateDisableByPolicy(defenderPolicyInfo)
			}

			if !shouldCreateApp && isDefenderInfo {
				shouldCreateApp = isDefenderUpdateDisableByUser(defenderInfo)
			}

			patchAssetAppService.CreateOrDeleteApplicationAndAssetRelation(assetId, constant.WindowsDefenderApp, shouldCreateApp)

			if isDefenderInfo {
				spywareVersion := getAntiSpywareVersion(defenderInfo)
				patchAsset.SpywareVersion = spywareVersion
			}

		}

		if val, ok := patchData["mrtversion_info"]; ok {
			propList := strings.Split(val.(string), "\n")
			for _, prop := range propList {
				if strings.Contains(prop, "=") {
					fieldName := strings.Split(prop, "=")[0]
					fieldValue := strings.Split(prop, "=")[1]
					switch strings.ToLower(strings.TrimSpace(fieldName)) {
					case "version":
						patchAsset.MrtVersion = strings.TrimSpace(fieldValue)
					}
				}
			}
		}

		if !reflect.DeepEqual(installedKbList, patchAsset.InstalledKbList) && len(installedKbList) > 0 {
			newItems := common.SubtractStringList(patchAsset.InstalledKbList, installedKbList)
			logger.ServiceLogger.Debug("Found new kb ids = ", newItems)

			patchAsset.InstalledKbList = installedKbList
			var installedPatchDataRestList []rest.PatchDataRest
			for _, kbId := range installedKbList {
				installedPatchDataRestList = append(installedPatchDataRestList, rest.PatchDataRest{KbId: kbId})
			}
			scannedPatchData.InstalledList = installedPatchDataRestList
		}
		patchAsset.LastPatchScanTime = time.Now().UnixMilli()
		_, err := NewAssetService().UpdateAsset(assetId, patchAsset)
		if err != nil {
			logger.ServiceLogger.Error("[processPatchDataForWindowsPatch]", err)
		}
	}
}

func (processorService AssetPatchProcessorService) fetchAllSupersedeUuids(uuids []string, supersededUuidMap *map[string]bool) {
	uuidMap := map[string]bool{}
	newUuidFound := false

	for _, _uuid := range uuids {
		capUUID := strings.ToUpper(_uuid)
		(*supersededUuidMap)[capUUID] = true
		wPatch, err := NewWindowsPatchService().GetWindowsPatchByUuid(capUUID)
		if err == nil && wPatch.Id != 0 && wPatch.SupersedesString != "" {
			supersededUuids := strings.Split(wPatch.SupersedesString, ",")
			for _, supersededUuid := range supersededUuids {
				if _, exists := uuidMap[strings.ToUpper(supersededUuid)]; !exists {
					uuidMap[strings.ToUpper(supersededUuid)] = true
					newUuidFound = true
				}
			}
		}
	}

	if newUuidFound {
		processorService.fetchAllSupersedeUuids(common.GetKeyList(uuidMap), supersededUuidMap)
	}
}

func (processorService AssetPatchProcessorService) getAgentPatchListFromSysInfo(assetId int64, sysInfo *common.SysInfo, displayVersion string) {
	assetPatchRelationService := NewAssetPatchRelationService()

	oldAgentPatchRelations, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
		rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
		rest.BuildQualification("is_old", "equals", "false", constant.AND.String()),
	}})
	if err == nil && len(oldAgentPatchRelations) > 0 {
		for _, oldAgentPatchRelation := range oldAgentPatchRelations {
			oldAgentPatchRelation.IsOld = true
			_, err = assetPatchRelationService.UpdateAgentPatchRelation(oldAgentPatchRelation.Id, oldAgentPatchRelation)
			if err != nil {
				logger.ServiceLogger.Error("[getAgentPatchListFromSysInfo]", err)
			}
		}
	}

	var assetPatchList []rest.AssetPatchRelationRest

	//TODO handle patch category
	supportedCategoryFilter := rest.SearchFilter{Qualification: []rest.Qualification{
		rest.BuildQualification("hidden", "equals", "false", constant.AND.String()),
	}}

	osAppList, err := NewPatchOsApplicationService().GetAll(supportedCategoryFilter)
	if err != nil || len(osAppList) == 0 {
		return
	}

	assetAppPatchFilter := rest.SearchFilter{Qualification: []rest.Qualification{
		rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
	}}

	assetAppList, err := NewPatchAssetApplicationService().GetAllPatchAssetApps(assetAppPatchFilter)
	if err != nil {
		return
	}

	var productIdList []int64
	if len(assetAppList) > 0 {
		for _, assetApp := range assetAppList {
			productIdList = append(productIdList, assetApp.ProductId)
		}
	}

	var installedIds []string
	for _, installedBaseIds := range sysInfo.InstalledBaseIds {
		installedIds = append(installedIds, fmt.Sprint(installedBaseIds))
	}

	patchMap := make(map[string][]rest.PatchRest)
	if len(installedIds) > 0 {
		installedPatches, err := NewWindowsPatchService().GetAllWindowsPatches(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", installedIds, constant.AND.String()),
		}})

		if err == nil && len(installedPatches) > 0 {
			for _, installedPatchRest := range installedPatches {
				wPatch, err := NewPatchService().CreatePatchFromWindowsPatch(installedPatchRest, model.Installed)
				if err == nil {
					if patches, exists := patchMap[wPatch.KbId]; exists {
						patchMap[wPatch.KbId] = append(patches, wPatch)
					} else {
						patchMap[wPatch.KbId] = []rest.PatchRest{wPatch}
					}
					relationRest := rest.AssetPatchRelationRest{
						PatchId:    wPatch.Id,
						AssetId:    assetId,
						PatchState: model.Installed.String(),
						IsOld:      false,
						IsDeclined: false,
					}
					assetPatchList = append(assetPatchList, relationRest)
				}
			}
		}
	}

	var missingIds []string
	for _, missingBaseIds := range sysInfo.MissingBaseIds {
		missingIds = append(missingIds, fmt.Sprint(missingBaseIds))
	}
	if len(missingIds) > 0 {
		missingPatches, err := NewWindowsPatchService().GetAllWindowsPatches(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", missingIds, constant.AND.String()),
		}})

		if err == nil && len(missingPatches) > 0 {
			// Filter out upgrades from missingPatches
			var upgradePatches []rest.WindowsPatchRest
			var filteredMissingPatches []rest.WindowsPatchRest

			for _, patch := range missingPatches {
				if strings.EqualFold(patch.Classification, model.UPGRADES.String()) {
					upgradePatches = append(upgradePatches, patch)
				} else {
					filteredMissingPatches = append(filteredMissingPatches, patch)
				}
			}

			if len(upgradePatches) > 0 {
				// Keep only the latest upgrade patch
				latestUpgradePatch := upgradePatches[0]
				for _, patch := range upgradePatches {
					if patch.ReleaseDate > latestUpgradePatch.ReleaseDate {
						latestUpgradePatch = patch
					}
				}
				filteredMissingPatches = append(filteredMissingPatches, latestUpgradePatch)

			}

			// Use filteredMissingPatches instead of missingPatches for further processing
			missingPatches = filteredMissingPatches
			if len(missingPatches) > 0 {
				for _, missingPatchRest := range missingPatches {
					wPatch, err := NewPatchService().CreatePatchFromWindowsPatch(missingPatchRest, model.Missing)
					if err == nil {
						if patches, exists := patchMap[wPatch.KbId]; exists {
							patchMap[wPatch.KbId] = append(patches, wPatch)
						} else {
							patchMap[wPatch.KbId] = []rest.PatchRest{wPatch}
						}
						relationRest := rest.AssetPatchRelationRest{
							PatchId:    wPatch.Id,
							AssetId:    assetId,
							PatchState: model.Missing.String(),
							IsOld:      false,
							IsDeclined: false,
						}
						assetPatchList = append(assetPatchList, relationRest)
					}
				}
			}
		}
	}

	logger.ServiceLogger.Info("Display version :", displayVersion, ", AssetId: ", assetId)
	// Apply the ignoreDuplicatePatch function to filter out duplicate patches based on display version
	assetPatchList = processorService.ignoreDuplicatePatch(patchMap, displayVersion, assetPatchList)

	var missingThirdPartyUUIDs []string
	for _, missingBaseIds := range sysInfo.MissingThirdPartyIds {
		missingThirdPartyUUIDs = append(missingThirdPartyUUIDs, fmt.Sprint(missingBaseIds))
	}
	if len(missingThirdPartyUUIDs) > 0 {
		missingThirdPartyPatches, err := NewThirdPartyPackageService().GetAllThirdPartyPackages(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", missingThirdPartyUUIDs, constant.AND.String()),
		}})

		if err == nil && len(missingThirdPartyPatches) > 0 {
			for _, missingPatchRest := range missingThirdPartyPatches {
				wPatch, err := NewPatchService().CreatePatchFromThirdPartyPatch(missingPatchRest, model.Missing, common.Windows.String())
				if err == nil {
					relationRest := rest.AssetPatchRelationRest{
						PatchId:    wPatch.Id,
						AssetId:    assetId,
						PatchState: model.Missing.String(),
						IsOld:      false,
						IsDeclined: false,
					}
					assetPatchList = append(assetPatchList, relationRest)
				}
			}
		}
	}

	var installedThirdPartyIds []string
	for _, installedBaseIds := range sysInfo.InstalledThirdPartyIds {
		installedThirdPartyIds = append(installedThirdPartyIds, fmt.Sprint(installedBaseIds))
	}

	if len(installedThirdPartyIds) > 0 {
		installedThirdPartyPatches, err := NewThirdPartyPackageService().GetAllThirdPartyPackages(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", installedThirdPartyIds, constant.AND.String()),
		}})

		if err == nil && len(installedThirdPartyPatches) > 0 {
			for _, installedPatchRest := range installedThirdPartyPatches {
				wPatch, err := NewPatchService().CreatePatchFromThirdPartyPatch(installedPatchRest, model.Installed, common.Windows.String())
				if err == nil {
					relationRest := rest.AssetPatchRelationRest{
						PatchId:    wPatch.Id,
						AssetId:    assetId,
						PatchState: model.Installed.String(),
						IsOld:      false,
						IsDeclined: false,
					}
					assetPatchList = append(assetPatchList, relationRest)
				}
			}
		}
	}

	if len(assetPatchList) > 0 {
		// TODO handel decline patch
		var patchIdList []int64
		for _, assetPatch := range assetPatchList {
			patchIdList = append(patchIdList, assetPatch.PatchId)
		}
		agentPatchRelation, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("patch_id", "in", patchIdList, constant.AND.String()),
			rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
		}})

		for _, assetPatch := range assetPatchList {
			relationRest := rest.AssetPatchRelationRest{}
			if err == nil && len(agentPatchRelation) > 0 {
				for _, agentPatch := range agentPatchRelation {
					if agentPatch.PatchId == assetPatch.PatchId && assetPatch.AssetId == assetPatch.AssetId {
						relationRest = agentPatch
					}
				}
			}

			if relationRest.Id > 0 {
				//TODO handle manually ignored
				patchMap := map[string]interface{}{}
				relationRest.IsOld = false
				patchMap["isOld"] = false
				relationRest.PatchState = assetPatch.PatchState
				patchMap["patchState"] = assetPatch.PatchState
				if relationRest.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
					patchMap["isDeclined"] = false
				}
				relationRest.PatchMap = patchMap
				_, err = assetPatchRelationService.UpdateAgentPatchRelation(relationRest.Id, relationRest)
				if err != nil {
					logger.ServiceLogger.Error("[getAgentPatchListFromSysInfo]", err)
				}
			} else {
				if assetPatch.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
				}
				_, err = assetPatchRelationService.CreateAgentPatchRelation(assetPatch)
				if err != nil {
					logger.ServiceLogger.Error("[getAgentPatchListFromSysInfo]", err)
				}
			}
		}
	}

}

func getAntiSpywareVersion(classDefenderInfo interface{}) string {
	var signatureVersion string
	var dataMap map[string]interface{}

	// Check if classDefenderInfo is a string and not nil
	if classDefenderInfoStr, ok := classDefenderInfo.(string); ok && classDefenderInfo != nil {
		// Remove quotes if present
		data := strings.Trim(classDefenderInfoStr, "\"")
		// Unmarshal JSON string to map
		err := json.Unmarshal([]byte(data), &dataMap)
		if err != nil {
			logger.PatchPoolingLogger.Error("Error in parsing Registry data for defender: %s\n", err)
		}
	} else if classDefenderInfoStr, ok := classDefenderInfo.(map[string]interface{}); ok && classDefenderInfo != nil {
		dataMap = classDefenderInfoStr
	}

	if len(dataMap) > 0 {
		if signatureUpdates, ok := dataMap["Signature Updates"].(map[string]interface{}); ok {
			if signatureVersionValue, ok := signatureUpdates["assignatureversion"].(string); ok {
				signatureVersion = signatureVersionValue
			}
		}
	}

	return signatureVersion
}

func isDefenderUpdateDisableByUser(classDefenderInfo interface{}) bool {
	var isSuccess bool
	var disableAntiSpyware, disableAntiVirus bool

	// Check if classDefenderInfo is a string and not nil
	if dataMap, ok := classDefenderInfo.(map[string]interface{}); ok && classDefenderInfo != nil {

		if len(dataMap) > 0 {
			if winDef, ok := dataMap["Windows Defender"].(map[string]interface{}); ok {
				if disableAntiSpywareValue, ok := winDef["disableantispyware"].(string); ok {
					bit, err := strconv.Atoi(disableAntiSpywareValue)
					if err == nil && bit >= 1 {
						disableAntiSpyware = true
					}
				}
				if disableAntiVirusValue, ok := winDef["disableantivirus"].(string); ok {
					bit, err := strconv.Atoi(disableAntiVirusValue)
					if err == nil && bit >= 1 {
						disableAntiVirus = true
					}
				}
			}
		}
	}

	if !(disableAntiVirus || disableAntiSpyware) {
		isSuccess = true
	}

	return isSuccess
}

func isDefenderUpdateDisableByPolicy(classDefenderPolicyInfo interface{}) bool {
	var isSuccess bool
	var disableAntiSpyware, disableAntiVirus bool

	if dataMap, ok := classDefenderPolicyInfo.(map[string]interface{}); ok && classDefenderPolicyInfo != nil {

		if len(dataMap) > 0 {
			if winDef, ok := dataMap["Windows Defender"].(map[string]interface{}); ok {
				if disableAntiSpywareValue, ok := winDef["disableantispyware"].(string); ok {
					bit, err := strconv.Atoi(disableAntiSpywareValue)
					if err == nil && bit >= 1 {
						disableAntiSpyware = true
					}
				}
				if disableAntiVirusValue, ok := winDef["disableantivirus"].(string); ok {
					bit, err := strconv.Atoi(disableAntiVirusValue)
					if err == nil && bit >= 1 {
						disableAntiVirus = true
					}
				}
			}
		}
	}

	if !(disableAntiVirus || disableAntiSpyware) {
		isSuccess = true
	}

	return isSuccess
}

func extractAllKbs(sysInfo string) []string {
	var kbIds []string

	if sysInfo != "" {
		matches := onlyHotfixPattern.FindAllString(sysInfo, -1)
		if matches != nil {
			for _, match := range matches {
				kb := strings.TrimSpace(strings.Replace(match, "KB", "", -1))
				kbIds = append(kbIds, kb)
			}
		}
	}

	return kbIds
}

func (processorService AssetPatchProcessorService) ProcessLinuxInstalledPatches(assetId int64, scannedPatchData rest.ScannedPatchData) []map[string]interface{} {
	//TODO : Pending Create Agent OS Application

	linuxOsAppIdMap := map[int64]bool{}
	var linuxInstalledPackageList []map[string]interface{}
	if scannedPatchData.PatchData != nil && len(scannedPatchData.PatchData) > 0 {
		asset, _ := NewAgentService().GetAssetMetaDataById(assetId)
		linuxOsApplicationService := NewLinuxOsApplicationService()
		if val, ok := scannedPatchData.PatchData["ubuntu_installed_list"]; ok {
			if installedPatchData, ok := val.(string); ok {
				installedPatchData = strings.ReplaceAll(installedPatchData, "\r", "")
				installedPatchData = strings.ReplaceAll(installedPatchData, "\t", "")
				installedPatchList := strings.Split(installedPatchData, "\n")
				for _, installedPatch := range installedPatchList {
					pkgData := strings.Split(installedPatch, ",")
					if len(pkgData) > 2 {
						pkgBaseInfo := pkgData[0]
						pkgOtherInfo := ""
						if strings.Contains(installedPatch, "[installed]") {
							pkgOtherInfo = pkgData[len(pkgData)-1]
						} else {
							pkgOtherInfo = pkgData[len(pkgData)-2]
						}
						if pkgBaseInfo != "" {
							pkgInfo := strings.Split(pkgBaseInfo, "/")
							if len(pkgInfo) >= 2 {
								pkgName := strings.TrimSpace(pkgInfo[0])
								distro := ""
								pkgVersion := ""
								arch := ""
								distro = strings.TrimSpace(pkgInfo[1])
								if strings.Contains(distro, "-") {
									otherInfo := strings.Split(distro, "-")
									if len(otherInfo) >= 2 {
										distro = strings.TrimSpace(otherInfo[0])
									}
								}
								if strings.HasPrefix(pkgOtherInfo, "now") {
									otherInfo := strings.Split(pkgOtherInfo, " ")
									if len(pkgInfo) >= 2 {
										pkgVersion = strings.TrimSpace(otherInfo[1])
										arch = strings.TrimSpace(otherInfo[2])
									}
									tmpDistro := common.GetLinuxDistributionNameByOsName(asset.Name)
									if tmpDistro != "" {
										distro = tmpDistro
									}
								}

								linuxPkgInfo := map[string]interface{}{
									"name":      pkgName,
									"version":   pkgVersion,
									"osVersion": distro,
									"arch":      arch,
								}

								linuxInstalledPackageList = append(linuxInstalledPackageList, linuxPkgInfo)

								if pkgName != "" && pkgVersion != "" {
									linuxOsAppRest := rest.LinuxOsApplicationRest{}
									linuxOsAppRest.Name = pkgName
									linuxOsAppRest.Version = pkgVersion
									linuxOsAppRest.Arch = arch
									linuxOsAppRest.Distribution = distro
									linuxOsAppRest.NameWithVersion = pkgName + constant.PkgNameVersionSeparator + pkgVersion
									linuxOsAppRest.NameWithDistro = pkgName + constant.PkgNameDistroSeparator + distro
									linuxOsAppRest.CreatedTime = time.Now().UnixMilli()
									linuxOsAppRest.Platform = common.Linux.String()
									linuxOsAppRest.ProductType = model.APPLICATION.String()
									linuxOsAppId := linuxOsApplicationService.CreateOrUpdate(linuxOsAppRest)
									linuxOsAppIdMap[linuxOsAppId] = true
								}
							}
						}
					}
				}
			}
		}
	}

	for linuxOsAppId := range linuxOsAppIdMap {
		patchAssetApp, _ := NewPatchAssetApplicationService().GetByAssetAndProductId(assetId, linuxOsAppId)
		if patchAssetApp.Id == 0 {
			_, err := NewPatchAssetApplicationService().CreatePatchAssetApplication(rest.PatchAssetApplicationRest{
				BaseEntityRest: rest.BaseEntityRest{Name: fmt.Sprint(linuxOsAppId)},
				AssetId:        assetId,
				ProductId:      linuxOsAppId,
				Platform:       common.Linux.String(),
			})
			if err != nil {
				logger.ServiceLogger.Error("[ProcessLinuxInstalledPatches]", err)
			}
		}
	}

	return linuxInstalledPackageList
}

func (processorService AssetPatchProcessorService) processDefenderInfo(scannedPatchData *rest.ScannedPatchData, windowsPatchService *WindowsPatchService, pchAsset rest.AssetRest) {
	if pchAsset.Id != 0 {
		antiSpyware := pchAsset.SpywareVersion
		if antiSpyware != "" {
			patchTitle := "Security Intelligence Update for Microsoft Defender Antivirus"
			patchAlternateTitle := "Security Intelligence Update for Windows Defender Antivirus"
			spywarePatchFilter := rest.SearchFilter{
				Qualification: []rest.Qualification{
					rest.BuildQualification("title", "contains", patchTitle, constant.OR.String()),
					rest.BuildQualification("title", "contains", patchAlternateTitle, constant.OR.String()),
				},
				SortBy: "release_date",
			}
			defenderPatchList, err := windowsPatchService.GetAllWindowsPatches(spywarePatchFilter)
			if err == nil && len(defenderPatchList) > 0 {
				for _, defenderPatch := range defenderPatchList {
					patchTitle = "Security Intelligence Update for Microsoft Defender Antivirus"
					patchAlternateTitle = "Security Intelligence Update for Windows Defender Antivirus"
					if strings.Contains(defenderPatch.Title, patchTitle) || strings.Contains(defenderPatch.Title, patchAlternateTitle) {
						if checkVersionCompatibility(antiSpyware, defenderPatch.Title) {
							(*scannedPatchData).MissingList = append((*scannedPatchData).MissingList, rest.PatchDataRest{
								UUID: defenderPatch.UUID,
								KbId: defenderPatch.KbId,
							})
							break
						}
					}
				}

				spywarePatchList, err := NewPatchService().GetAllPatches(spywarePatchFilter)

				if err == nil && len(spywarePatchList) > 0 {
					for _, spywarePatch := range spywarePatchList {
						if strings.Contains(spywarePatch.Title, antiSpyware) {
							oldAgentPatchRelations, _ := NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
								rest.BuildQualification("asset_id", "equals", pchAsset.AssetId, constant.AND.String()),
								rest.BuildQualification("patch_id", "equals", spywarePatch.Id, constant.AND.String()),
							}})
							if len(oldAgentPatchRelations) == 1 {
								oldAgentPatch := oldAgentPatchRelations[0]
								if oldAgentPatch.PatchState == model.Installed.String() {
									(*scannedPatchData).InstalledList = append((*scannedPatchData).InstalledList, rest.PatchDataRest{
										UUID: spywarePatch.UUID,
										KbId: spywarePatch.KbId,
									})
								}
							} else {
								(*scannedPatchData).InstalledList = append((*scannedPatchData).InstalledList, rest.PatchDataRest{
									UUID: spywarePatch.UUID,
									KbId: spywarePatch.KbId,
								})
							}
							break
						}
					}
				}
			}
		}

		antiMalware := pchAsset.AntiMalwareVersion
		if antiMalware != "" {
			patchTitle := "Update for Microsoft Defender Antivirus antimalware platform"
			patchAlternateTitle := "Update for Windows Defender Antivirus antimalware platform"
			malwarePatchFilter := rest.SearchFilter{
				Qualification: []rest.Qualification{
					rest.BuildQualification("title", "contains", patchTitle, constant.OR.String()),
					rest.BuildQualification("title", "contains", patchAlternateTitle, constant.OR.String()),
				},
				SortBy: "release_date",
			}
			defenderPatchList, err := windowsPatchService.GetAllWindowsPatches(malwarePatchFilter)
			if err == nil && len(defenderPatchList) > 0 {
				for _, defenderPatch := range defenderPatchList {
					if strings.Contains(defenderPatch.Title, patchTitle) || strings.Contains(defenderPatch.Title, patchAlternateTitle) {
						if checkVersionCompatibility(antiMalware, defenderPatch.Title) {
							(*scannedPatchData).MissingList = append((*scannedPatchData).MissingList, rest.PatchDataRest{
								UUID: defenderPatch.UUID,
								KbId: defenderPatch.KbId,
							})
							break
						}
					}
				}

				malwarePatchList, err := NewPatchService().GetAllPatches(malwarePatchFilter)

				if err == nil && len(malwarePatchList) > 0 {
					for _, malware := range malwarePatchList {
						if strings.Contains(malware.Title, antiMalware) {
							oldAgentPatchRelations, _ := NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
								rest.BuildQualification("asset_id", "equals", pchAsset.AssetId, constant.AND.String()),
								rest.BuildQualification("patch_id", "equals", malware.Id, constant.AND.String()),
							}})
							if len(oldAgentPatchRelations) == 1 {
								oldAgentPatch := oldAgentPatchRelations[0]
								if oldAgentPatch.PatchState == model.Installed.String() {
									(*scannedPatchData).InstalledList = append((*scannedPatchData).InstalledList, rest.PatchDataRest{
										UUID: malware.UUID,
										KbId: malware.KbId,
									})
								}
							} else {
								(*scannedPatchData).InstalledList = append((*scannedPatchData).InstalledList, rest.PatchDataRest{
									UUID: malware.UUID,
									KbId: malware.KbId,
								})
							}
							break
						}
					}
				}
			}
		}
	}
}

func (processorService AssetPatchProcessorService) ProcessLinuxPatches(assetId int64, scannedPatchData rest.ScannedPatchData) {
	asset, _ := NewAgentService().GetAssetMetaDataById(assetId)
	ubuntuPatchService := NewUbuntuPatchService()
	missingList := scannedPatchData.LinuxMissingPatches
	missingMap := map[int64]bool{}
	var archList []string
	for _, missing := range missingList {
		missingMap[missing] = true
	}

	var installedList []int64
	var patchAssetAppIds []int64
	patchAssetApplicationList, _ := NewPatchAssetApplicationService().GetAllPatchAssetApps(rest.SearchFilter{Qualification: []rest.Qualification{
		rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
	}})
	if len(patchAssetApplicationList) > 0 {
		for _, applicationRest := range patchAssetApplicationList {
			if applicationRest.Platform == common.Linux {
				patchAssetAppIds = append(patchAssetAppIds, applicationRest.ProductId)
			}
		}

		linuxOsAppList, _ := NewLinuxOsApplicationService().GetAllLinuxOsApplications(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", patchAssetAppIds, constant.AND.String()),
		}})
		if len(linuxOsAppList) > 0 {
			var nameWithVersionList []string
			for _, linuxOsApp := range linuxOsAppList {
				nameWithVersionList = append(nameWithVersionList, linuxOsApp.NameWithVersion)
			}

			distro := common.GetLinuxDistributionNameByOsName(asset.PlatformVersion)
			if !strings.Contains(strings.ToLower(asset.Arch), "all") {
				if strings.Contains(strings.ToLower(asset.Arch), "64") {
					archList = append(archList, "amd64")
					archList = append(archList, "x64")
					archList = append(archList, "x86_64")
				} else {
					archList = append(archList, "i386")
					archList = append(archList, "x86")
					archList = append(archList, "i686")
					archList = append(archList, "i586")
				}
				archList = append(archList, asset.Arch)
				archList = append(archList, "all")
				archList = append(archList, "no_arch")
			}

			var qualifications []rest.Qualification
			qualifications = append(qualifications, rest.BuildQualification("pkg_and_version", "in", nameWithVersionList, constant.AND.String()))
			qualifications = append(qualifications, rest.BuildQualification("os_version", "equals", distro, constant.AND.String()))
			if len(archList) > 0 {
				qualifications = append(qualifications, rest.BuildQualification("arch", "in", archList, constant.AND.String()))
			}
			pkgFilter := rest.SearchFilter{
				Qualification: qualifications,
			}
			ubuntuPkgList, _ := ubuntuPatchService.GetAllUbuntuPatches(pkgFilter)
			if len(ubuntuPkgList) > 0 {
				for _, ubuntuPatch := range ubuntuPkgList {
					installedList = append(installedList, ubuntuPatch.Id)
				}
			}
		}
	}

	for _, installedId := range installedList {
		delete(missingMap, installedId)
	}

	var assetPatchList []rest.AssetPatchRelationRest
	if len(missingMap) > 0 {
		for missing := range missingMap {
			isCreate := false
			ubuntuPkg, _ := ubuntuPatchService.GetPatch(missing, false)
			if ubuntuPkg.Id > 0 &&
				!strings.Contains(strings.ToLower(ubuntuPkg.Version), "~bpo") &&
				strings.Contains(strings.ToLower(strings.Join(common.GetPatchArchitectureByAgentArch(asset.Arch), ", ")), strings.ToLower(ubuntuPkg.Arch)) {
				pch, _ := NewPatchService().GetPatchByUuid(ubuntuPkg.UUID)
				if pch.Id > 0 {
					if pch.OsArch == "" || len(pch.DownloadFileDetails) == 0 || !ubuntuPkg.Downloadable {
						_, err := NewPatchService().DeletePatch(pch.Id)
						if err != nil {
							logger.ServiceLogger.Error("[ProcessLinuxPatches]", err)
						}
						isCreate = true
					} else {
						assetPatchList = append(assetPatchList, rest.AssetPatchRelationRest{
							PatchId:    pch.Id,
							AssetId:    assetId,
							PatchState: model.Missing.String(),
							IsOld:      false,
							IsDeclined: false,
						})
					}
				} else {
					isCreate = true
				}

				if isCreate && ubuntuPkg.Downloadable {
					pchRest := rest.PatchRest{}
					pchRest.OsPlatform = common.Ubuntu.String()
					pchRest.Title = ubuntuPkg.PackageName
					pchRest.RebootBehaviour = model.MAY_BE.String()
					pchRest.Source = model.Scanning.String()
					pchRest.ReleaseDate = time.Unix(ubuntuPkg.ReleaseDate, 0).UnixMilli()
					lang, _ := NewLanguageService().GetLanguageByCode(0)
					if lang.Id > 0 {
						pchRest.LanguageSupported = []int64{lang.Id}
					}
					releasePkgFilter := rest.SearchFilter{
						Qualification: []rest.Qualification{
							rest.BuildQualification("os_version", "equals", ubuntuPkg.OsVersion, "and"),
							rest.BuildQualification("name_and_version", "equals", ubuntuPkg.PkgAndVersion, "and"),
						},
						SortBy: "-created_time",
					}
					// Use secure parameterized queries to prevent SQL injection
					releasePkgQueryResult := rest.PrepareSecureQueryFromSearchFilter(releasePkgFilter, common.UBUNTU_RELEASE_PACKAGES.String(), false, "")
					releasePkgList, _ := NewUbuntuReleasePackageService().Repository.GetAllUbuntuReleasePackagesByQuery(releasePkgQueryResult.Query, releasePkgQueryResult.Parameters)
					if len(releasePkgList) > 0 {
						notice, _ := NewUbuntuNoticeDataService().Repository.GetByNoticeId(releasePkgList[0].NoticeID)
						if notice.Id > 0 {
							pchRest.Title = notice.Title
							pchRest.BulletinId = notice.NoticeId
							pchRest.SupportUrl = notice.SupportURL
							if len(notice.CVEsIDs) > 0 {
								pchRest.CVENumber = strings.Join(notice.CVEsIDs, ", ")
							}
							if notice.Published != "" {
								layout := "2006-01-02T15:04:05.999999"
								t, err := time.Parse(layout, notice.Published)
								if err == nil {
									pchRest.ReleaseDate = t.UnixMilli()
								}
							}
						}
					}
					pchRest.PatchSeverity = getSeverityByPriority(ubuntuPkg.Priority).String()
					pchRest.UUID = ubuntuPkg.UUID
					pchRest.OsArch = strings.Join(common.GetPatchArchitectureByAgentArch(ubuntuPkg.Arch), ", ")
					pchRest.DownloadSize = ubuntuPkg.Size
					pchRest.DownloadFileDetails = []model.PatchFileData{
						{
							FileName:       ubuntuPkg.FileName,
							RefName:        "",
							Url:            ubuntuPkg.DownloadUrl + "?checksum=" + ubuntuPkg.Sha1,
							DownloadUrl:    ubuntuPkg.DownloadUrl + "?checksum=" + ubuntuPkg.Sha1,
							Size:           ubuntuPkg.Size,
							ReleaseDate:    0,
							Language:       0,
							ChecksumSHA256: ubuntuPkg.Sha1,
						},
					}
					dependFileDetails := ubuntuPatchService.GetAllDependentFileDetails(ubuntuPkg, archList)
					if dependFileDetails != nil && len(dependFileDetails) > 0 {
						pchRest.DownloadFileDetails = append(pchRest.DownloadFileDetails, dependFileDetails...)
					}
					pchRest.PatchUpdateCategory = model.SECURITY_UPDATES.String()
					pchId, err := NewPatchService().Create(pchRest, model.Missing)
					if err.Message == "" && pchId > 0 {
						assetPatchList = append(assetPatchList, rest.AssetPatchRelationRest{
							PatchId:    pchId,
							AssetId:    assetId,
							PatchState: model.Missing.String(),
							IsOld:      false,
							IsDeclined: false,
						})
					}
				}
			}
		}
	}

	installedMap := map[int64]bool{}
	for _, installed := range installedList {
		installedMap[installed] = true
	}

	if len(installedMap) > 0 {
		for installed := range installedMap {
			isCreate := false
			ubuntuPkg, _ := ubuntuPatchService.GetPatch(installed, false)
			if ubuntuPkg.Id > 0 &&
				!strings.Contains(strings.ToLower(ubuntuPkg.Version), "~bpo") &&
				strings.Contains(strings.ToLower(strings.Join(common.GetPatchArchitectureByAgentArch(asset.Arch), ", ")), strings.ToLower(ubuntuPkg.Arch)) {
				pch, _ := NewPatchService().GetPatchByUuid(ubuntuPkg.UUID)
				if pch.Id > 0 {
					if pch.OsArch == "" || len(pch.DownloadFileDetails) == 0 || !ubuntuPkg.Downloadable {
						_, err := NewPatchService().DeletePatch(pch.Id)
						if err != nil {
							logger.ServiceLogger.Error("[ProcessLinuxPatches]", err)
						}
						isCreate = true
					} else {
						assetPatchList = append(assetPatchList, rest.AssetPatchRelationRest{
							PatchId:    pch.Id,
							AssetId:    assetId,
							PatchState: model.Installed.String(),
							IsOld:      false,
							IsDeclined: false,
						})
					}
				} else {
					isCreate = true
				}

				if isCreate && ubuntuPkg.Downloadable {
					pchRest := rest.PatchRest{}
					pchRest.OsPlatform = common.Ubuntu.String()
					pchRest.Title = ubuntuPkg.PackageName
					pchRest.RebootBehaviour = model.MAY_BE.String()
					pchRest.Source = model.Scanning.String()
					pchRest.ReleaseDate = time.Unix(ubuntuPkg.ReleaseDate, 0).UnixMilli()
					lang, _ := NewLanguageService().GetLanguageByCode(0)
					if lang.Id > 0 {
						pchRest.LanguageSupported = []int64{lang.Id}
					}
					// Use secure parameterized queries to prevent SQL injection
					releasePkgQueryResult := rest.PrepareSecureQueryFromSearchFilter(rest.SearchFilter{
						Qualification: []rest.Qualification{
							rest.BuildQualification("name_and_version", "equals", ubuntuPkg.PkgAndVersion, "and"),
						}}, common.UBUNTU_RELEASE_PACKAGES.String(), false, "")
					releasePkgList, _ := NewUbuntuReleasePackageService().Repository.GetAllUbuntuReleasePackagesByQuery(releasePkgQueryResult.Query, releasePkgQueryResult.Parameters)
					if len(releasePkgList) > 0 {
						notice, _ := NewUbuntuNoticeDataService().Repository.GetByNoticeId(releasePkgList[0].NoticeID)
						if notice.Id > 0 {
							pchRest.Title = notice.Title
							pchRest.BulletinId = notice.NoticeId
							pchRest.SupportUrl = notice.SupportURL
							if len(notice.CVEsIDs) > 0 {
								pchRest.CVENumber = strings.Join(notice.CVEsIDs, ", ")
							}
							if notice.Published != "" {
								layout := "2006-01-02T15:04:05.999999"
								t, err := time.Parse(layout, notice.Published)
								if err == nil {
									pchRest.ReleaseDate = t.UnixMilli()
								}
							}
						}
					}
					pchRest.PatchSeverity = getSeverityByPriority(ubuntuPkg.Priority).String()
					pchRest.UUID = ubuntuPkg.UUID
					pchRest.OsArch = strings.Join(common.GetPatchArchitectureByAgentArch(ubuntuPkg.Arch), ", ")
					pchRest.DownloadSize = ubuntuPkg.Size
					pchRest.DownloadFileDetails = []model.PatchFileData{
						{
							FileName:       ubuntuPkg.FileName,
							RefName:        "",
							Url:            ubuntuPkg.DownloadUrl + "?checksum=" + ubuntuPkg.Sha1,
							DownloadUrl:    ubuntuPkg.DownloadUrl + "?checksum=" + ubuntuPkg.Sha1,
							Size:           ubuntuPkg.Size,
							ReleaseDate:    0,
							Language:       0,
							ChecksumSHA256: ubuntuPkg.Sha1,
						},
					}
					dependFileDetails := ubuntuPatchService.GetAllDependentFileDetails(ubuntuPkg, archList)
					if dependFileDetails != nil && len(dependFileDetails) > 0 {
						pchRest.DownloadFileDetails = append(pchRest.DownloadFileDetails, dependFileDetails...)
					}
					pchRest.PatchUpdateCategory = model.SECURITY_UPDATES.String()
					pchId, err := NewPatchService().Create(pchRest, model.Installed)
					if err.Message == "" && pchId > 0 {
						assetPatchList = append(assetPatchList, rest.AssetPatchRelationRest{
							PatchId:    pchId,
							AssetId:    assetId,
							PatchState: model.Installed.String(),
							IsOld:      false,
							IsDeclined: false,
						})
					}
				}
			}
		}
	}

	assetPatchRelationService := NewAssetPatchRelationService()
	oldAgentPatchRelations, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
		rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
	}})
	if err == nil && len(oldAgentPatchRelations) > 0 {
		for _, oldAgentPatchRelation := range oldAgentPatchRelations {
			oldAgentPatchRelation.IsOld = true
			_, err = assetPatchRelationService.UpdateAgentPatchRelation(oldAgentPatchRelation.Id, oldAgentPatchRelation)
			if err != nil {
				logger.ServiceLogger.Error("[ProcessLinuxPatches]", err)
			} //TODO need to hadle patch delete if patch is not in missing or installed or ignored
		}
	}

	if len(assetPatchList) > 0 {
		// TODO handel decline patch
		var patchIdList []int64
		for _, assetPatch := range assetPatchList {
			patchIdList = append(patchIdList, assetPatch.PatchId)
		}
		agentPatchRelation, err := NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("patch_id", "in", patchIdList, constant.AND.String()),
			rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
		}})

		for _, assetPatch := range assetPatchList {
			relationRest := rest.AssetPatchRelationRest{}
			if err == nil && len(agentPatchRelation) > 0 {
				for _, agentPatch := range agentPatchRelation {
					if agentPatch.PatchId == assetPatch.PatchId && assetPatch.AssetId == assetPatch.AssetId {
						relationRest = agentPatch
					}
				}
			}

			if relationRest.Id > 0 {
				patchMap := map[string]interface{}{}
				relationRest.IsOld = false
				patchMap["isOld"] = false
				relationRest.PatchState = assetPatch.PatchState
				patchMap["patchState"] = assetPatch.PatchState
				if relationRest.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
					patchMap["isDeclined"] = false
				}
				relationRest.PatchMap = patchMap
				_, err = NewAssetPatchRelationService().UpdateAgentPatchRelation(relationRest.Id, relationRest)
				if err != nil {
					logger.ServiceLogger.Error("[ProcessLinuxPatches]", err)
				}
			} else {
				if assetPatch.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
				}
				_, err = NewAssetPatchRelationService().CreateAgentPatchRelation(assetPatch)
				if err != nil {
					logger.ServiceLogger.Error("[ProcessLinuxPatches]", err)
				}
			}
		}
	}
}

func getSeverityByPriority(priority string) model.PatchSeverity {
	priority = strings.ToUpper(priority)
	if priority == "" {
		return model.PS_UNSPECIFIED
	}

	normalized := strings.ToLower(priority)

	switch normalized {
	case "optional":
		return model.PS_LOW
	case "standard", "moderate":
		return model.PS_MODERATE
	case "important", "required":
		return model.PS_IMPORTANT
	case "extra", "critical":
		return model.PS_CRITICAL
	default:
		return model.PS_UNSPECIFIED
	}
}

func (processorService AssetPatchProcessorService) PrepareLatestPackageInfoByInstalledList(asset rest.AssetMetaDataRest) string {
	patchInfoFileName := ""

	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while Prepare Latest Package Info By InstalledList for asset :", asset.Id)
		}
	}()

	linuxPackageInfo := map[string]interface{}{}
	if asset.Id != 0 {
		var patchAssetAppIds []int64
		patchAssetApplicationList, _ := NewPatchAssetApplicationService().GetAllPatchAssetApps(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("asset_id", "equals", asset.Id, constant.AND.String()),
		}})
		if len(patchAssetApplicationList) > 0 {
			for _, applicationRest := range patchAssetApplicationList {
				if applicationRest.Platform == common.Linux {
					patchAssetAppIds = append(patchAssetAppIds, applicationRest.ProductId)
				}
			}
			linuxOsAppList, _ := NewLinuxOsApplicationService().GetAllLinuxOsApplications(rest.SearchFilter{Qualification: []rest.Qualification{
				rest.BuildQualification("id", "in", patchAssetAppIds, constant.AND.String()),
			}})
			if len(linuxOsAppList) > 0 {
				distro := common.GetLinuxDistributionNameByOsName(asset.PlatformVersion)
				var archList []string
				if !strings.Contains(strings.ToLower(asset.Arch), "all") {
					if strings.Contains(strings.ToLower(asset.Arch), "64") {
						archList = append(archList, "amd64")
						archList = append(archList, "x64")
						archList = append(archList, "x86_64")
					} else {
						archList = append(archList, "i386")
						archList = append(archList, "x86")
						archList = append(archList, "i686")
						archList = append(archList, "i586")
					}
					archList = append(archList, asset.Arch)
					archList = append(archList, "all")
					archList = append(archList, "no_arch")
				}
				var pkgNameList []string
				for _, linuxOsApp := range linuxOsAppList {
					pkgNameList = append(pkgNameList, fmt.Sprintf("'%s'", linuxOsApp.Name))
				}
				if len(pkgNameList) > 0 {
					startTime := time.Now()
					chunkList := common.ChunkList(pkgNameList, 500)
					for _, chunk := range chunkList {
						var qualifications []rest.Qualification
						qualifications = append(qualifications, rest.BuildQualification("package_name", "contains_with_in", chunk, constant.AND.String()))
						qualifications = append(qualifications, rest.BuildQualification("os_version", "equals", distro, constant.AND.String()))
						qualifications = append(qualifications, rest.BuildQualification("downloadable", "equals", true, constant.AND.String()))
						if len(archList) > 0 {
							qualifications = append(qualifications, rest.BuildQualification("arch", "in", archList, constant.AND.String()))
						}
						pkgFilter := rest.SearchFilter{
							Qualification: qualifications,
						}
						searchQuery := rest.PrepareSecureQueryFromSearchFilter(pkgFilter, common.UBUNTU_PATCH.String(), false, "")
						ubuntuPkgList, _ := NewUbuntuPatchService().Repository.GetAllUbuntuPatchesByQuery(searchQuery.Query, searchQuery.Parameters)
						if len(ubuntuPkgList) > 0 {
							for _, ubuntuPatch := range ubuntuPkgList {
								linuxPackageInfo[fmt.Sprint(ubuntuPatch.Id)] = map[string]interface{}{
									"name":      ubuntuPatch.PackageName,
									"version":   ubuntuPatch.Version,
									"osVersion": ubuntuPatch.OsVersion,
									"arch":      ubuntuPatch.Arch,
								}
							}
						}
					}
					elapsedTime := time.Since(startTime)
					logger.ServiceLogger.Debug("package search time:", elapsedTime, " for asset ", asset.Id)
				}
			}
		}
	}

	if len(linuxPackageInfo) > 0 {
		patchInfoFileName = uuid.NewString() + ".json"
		file, err := os.Create(filepath.Join(common.PatchDbPath(), patchInfoFileName))
		if err != nil {
			return ""
		}
		defer func(file *os.File) {
			err = file.Close()
			if err != nil {
				logger.ServiceLogger.Error("[PrepareLatestPackageInfoByInstalledList]", err)
			}
		}(file)

		jsonData, err := json.MarshalIndent(linuxPackageInfo, "", "  ")
		if err != nil {
			return ""
		}

		_, err = file.Write(jsonData)
		if err != nil {
			return ""
		}

	}
	return patchInfoFileName
}

func checkVersionCompatibility(antiSpyware, title string) bool {
	compitable := false
	logger.PatchPoolingLogger.Debug("Spyware defenderVersion: %s", antiSpyware)
	logger.PatchPoolingLogger.Debug("Patch Title: %s", title)

	defenderVersion := ""
	matches := defenderVersionPattern.FindStringSubmatch(title)
	if len(matches) > 1 {
		defenderVersion = matches[1]
		if strings.Contains(defenderVersion, "Version") {
			defenderVersion = strings.ReplaceAll(defenderVersion, "Version", "")
			defenderVersion = strings.TrimSpace(defenderVersion)
		}
	}
	logger.PatchPoolingLogger.Debug("Patch Spyware defenderVersion: %s", defenderVersion)

	// Use hashicorp/go-defenderVersion to handle defenderVersion comparison
	antiSpywareVersion, err := version.NewVersion(antiSpyware)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error while parsing spyware defenderVersion: %s", err)
		return false
	}

	patchVersion, err := version.NewVersion(defenderVersion)
	if err != nil {
		logger.PatchPoolingLogger.Error("Error while parsing patch defenderVersion: %s", err)
		return false
	}

	if patchVersion.Compare(antiSpywareVersion) > 0 {
		compitable = true
	}

	return compitable
}

func (processorService AssetPatchProcessorService) ProcessMacPatches(assetId int64, scannedPatchData rest.ScannedPatchData) {
	asset, _ := NewAgentService().GetAssetMetaDataById(assetId)
	var assetPatchList []rest.AssetPatchRelationRest
	macPatchService := NewMacPatchService()
	thirdPartyPatchService := NewThirdPartyPackageService()
	patchServices := NewPatchService()
	missingList := scannedPatchData.MacMissingPatches
	if len(missingList) > 0 {
		macPatchList, _ := macPatchService.GetAllMacPatches(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("product_key", "in", missingList, constant.AND.String()),
		}})
		if len(macPatchList) > 0 {
			highestVersion := getHighestOsVersion(macPatchList)
			if highestVersion != "" {
				macPatchList = filterByHighestVersion(macPatchList, highestVersion)
			}
			for _, macPatch := range macPatchList {
				if macPatch.Name != "" {
					// create missing patch
					mPatch, err := patchServices.CreatePatchFromMacPatch(macPatch, asset.OsVersion, model.Missing, true)
					if err == nil && mPatch.Id != 0 {
						relationRest := rest.AssetPatchRelationRest{
							PatchId:    mPatch.Id,
							AssetId:    assetId,
							PatchState: model.Missing.String(),
							IsOld:      false,
							IsDeclined: false,
						}
						assetPatchList = append(assetPatchList, relationRest)
					}
				}
			}
		}

		macThirdPartyPatchList, _ := thirdPartyPatchService.GetAllThirdPartyPackages(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("uuid", "in", missingList, constant.AND.String()),
		}})
		if len(macThirdPartyPatchList) > 0 {
			for _, macThirdPartyPatch := range macThirdPartyPatchList {
				if macThirdPartyPatch.Name != "" {
					// create missing patch
					mPatch, err := patchServices.CreatePatchFromThirdPartyPatch(macThirdPartyPatch, model.Missing, common.MacOS.String())
					if err == nil && mPatch.Id != 0 {
						relationRest := rest.AssetPatchRelationRest{
							PatchId:    mPatch.Id,
							AssetId:    assetId,
							PatchState: model.Missing.String(),
							IsOld:      false,
							IsDeclined: false,
						}
						assetPatchList = append(assetPatchList, relationRest)
					}
				}
			}
		}
	}

	if scannedPatchData.PatchData != nil {
		patchData := scannedPatchData.PatchData
		if osVersion, ok := patchData["mac_os_version"].(string); ok {
			if osVersion != "" {
				if !strings.HasPrefix(osVersion, "10") {
					osVersion = strings.Split(osVersion, ".")[0]
				}
				logger.ServiceLogger.Debug("retrieving mac_installed_list...")
				if installedPkgList, ok := patchData["mac_installed_list"].(string); ok {
					var installSoftwareList map[string]interface{}
					err := json.Unmarshal([]byte(strings.TrimSpace(installedPkgList)), &installSoftwareList)
					if err == nil {
						logger.ServiceLogger.Debug("mac_installed_list parsed...")
						if installSoftwareList != nil {
							if installedPkgData, ok := installSoftwareList["SPInstallHistoryDataType"].([]interface{}); ok {
								if installedPkgData != nil {
									for _, packageData := range installedPkgData {
										if packageMap, ok := packageData.(map[string]interface{}); ok {
											if packageMap["install_version"] != nil {
												macPatchList, _ := macPatchService.GetAllMacPatches(rest.SearchFilter{Qualification: []rest.Qualification{
													rest.BuildQualification("name", "equals", packageMap["_name"].(string), constant.AND.String()),
													rest.BuildQualification("version", "equals", packageMap["install_version"].(string), constant.AND.String()),
												}})
												if len(macPatchList) > 0 {
													for _, macPatch := range macPatchList {
														if macPatch.Name != "" {
															// create missing patch
															mPatch, err := patchServices.CreatePatchFromMacPatch(macPatch, asset.OsVersion, model.Installed, true)
															if err == nil && mPatch.Id != 0 {
																relationRest := rest.AssetPatchRelationRest{
																	PatchId:    mPatch.Id,
																	AssetId:    assetId,
																	PatchState: model.Installed.String(),
																	IsOld:      false,
																	IsDeclined: false,
																}
																assetPatchList = append(assetPatchList, relationRest)
															}
														}
													}
												}
											} else {
												matcher := versionPattern.FindStringSubmatch(packageMap["_name"].(string))
												if len(matcher) > 0 {
													title := strings.TrimSpace(strings.Split(packageMap["_name"].(string), matcher[0])[0])
													macPatchList, _ := macPatchService.GetAllMacPatches(rest.SearchFilter{Qualification: []rest.Qualification{
														rest.BuildQualification("name", "contains", title, constant.AND.String()),
														rest.BuildQualification("version", "contains", matcher[0], constant.AND.String()),
														rest.BuildQualification("os_version", "contains", osVersion, constant.AND.String()),
													}})
													if len(macPatchList) > 0 {
														for _, macPatch := range macPatchList {
															if macPatch.Name != "" {
																// create missing patch
																mPatch, err := patchServices.CreatePatchFromMacPatch(macPatch, asset.OsVersion, model.Installed, true)
																if err == nil && mPatch.Id != 0 {
																	relationRest := rest.AssetPatchRelationRest{
																		PatchId:    mPatch.Id,
																		AssetId:    assetId,
																		PatchState: model.Installed.String(),
																		IsOld:      false,
																		IsDeclined: false,
																	}
																	assetPatchList = append(assetPatchList, relationRest)
																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		if installedThirdPartyList, ok := patchData["mac_third_party_installed_list"].([]interface{}); ok {
			logger.ServiceLogger.Debug("retrieving mac_installed_list...")
			if len(installedThirdPartyList) > 0 {
				macThirdPartyPatchList, _ := thirdPartyPatchService.GetAllThirdPartyPackages(rest.SearchFilter{Qualification: []rest.Qualification{
					rest.BuildQualification("uuid", "in", installedThirdPartyList, constant.AND.String()),
				}})
				if len(macThirdPartyPatchList) > 0 {
					for _, macThirdPartyPatch := range macThirdPartyPatchList {
						if macThirdPartyPatch.Name != "" {
							// create installed patch
							mPatch, err := patchServices.CreatePatchFromThirdPartyPatch(macThirdPartyPatch, model.Installed, common.MacOS.String())
							if err == nil && mPatch.Id != 0 {
								relationRest := rest.AssetPatchRelationRest{
									PatchId:    mPatch.Id,
									AssetId:    assetId,
									PatchState: model.Installed.String(),
									IsOld:      false,
									IsDeclined: false,
								}
								assetPatchList = append(assetPatchList, relationRest)
							}
						}
					}
				}
			}
		}
	}

	if len(assetPatchList) > 0 {
		assetPatchRelationService := NewAssetPatchRelationService()
		// TODO handel decline patch
		var patchIdList []int64
		for _, assetPatch := range assetPatchList {
			patchIdList = append(patchIdList, assetPatch.PatchId)
		}
		agentPatchRelation, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
			rest.BuildQualification("patch_id", "in", patchIdList, constant.AND.String()),
			rest.BuildQualification("asset_id", "equals", assetId, constant.AND.String()),
		}})

		for _, assetPatch := range assetPatchList {
			relationRest := rest.AssetPatchRelationRest{}
			if err == nil && len(agentPatchRelation) > 0 {
				for _, agentPatch := range agentPatchRelation {
					if agentPatch.PatchId == assetPatch.PatchId && assetPatch.AssetId == assetPatch.AssetId {
						relationRest = agentPatch
					}
				}
			}

			if relationRest.Id > 0 {
				//TODO handle manually ignored
				patchMap := map[string]interface{}{}
				relationRest.IsOld = false
				relationRest.PatchState = assetPatch.PatchState
				patchMap["patchState"] = assetPatch.PatchState
				if relationRest.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
					patchMap["isDeclined"] = false
				}
				relationRest.PatchMap = patchMap
				_, err = assetPatchRelationService.UpdateAgentPatchRelation(relationRest.Id, relationRest)
				if err != nil {
					logger.ServiceLogger.Error("[ProcessMacPatches]", err)
				}
			} else {
				if assetPatch.PatchState == model.Installed.String() {
					assetPatch.IsDeclined = false
				}
				_, err := assetPatchRelationService.CreateAgentPatchRelation(assetPatch)
				if err != nil {
					logger.ServiceLogger.Error("[ProcessMacPatches]", err)
				}
			}
		}
	}
}

func filterByHighestVersion(macPatchList []rest.MacPatchRest, finalVersion string) []rest.MacPatchRest {
	var filteredList []rest.MacPatchRest
	for _, patchRest := range macPatchList {
		if patchRest.ProductType == "macOsVersionUpdate" {
			if patchRest.Version == finalVersion || !strings.EqualFold(patchRest.ProductType, "macOsVersionUpdate") {
				filteredList = append(filteredList, patchRest)
			}
		} else {
			filteredList = append(filteredList, patchRest)
		}
	}
	macPatchList = filteredList
	return macPatchList
}

// ignore Duplicate Patch filters out duplicate patches based on display version
// It keeps patches that contain the display version and removes others when duplicates are found
// It also keeps DEFINITION_UPDATES category patches regardless of version
func (processorService AssetPatchProcessorService) ignoreDuplicatePatch(patchMap map[string][]rest.PatchRest,
	displayVersion string, assetPatchList []rest.AssetPatchRelationRest) []rest.AssetPatchRelationRest {
	if len(strings.TrimSpace(displayVersion)) > 0 {
		logger.ServiceLogger.Info("ignoreDuplicatePatch Display version :", displayVersion)
		// Set to collect patch IDs that should be ignored
		patchIdsToIgnore := make(map[int64]bool)

		if len(patchMap) > 0 {
			// Iterate through each KB and its associated patches
			for _, patches := range patchMap {
				// Only process if there are multiple patches for the same KB
				if len(patches) > 1 {
					// Collect patches to ignore: those that don't contain display version and aren't definition updates
					patchesToIgnore := lo.Filter(patches, func(p rest.PatchRest, _ int) bool {
						return !strings.Contains(p.Title, displayVersion) &&
							!strings.EqualFold(p.PatchUpdateCategory, model.DEFINITION_UPDATES.String())
					})

					// Only add to ignore list if we're not ignoring all patches for this KB
					if len(patchesToIgnore) > 0 && len(patchesToIgnore) != len(patches) {
						for _, p := range patchesToIgnore {
							patchIdsToIgnore[p.Id] = true
						}
					}
				}
			}
		}

		// If we have patches to ignore, filter them out from the asset patch list
		if len(patchIdsToIgnore) > 0 {
			assetPatchList = lo.Filter(assetPatchList, func(ap rest.AssetPatchRelationRest, _ int) bool {
				return !patchIdsToIgnore[ap.PatchId]
			})
		}
	}

	return assetPatchList
}

func getHighestOsVersion(macPatchList []rest.MacPatchRest) string {
	osVersion := ""
	for _, macPatch := range macPatchList {
		if osVersion == "" && strings.EqualFold(macPatch.ProductType, "macOsVersionUpdate") {
			osVersion = macPatch.Version
		} else if osVersion != "" && strings.EqualFold(macPatch.ProductType, "macOsVersionUpdate") &&
			osVersion <= macPatch.Version {
			osVersion = macPatch.Version
		}
	}

	return osVersion
}
