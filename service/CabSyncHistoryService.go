package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type CabSyncHistoryService struct {
	Repository *repository.CabSyncHistoryRepository
}

func NewCabSyncHistoryService() *CabSyncHistoryService {
	return &CabSyncHistoryService{Repository: repository.NewCabSyncHistoryRepository()}
}

func (service CabSyncHistoryService) convertToModel(cabSyncHistoryRest rest.CabSyncHistoryRest) (*model.CabSyncHistory, error) {
	return &model.CabSyncHistory{
		Id:           cabSyncHistoryRest.Id,
		LastSyncTime: cabSyncHistoryRest.LastSyncTime,
		ReleaseDate:  cabSyncHistoryRest.ReleaseDate,
	}, nil
}

func (service CabSyncHistoryService) convertToRest(cabSyncHistory model.CabSyncHistory) rest.CabSyncHistoryRest {
	return rest.CabSyncHistoryRest{
		Id:           cabSyncHistory.Id,
		LastSyncTime: cabSyncHistory.LastSyncTime,
		ReleaseDate:  cabSyncHistory.ReleaseDate,
	}
}

func (service CabSyncHistoryService) BeforeCreate(cabSyncHistoryRest rest.CabSyncHistoryRest) common.CustomError {
	history, err := service.Repository.GetByReleaseDate(cabSyncHistoryRest.ReleaseDate)
	if err == nil && history.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service CabSyncHistoryService) Create(cabSyncHistoryRest rest.CabSyncHistoryRest) (int64, common.CustomError) {
	logger.ServiceLogger.Debug("Process started to create cab sync history : ", cabSyncHistoryRest.ReleaseDate)
	history, err := service.convertToModel(cabSyncHistoryRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating cab sync history: %s : %s", cabSyncHistoryRest.ReleaseDate, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(history)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating cab sync history: %s : %s", cabSyncHistoryRest.ReleaseDate, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Debug("Cab sync history ", cabSyncHistoryRest.ReleaseDate, " created successfully.")
	return id, common.CustomError{}
}

func (service CabSyncHistoryService) Update(id int64, restModel rest.CabSyncHistoryRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update cab sync history with id - %v", id))
	history, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating cab sync history for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	history.LastSyncTime = restModel.LastSyncTime
	history.ReleaseDate = restModel.ReleaseDate
	_, err = service.Repository.Update(&history)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating cab sync history for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update cab sync history with id - %v", id))
	return true, common.CustomError{}
}

func (service CabSyncHistoryService) PerformPartialUpdate(restModel rest.CabSyncHistoryRest, cabSyncHistory model.CabSyncHistory) {
	cabSyncHistory.LastSyncTime = restModel.LastSyncTime
	cabSyncHistory.ReleaseDate = restModel.ReleaseDate
}

func (service CabSyncHistoryService) CreateOrUpdate(historyRest rest.CabSyncHistoryRest) {
	history, err := service.Repository.GetByReleaseDate(historyRest.ReleaseDate)
	if err == nil && history.Id != 0 {
		service.Update(history.Id, historyRest)
	} else {
		historyRest.Id = 0
		service.Create(historyRest)
	}
}
