package service

import (
	"deployment/common"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
)

type AuditService struct {
	Repository *repository.AuditRepository
}

func NewAuditService() *AuditService {
	return &AuditService{
		Repository: repository.NewAuditRepository(),
	}
}

func (service AuditService) convertToModel(auditRest rest.AuditRest) (*model.Audit, error) {
	return &model.Audit{
		Id:              auditRest.Id,
		DisplayName:     auditRest.DisplayName,
		CreatedById:     auditRest.CreatedById,
		CreatedTime:     auditRest.CreatedTime,
		RefId:           auditRest.RefId,
		PerformerId:     auditRest.PerformerId,
		AuditString:     auditRest.AuditString,
		AuditEventType:  auditRest.AuditEventType,
		AuditEventModel: auditRest.AuditEventModel,
	}, nil
}

func (service AuditService) convertToRest(audit model.Audit) rest.AuditRest {
	return rest.AuditRest{
		Id:              audit.Id,
		DisplayName:     audit.DisplayName,
		CreatedById:     audit.CreatedById,
		CreatedTime:     audit.CreatedTime,
		RefId:           audit.RefId,
		PerformerId:     audit.PerformerId,
		AuditString:     audit.AuditString,
		AuditEventType:  audit.AuditEventType,
		AuditEventModel: audit.AuditEventModel,
	}
}

func (service AuditService) Create(rest rest.AuditRest) (int64, error) {
	// TODO: Handle Before create
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	audit, err := service.convertToModel(rest)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(audit)
	if err != nil {
		return 0, err
	}
	// TODO: Handle after create validation
	return id, nil
}

func (service AuditService) GetAllByAuditEventModel(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var auditPageList []model.Audit
	var err error
	tableName := "audits"
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		auditPageList, err = service.Repository.GetAllAudits(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.convertListToRest(auditPageList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AuditService) convertListToRest(audits []model.Audit) []rest.AuditRest {
	var auditRestList []rest.AuditRest
	if len(audits) != 0 {
		for _, audit := range audits {
			auditRest := service.convertToRest(audit)
			auditRestList = append(auditRestList, auditRest)
		}
	}
	return auditRestList
}
