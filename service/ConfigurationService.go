package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
)

type ConfigurationService struct {
	Repository *repository.ConfigurationRepository
}

func NewConfigurationService() *ConfigurationService {
	return &ConfigurationService{
		Repository: repository.NewConfigurationRepository(),
	}
}

func (service ConfigurationService) convertToModel(configRest rest.ConfigurationRest) (*model.Configuration, error) {
	var osType common.OsType
	var osArch common.OsArchitecture
	var configType model.ExecutionType
	var err error

	osType = osType.ToOsType(configRest.Os)

	osArch = osArch.ToOsArch(configRest.Arch)

	configType, err = configType.ToExecutionType(configRest.ExecutionType)
	if err != nil {
		return nil, err
	}
	configRest.Name = configRest.DisplayName
	return &model.Configuration{
		BaseEntityModel:      ConvertToBaseEntityModel(configRest.BaseEntityRest),
		DisplayName:          configRest.DisplayName,
		Description:          configRest.Description,
		Os:                   osType,
		Arch:                 osArch,
		ConfigurationType:    configType,
		ConfigurationActions: rest.ToConfigurationList(configRest.ConfigurationActions),
		SelfServiceSupported: configRest.SelfServiceSupported,
		IsRemediation:        configRest.IsRemediation,
		Tags:                 configRest.Tags,
	}, nil
}

func (service ConfigurationService) convertToRest(configInfo model.Configuration) rest.ConfigurationRest {
	return rest.ConfigurationRest{
		BaseEntityRest:       ConvertToBaseEntityRest(configInfo.BaseEntityModel),
		DisplayName:          configInfo.DisplayName,
		Description:          configInfo.Description,
		Os:                   configInfo.Os.String(),
		Arch:                 configInfo.Arch.String(),
		ExecutionType:        configInfo.ConfigurationType.String(),
		ConfigurationActions: rest.ToConfigurationActionRestList(configInfo.ConfigurationActions),
		SelfServiceSupported: configInfo.SelfServiceSupported,
		IsRemediation:        configInfo.IsRemediation,
		Tags:                 configInfo.Tags,
	}
}

func (service ConfigurationService) performPartialUpdate(model *model.Configuration, restModel rest.ConfigurationRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&model.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchMap["description"] != nil && model.Description != restModel.Description {
		common.PrepareInDiffMap("description", model.Description, restModel.Description, &diffMap)
		model.Description = restModel.Description
	}

	if restModel.PatchMap["configurationType"] != nil {
		configType, _ := model.ConfigurationType.ToExecutionType(restModel.ExecutionType)
		if model.ConfigurationType != configType {
			common.PrepareInDiffMap("configuration_type", model.ConfigurationType.String(), restModel.ExecutionType, &diffMap)
			model.ConfigurationType = configType
		}
	}

	if restModel.PatchMap["ConfigurationActions"] != nil {
		common.PrepareInDiffMap("configuration_action", model.ConfigurationActions, restModel.ConfigurationActions, &diffMap)
		model.ConfigurationActions = rest.ToConfigurationList(restModel.ConfigurationActions)
	}

	if restModel.PatchMap["displayName"] != nil && model.DisplayName != restModel.DisplayName {
		common.PrepareInDiffMap("display_name", model.DisplayName, restModel.DisplayName, &diffMap)
		model.DisplayName = restModel.DisplayName
	}

	if restModel.PatchMap["tags"] != nil && !reflect.DeepEqual(model.Tags, restModel.Tags) {
		common.PrepareInDiffMap("tags", model.Tags, restModel.Tags, &diffMap)
		model.Tags = restModel.Tags
	}

	if restModel.PatchMap["selfServiceSupported"] != nil && model.SelfServiceSupported != restModel.SelfServiceSupported {
		common.PrepareInDiffMap("self_service_supported", strconv.FormatBool(model.SelfServiceSupported), strconv.FormatBool(restModel.SelfServiceSupported), &diffMap)
		model.SelfServiceSupported = restModel.SelfServiceSupported
	}

	if restModel.PatchMap["isRemediation"] != nil && model.IsRemediation != restModel.IsRemediation {
		common.PrepareInDiffMap("is_remediation", strconv.FormatBool(model.IsRemediation), strconv.FormatBool(restModel.IsRemediation), &diffMap)
		model.IsRemediation = restModel.IsRemediation
	}

	return diffMap, len(diffMap) != 0
}

func (service ConfigurationService) BulkCreate(configurationRests []rest.ConfigurationRest) {
	if configurationRests != nil && len(configurationRests) >= 0 {
		for _, configurationRest := range configurationRests {
			_, err := service.Create(configurationRest)
			if err != nil {
				logger.ServiceLogger.Error("[ConfigurationService][BulkCreate]", err)
			}
		}
	}
}

func (service ConfigurationService) Create(rest rest.ConfigurationRest) (int64, error) {
	//TODO Handel Before create
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	configuration, err := service.convertToModel(rest)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(configuration)
	if err != nil {
		return 0, err
	}
	service.AfterCreate(configuration)
	return id, nil
}

func (service ConfigurationService) Update(id int64, restModel rest.ConfigurationRest) (bool, common.CustomError) {
	configuration, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	//TODO Before update

	_, isUpdatable := service.performPartialUpdate(&configuration, restModel)
	if isUpdatable {
		configuration.UpdatedTime = common.CurrentMillisecond()
		configuration.UpdatedById = common.GetUserFromCallContext()
		_, err := service.Repository.Update(&configuration)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		return true, common.CustomError{}
	} else {
		return isUpdatable, common.CustomError{}
	}
	//TODO: Handle after update event
}

func (service ConfigurationService) GetConfiguration(id int64, includeArchived bool) (rest.ConfigurationRest, error) {
	var configurationRest rest.ConfigurationRest
	configuration, err := service.Repository.GetById(id, includeArchived)
	if err != nil {
		return configurationRest, err
	}
	return service.convertToRest(configuration), nil
}

func (service ConfigurationService) DeleteConfiguration(id int64, permanentDelete bool) (bool, error) {
	success := false
	var err error
	if permanentDelete {
		success, err = service.Repository.PermanentDeleteById(id)
	} else {
		success, err = service.Repository.DeleteById(id)
	}
	if err != nil {
		return success, err
	}
	return true, nil
}

func (service ConfigurationService) GetAllConfiguration(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var configurationPageList []model.Configuration
	var err error
	tableName := "configurations"
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		configurationPageList, err = service.Repository.GetAllConfigurations(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.convertListToRestConfiguration(configurationPageList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service ConfigurationService) convertListToRestConfiguration(configurations []model.Configuration) []rest.ConfigurationRest {
	var configurationRestList []rest.ConfigurationRest
	if len(configurations) != 0 {
		for _, configuration := range configurations {
			configurationRest := service.convertToRest(configuration)
			configurationRestList = append(configurationRestList, configurationRest)
		}
	}
	return configurationRestList
}

func (service ConfigurationService) AfterCreate(configuration *model.Configuration) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for config id : ", configuration.Id)
		}
	}()

	configuration.Name = fmt.Sprintf("CNF-%03d", configuration.Id)
	_, err := service.Repository.Update(configuration)
	if err != nil {
		logger.ServiceLogger.Error("[ConfigurationService][AfterCreate]", err)
	}
}

func (service ConfigurationService) CreateOOBConfiguration() {
	filter := rest.SearchFilter{
		Offset:          0,
		Size:            1,
		Qualification:   []rest.Qualification{},
		IncludeArchived: true,
	}
	query := rest.PrepareSecureQueryFromSearchFilter(filter, "configurations", true, "")
	count := service.Repository.Count(query.Query, query.Parameters)
	if count == 0 {

		var configurationList []rest.ConfigurationRest

		configurationRest := rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Stop Microsoft Reboot Notification upon Patch Installation",
			Description:    "Turns off the Microsoft reboot notification following patch installation.",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\" /v NoAutoRebootWithLoggedOnUsers /t REG_DWORD /d 1 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Stop Automated Updater for Winzip",
			Description:    "Stop Automated Updater for Winzip",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{
				{
					OrderId:     1,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\" /v NoAutoRebootWithLoggedOnUsers /t REG_DWORD /d 1 /f",
				},
				{
					OrderId:     2,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Nico Mak Computing\\WinZip\\UpdateCheck\" /v AutoMode /t REG_DWORD /d 0 /f",
				},
			},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Prevents upgrading to the latest version of Windows via Windows Update",
			Description:    "Prevents upgrading to the latest version of Windows via Windows Update",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\" /v DisableOSUpgrade /t REG_DWORD /d 1 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Stop Windows Error Reporting",
			Description:    "Disables the Windows Error Reporting",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\Windows Error Reporting\" /v Disabled /t REG_DWORD /d 1 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Stop Automated Windows 10 Peer to Peer Update",
			Description:    "Turns off peer-to-peer patch updates for Windows 10",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\DeliveryOptimization\\Config\" /v DODownloadMode /t REG_DWORD /d 0 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Turns off Windows 10 upgrade notifications",
			Description:    "Turns off Windows 10 upgrade notifications",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Windows\\Gwx\" /v DisableGwx /t REG_DWORD /d 1 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Turns off automatic updates for Office 365",
			Description:    "Disables the automatic updates of Office 365",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{
				{
					OrderId:     1,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Office\\15.0\\Common\\OfficeUpdate\" /v EnableAutomaticUpdates /t REG_DWORD /d 0 /f",
				},
				{
					OrderId:     2,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Office\\16.0\\Common\\OfficeUpdate\" /v EnableAutomaticUpdates /t REG_DWORD /d 0 /f",
				},
				{
					OrderId:     3,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Office\\14.0\\Common\\OfficeUpdate\" /v EnableAutomaticUpdates /t REG_DWORD /d 0 /f",
				},
				{
					OrderId:     4,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Office\\12.0\\Common\\OfficeUpdate\" /v EnableAutomaticUpdates /t REG_DWORD /d 0 /f",
				},
				{
					OrderId:     5,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Microsoft\\Office\\11.0\\Common\\OfficeUpdate\" /v EnableAutomaticUpdates /t REG_DWORD /d 0 /f",
				},
			},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Turns off automatic updates for CCleaner",
			Description:    "Turns off automatic updates for CCleaner",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Piriform\\CCleaner\" /v UpdateCheck /t REG_DWORD /d 0 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationRest = rest.ConfigurationRest{
			BaseEntityRest: rest.BaseEntityRest{},
			DisplayName:    "Turn Off automatic updates for Windows operating system.",
			Description:    "Disables the Windows automatic updates",
			Os:             "windows",
			Arch:           "x64",
			ExecutionType:  "command",
			ConfigurationActions: []rest.ConfigurationActionRest{{
				OrderId:     1,
				CommandType: "reg",
				Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\" /v NoAutoUpdate /t REG_DWORD /d 1 /f",
			}},
			SelfServiceSupported: false,
			IsRemediation:        false,
		}
		configurationList = append(configurationList, configurationRest)

		configurationList = append(configurationList, []rest.ConfigurationRest{
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Stop Automated Java Updates",
				Description:    "This setting will trun off the automatic updates of Java",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\JavaSoft\\Java Update\\Policy\" /v NotifyDownload /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\JavaSoft\\Java Update\\Policy\" /v EnableJavaUpdate /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     3,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\JavaSoft\\Java Update\\Policy\" /v EnableAutoUpdateCheck /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     4,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\JavaSoft\\Java Update\\Policy\" /v EnableJavaUpdate /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     5,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\JavaSoft\\Java Update\\Policy\" /v NotifyDownload /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     6,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\JavaSoft\\Java Update\\Policy\" /v EnableAutoUpdateCheck /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turn Off automatic updates for Windows operating system.",
				Description:    "Disables the Windows automatic updates",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{{
					OrderId:     1,
					CommandType: "reg",
					Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU\" /v NoAutoUpdate /t REG_DWORD /d 1 /f",
				}},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turn Off Automated upgrade of IE 11",
				Description:    "Disables Internet Explorer from upgrading to version 11 through Windows updates",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Internet Explorer\\Setup\\11.0\" /v DoNotAllowIE11 /t REG_DWORD /d 1 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Stop Automatic Delivery of IE 10",
				Description:    "Disables Internet Explorer from upgrading to version 10 through Windows updates",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Internet Explorer\\Setup\\10.0\" /v DoNotAllowIE10 /t REG_DWORD /d 1 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Stop Automated App Updates",
				Description:    "Disables automatic updates of windows apps.",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Policies\\Microsoft\\WindowsStore\\\" /v AutoDownload /t REG_DWORD /d 4 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Stop Auto Update of Google Chrome",
				Description:    "Disables automatic update of Google Chrome",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Google\\Update\" /v DisableAutoUpdateChecksCheckboxValue /t REG_DWORD /d 1 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Google\\Update\" /v Update{8A69D345-D564-463C-AFF1-A69D9E530F96} /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     3,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Google\\Update\" /v AutoUpdateCheckPeriodMinutes /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     4,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Google\\Update\" /v UpdateDefault /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off automatic updates for Adobe Reader Version 9",
				Description:    "Disables the automatic updates of Adobe Reader version 9",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\9.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\Policies\\Adobe\\Acrobat Reader\\9.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns Off Automated Adobe Reader 8 update",
				Description:    "Disables the automatic updates of Adobe Reader 8",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\8.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\Policies\\Adobe\\Acrobat Reader\\8.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Truns off Automated Adobe Reader 11 update",
				Description:    "Disables automatic updates for Adobe Reader 11",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\11.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\Policies\\Adobe\\Acrobat Reader\\11.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off Automated Adobe Reader 10 updater",
				Description:    "Disables the automatic updates of Adobe Reader 10",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\Policies\\Adobe\\Acrobat Reader\\10.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\10.0\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off automated Adobe AIR Updater",
				Description:    "Disables the automatic updates of Adobe AIR that is installed at the system level",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\Software\\Policies\\Adobe\\AIR\" /v UpdateDisabled /t REG_DWORD /d 1 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\10.0\\FeatureLockdown\" /v UpdateDisabled /t REG_DWORD /d 1 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off Automated Adobe Acrobat Reader DC updater",
				Description:    "Disables the automatic updates of Adobe Acrobat Reader DC",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Wow6432Node\\Policies\\Adobe\\Acrobat Reader\\DC\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
					{
						OrderId:     2,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Acrobat Reader\\DC\\FeatureLockdown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off Automated Adobe Acrobat XI Updater",
				Description:    "Disables the automatic updates of Adobe Acrobat XI",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Adobe Acrobat\\11.0\\FeatureLockDown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
			{
				BaseEntityRest: rest.BaseEntityRest{},
				DisplayName:    "Turns off Automated Adobe Acrobat X Updater",
				Description:    "Disables the automatic updates of Adobe Acrobat X",
				Os:             "windows",
				Arch:           "x64",
				ExecutionType:  "command",
				ConfigurationActions: []rest.ConfigurationActionRest{
					{
						OrderId:     1,
						CommandType: "reg",
						Command:     "reg add \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Policies\\Adobe\\Adobe Acrobat\\10.0\\FeatureLockDown\" /v bUpdater /t REG_DWORD /d 0 /f",
					},
				},
				SelfServiceSupported: false,
				IsRemediation:        false,
			},
		}...)

		service.BulkCreate(configurationList)
	}
}

func (service ConfigurationService) GetTagFilterWithCount() interface{} {
	response := map[string]int64{}
	tupleQual := rest.PrepareTupleQueryFromSearchFilter([]string{"tags"}, "configurations")
	pkgList, err := service.Repository.GetAllConfiguration(tupleQual)
	if err == nil {
		for _, pkg := range pkgList {
			if len(pkg.Tags) > 0 {
				for _, tag := range pkg.Tags {
					if val, ok := response[tag]; ok {
						response[tag] = val + 1
					} else {
						response[tag] = 1
					}
				}
			}
		}
	}
	return response
}

func (service ConfigurationService) CreateRemediationActionsAgentTasks(context map[string]interface{}) {
	var actionCtx rest.SystemActionContext
	contextData, _ := json.Marshal(context)
	logger.ServiceLogger.Info("CreateRemediationActionsAgentTasks : ", contextData)
	err := json.Unmarshal(contextData, &actionCtx)
	if err != nil {
		logger.ServiceLogger.Error("[ConfigurationService][CreateRemediationActionsAgentTasks]", err)
	}

	if actionCtx.Actions != nil {
		agentTaskService := NewAgentTaskService()
		for _, actionId := range actionCtx.Actions {
			configuration, err := service.GetConfiguration(common.ConvertToInt64(actionId), false)
			if err != nil {
				continue
			}
			if configuration.Id > 0 && configuration.IsRemediation {
				agentTask := rest.AgentTaskRest{}
				agentTask.AgentId = actionCtx.AssetId
				agentTask.RefId = actionId
				agentTask.RefModel = common.CONFIGURATION.String()
				agentTask.TaskStatus = model.TaskReadyToDeploy.String()
				agentTask.TaskType = model.SYSTEM_ACTION.String()
				if actionCtx.Context != nil {
					agentTask.CustomTaskDetails = map[string]interface{}{"context": actionCtx.Context}
				}
				_, err = agentTaskService.Create(agentTask)
				if err != nil {
					logger.ServiceLogger.Error("[ConfigurationService][CreateRemediationActionsAgentTasks]", err)
				}
			}
		}
	}
}
