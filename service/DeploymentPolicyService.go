package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type DeploymentPolicyService struct {
	Repository *repository.DeploymentPolicyRepository
}

func NewDeploymentPolicyService() *DeploymentPolicyService {
	return &DeploymentPolicyService{
		Repository: repository.NewDeploymentPolicyRepository(),
	}
}

func (service DeploymentPolicyService) convertToModel(restPolicy rest.DeploymentPolicyRest) (*model.DeploymentPolicy, error) {
	var policyType model.DeploymentPolicyType
	var initialDeploymentOn model.DeploymentStartOn
	var restartType model.RestartType
	//var deploymentDays model.DeploymentDays

	if restPolicy.Type != "" {
		t, err := policyType.ToDeploymentPolicyType(restPolicy.Type)
		if err != nil {
			return nil, err
		}
		policyType = t
	}

	if restPolicy.InitiateDeploymentOn != "" {
		on, err := initialDeploymentOn.ToDeploymentStartOn(restPolicy.InitiateDeploymentOn)
		if err != nil {
			return nil, err
		}
		initialDeploymentOn = on
	}

	restartType = restartType.ToRestartType(restPolicy.RestartType)

	restPolicy.Name = restPolicy.DisplayName
	return &model.DeploymentPolicy{
		BaseEntityRefModel:   ConvertToBaseEntityRefModel(restPolicy.BaseEntityRefModelRest),
		DisplayName:          restPolicy.DisplayName,
		Description:          restPolicy.Description,
		Type:                 policyType,
		InitiateDeploymentOn: initialDeploymentOn,
		DeploymentDays:       restPolicy.DeploymentDays,
		DeploymentTimeFrom:   restPolicy.DeploymentTimeFrom,
		DeploymentTimeTo:     restPolicy.DeploymentTimeTo,
		AfterEveryTimeUnit:   restPolicy.AfterEveryTimeUnit,
		AfterEveryTime:       restPolicy.AfterEveryTime,
		RestartType:          restartType,
	}, nil
}

func (service DeploymentPolicyService) convertToRest(modelPolicy model.DeploymentPolicy) rest.DeploymentPolicyRest {
	return rest.DeploymentPolicyRest{
		BaseEntityRefModelRest: ConvertToBaseEntityRefModelRest(modelPolicy.BaseEntityRefModel),
		DisplayName:            modelPolicy.DisplayName,
		Description:            modelPolicy.Description,
		Type:                   modelPolicy.Type.String(),
		InitiateDeploymentOn:   modelPolicy.InitiateDeploymentOn.String(),
		DeploymentDays:         modelPolicy.DeploymentDays,
		DeploymentTimeFrom:     modelPolicy.DeploymentTimeFrom,
		DeploymentTimeTo:       modelPolicy.DeploymentTimeTo,
		AfterEveryTimeUnit:     modelPolicy.AfterEveryTimeUnit,
		AfterEveryTime:         modelPolicy.AfterEveryTime,
		RestartType:            modelPolicy.RestartType.String(),
	}
}

func (service DeploymentPolicyService) performPartialUpdate(domainModel *model.DeploymentPolicy, rest rest.DeploymentPolicyRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBaseRef(&domainModel.BaseEntityRefModel, rest.BaseEntityRefModelRest)

	if rest.PatchMap["description"] != nil && domainModel.Description != rest.Description {
		domainModel.Description = rest.Description
		common.PrepareInDiffMap("description", domainModel.Description, rest.Description, &diffMap)
	}

	if rest.PatchMap["displayName"] != nil && domainModel.DisplayName != rest.DisplayName {
		common.PrepareInDiffMap("display_name", domainModel.DisplayName, rest.DisplayName, &diffMap)
		domainModel.DisplayName = rest.DisplayName
	}

	if rest.PatchMap["type"] != nil && domainModel.Type.String() != rest.Type {
		domainModel.Type, _ = domainModel.Type.ToDeploymentPolicyType(rest.Type)
		common.PrepareInDiffMap("type", domainModel.Type.String(), rest.Type, &diffMap)
	}

	if rest.PatchMap["initiateDeploymentOn"] != nil && domainModel.InitiateDeploymentOn.String() != rest.InitiateDeploymentOn {
		domainModel.InitiateDeploymentOn, _ = domainModel.InitiateDeploymentOn.ToDeploymentStartOn(rest.InitiateDeploymentOn)
		common.PrepareInDiffMap("initial_deployment_on", domainModel.InitiateDeploymentOn.String(), rest.InitiateDeploymentOn, &diffMap)
	}

	if rest.PatchMap["deploymentDays"] != nil {
		domainModel.DeploymentDays = rest.DeploymentDays
		common.PrepareInDiffMap("deployment_days", domainModel.DeploymentDays, rest.DeploymentDays, &diffMap)
	}

	if rest.PatchMap["deploymentTimeFrom"] != nil && domainModel.DeploymentTimeFrom != rest.DeploymentTimeFrom {
		domainModel.DeploymentTimeFrom = rest.DeploymentTimeFrom
		common.PrepareInDiffMap("deployment_time_from", domainModel.DeploymentTimeFrom, rest.DeploymentTimeFrom, &diffMap)
	}

	if rest.PatchMap["deploymentTimeTo"] != nil && domainModel.DeploymentTimeTo != rest.DeploymentTimeTo {
		domainModel.DeploymentTimeTo = rest.DeploymentTimeTo
		common.PrepareInDiffMap("deployment_time_to", domainModel.DeploymentTimeTo, rest.DeploymentTimeTo, &diffMap)
	}

	if rest.PatchMap["afterEveryTimeUnit"] != nil && domainModel.AfterEveryTimeUnit != rest.AfterEveryTimeUnit {
		domainModel.AfterEveryTimeUnit = rest.AfterEveryTimeUnit
		common.PrepareInDiffMap("after_every_time_unit", domainModel.AfterEveryTimeUnit, rest.AfterEveryTimeUnit, &diffMap)
	}

	if rest.PatchMap["afterEveryTime"] != nil && domainModel.AfterEveryTime != rest.AfterEveryTime {
		domainModel.AfterEveryTime = rest.AfterEveryTime
		common.PrepareInDiffMap("after_every_time", domainModel.AfterEveryTime, rest.AfterEveryTime, &diffMap)
	}

	if rest.PatchMap["restartType"] != nil && domainModel.RestartType.String() != rest.RestartType {
		var restartType model.RestartType
		restartType = restartType.ToRestartType(rest.RestartType)
		domainModel.RestartType = restartType
		common.PrepareInDiffMap("restart_type", domainModel.RestartType.String(), rest.RestartType, &diffMap)
	}

	return diffMap, len(diffMap) != 0
}

func (service DeploymentPolicyService) GetDeploymentPolicy(id int64, includeArchive bool) (rest.DeploymentPolicyRest, error) {
	var policyRest rest.DeploymentPolicyRest
	policy, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		return policyRest, err
	}
	return service.convertToRest(policy), nil
}

func (service DeploymentPolicyService) DeleteDeploymentPolicy(id int64) (bool, error) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, err
	}
	//TODO : Handle after delete event
	return true, nil
}

func (service DeploymentPolicyService) Create(rest rest.DeploymentPolicyRest) (int64, error) {
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	policy, err := service.convertToModel(rest)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(policy)
	if err != nil {
		return 0, err
	}
	service.AfterCreate(policy)
	return id, nil
}

func (service DeploymentPolicyService) Update(id int64, restModel rest.DeploymentPolicyRest) (bool, common.CustomError) {
	policy, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	_, isUpdatable := service.performPartialUpdate(&policy, restModel)
	if isUpdatable {
		policy.UpdatedTime = common.CurrentMillisecond()
		policy.UpdatedById = common.GetUserFromCallContext()
		_, err := service.Repository.Update(&policy)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		return true, common.CustomError{}
	} else {
		return isUpdatable, common.CustomError{}
	}
}

func (service DeploymentPolicyService) GetAllDeploymentPolicy(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var policyPageList []model.DeploymentPolicy
	var err error
	tableName := "deployment_policies"
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		policyPageList, err = service.Repository.GetAllPolicy(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.convertListToRest(policyPageList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service DeploymentPolicyService) convertListToRest(policies []model.DeploymentPolicy) []rest.DeploymentPolicyRest {
	var policyRestList []rest.DeploymentPolicyRest
	if len(policies) != 0 {
		for _, policy := range policies {
			policyRest := service.convertToRest(policy)
			policyRestList = append(policyRestList, policyRest)
		}
	}
	return policyRestList
}

func (service DeploymentPolicyService) AfterCreate(policy *model.DeploymentPolicy) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for policy id : ", policy.Id)
		}
	}()

	policy.Name = fmt.Sprintf("POLICY-%v", policy.Id)
	_, err := service.Repository.Update(policy)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentPolicyService][AfterCreate]", err)
	}
}

func (service DeploymentPolicyService) CreateOOBDeploymentPolicy() {
	filter := rest.SearchFilter{
		Offset:          0,
		Size:            1,
		Qualification:   []rest.Qualification{},
		IncludeArchived: true,
	}
	query := rest.PrepareSecureQueryFromSearchFilter(filter, "deployment_policies", true, "")
	count := service.Repository.Count(query.Query, query.Parameters)
	if count == 0 {
		deploymentPolicyRest := rest.DeploymentPolicyRest{}
		deploymentPolicyRest.Name = "OOB Instant deployment policy"
		deploymentPolicyRest.DisplayName = "OOB Instant deployment policy"
		deploymentPolicyRest.Type = "instant"
		deploymentPolicyRest.OOB = true
		deploymentPolicyRest.RefModel = "all"
		policyId, err := service.Create(deploymentPolicyRest)
		if err == nil {
			constant.DefaultInstantDeploymentPolicyId = policyId
		}
	}
}
