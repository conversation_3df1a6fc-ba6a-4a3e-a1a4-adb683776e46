package service

import (
	"deployment/cache"
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"deployment/rest/view"
	"encoding/base64"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"
)

type AgentTaskService struct {
	Repository *repository.AgentTaskRepository
}

func NewAgentTaskService() *AgentTaskService {
	return &AgentTaskService{
		Repository: repository.NewAgentTaskRepository(),
	}
}

func (service AgentTaskService) convertToModel(restModel rest.AgentTaskRest) (*model.AgentTask, error) {
	var taskStatus model.AgentTaskStatus
	var taskType model.AgentTaskType
	var err error
	taskStatus, err = taskStatus.ToTaskStatus(restModel.TaskStatus)
	if err != nil {
		return &model.AgentTask{}, err
	}
	restModel.Name = uuid.New().String()
	return &model.AgentTask{
		BaseEntityRefModel:   ConvertToBaseEntityRefModel(restModel.BaseEntityRefModelRest),
		AgentId:              restModel.AgentId,
		DeploymentId:         restModel.DeploymentId,
		CustomTaskDetails:    restModel.CustomTaskDetails,
		TaskType:             taskType.ToTaskType(restModel.TaskType),
		TaskStatus:           taskStatus,
		TaskResult:           restModel.TaskResult,
		HasMultipleExecution: restModel.HasMultipleExecution,
	}, nil
}

func (service AgentTaskService) ConvertToRest(entityModel model.AgentTask) rest.AgentTaskRest {
	taskRest := rest.AgentTaskRest{
		BaseEntityRefModelRest: ConvertToBaseEntityRefModelRest(entityModel.BaseEntityRefModel),
		AgentId:                entityModel.AgentId,
		DeploymentId:           entityModel.DeploymentId,
		CustomTaskDetails:      entityModel.CustomTaskDetails,
		TaskType:               entityModel.TaskType.String(),
		TaskStatus:             entityModel.TaskStatus.String(),
		HasMultipleExecution:   entityModel.HasMultipleExecution,
	}
	taskResult := entityModel.TaskResult
	if !common.IsBase64Encoded(taskResult) {
		taskResult = base64.StdEncoding.EncodeToString([]byte(taskResult))
	}
	taskRest.TaskResult = taskResult

	if common.PACKAGE.String() == entityModel.RefModel {
		pkg, err := NewPackageService().Repository.GetById(entityModel.RefId, false)
		if err == nil {
			taskRest.DisplayName = pkg.DisplayName
			taskRest.IconFile = rest.ConvertToFileMetaDataRest(pkg.IconFile)
		}
	} else if common.CONFIGURATION.String() == entityModel.RefModel {
		configuration, err := NewConfigurationService().Repository.GetById(entityModel.RefId, false)
		if err == nil {
			taskRest.DisplayName = configuration.DisplayName
		}
	} else if common.COMPLIANCES.String() == entityModel.RefModel {
		compliance, err := NewComplianceService().Repository.GetById(entityModel.RefId, false)
		if err == nil {
			taskRest.DisplayName = compliance.DisplayName
		}
	} else if common.PATCH.String() == strings.ToLower(entityModel.RefModel) {
		pch, err := NewPatchService().Repository.GetById(entityModel.RefId, false)
		if err == nil {
			taskRest.DisplayName = pch.Title
			taskRest.Name = pch.Name
		}
	} else if common.SYSTEM_ACTION.String() == entityModel.RefModel {
		action, err := NewSystemActionService().Repository.GetById(entityModel.RefId)
		if err == nil {
			taskRest.DisplayName = action.Name
		}
	}

	asset, err := NewAgentService().GetAssetMetaDataById(taskRest.AgentId)
	if err == nil {
		taskRest.AssetName = asset.ComputerName
	}

	return taskRest
}

func (service AgentTaskService) performPartialUpdate(agentTask *model.AgentTask, agentTaskRest rest.AgentTaskRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBaseRef(&agentTask.BaseEntityRefModel, agentTaskRest.BaseEntityRefModelRest)

	if agentTaskRest.PatchMap["hasMultipleExecution"] != nil && agentTaskRest.HasMultipleExecution != agentTask.HasMultipleExecution {
		common.PrepareInDiffMap("has_multiple_execution", agentTask.HasMultipleExecution, agentTaskRest.HasMultipleExecution, &diffMap)
		agentTask.HasMultipleExecution = agentTaskRest.HasMultipleExecution
	}

	if agentTaskRest.PatchMap["taskResult"] != nil && agentTaskRest.TaskResult != agentTask.TaskResult {
		common.PrepareInDiffMap("task_result", agentTask.TaskResult, agentTaskRest.TaskResult, &diffMap)
		agentTask.TaskResult = agentTaskRest.TaskResult
	}

	if agentTaskRest.PatchMap["taskStatus"] != nil && agentTaskRest.TaskStatus != agentTask.TaskStatus.String() {
		var agentTaskStatus model.AgentTaskStatus
		var err error
		agentTaskStatus, err = agentTaskStatus.ToTaskStatus(agentTaskRest.TaskStatus)
		if err == nil {
			common.PrepareInDiffMap("task_status", agentTask.TaskStatus.String(), agentTaskRest.TaskStatus, &diffMap)
			agentTask.TaskStatus = agentTaskStatus
		}
	}

	if agentTaskRest.PatchMap["taskType"] != nil && agentTaskRest.TaskType != agentTask.TaskType.String() {
		var agentTaskType model.AgentTaskType
		agentTaskType = agentTaskType.ToTaskType(agentTaskRest.TaskType)
		common.PrepareInDiffMap("task_type", agentTask.TaskType.String(), agentTaskRest.TaskType, &diffMap)
		agentTask.TaskType = agentTaskType
	}

	if agentTaskRest.PatchMap["customTaskDetails"] != nil && !reflect.DeepEqual(agentTask.CustomTaskDetails, agentTaskRest.CustomTaskDetails) {
		common.PrepareInDiffMap("custom_task_details", agentTask.CustomTaskDetails, agentTaskRest.CustomTaskDetails, &diffMap)
		agentTask.CustomTaskDetails = agentTaskRest.CustomTaskDetails
	}

	return diffMap, len(diffMap) > 0
}

func (service AgentTaskService) Create(restModel rest.AgentTaskRest) (int64, error) {
	restModel.CreatedTime = common.CurrentMillisecond()
	restModel.UpdatedTime = common.CurrentMillisecond()
	restModel.CreatedById = common.GetUserFromCallContext()
	agentTask, err := service.convertToModel(restModel)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(agentTask)
	//TODO: Handle after create
	agentTask.Id = id
	service.PostOperation(agentTask)
	return id, err
}

func (service AgentTaskService) Update(id int64, restModel rest.AgentTaskRest) (bool, common.CustomError) {
	agentTask, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	diffMap, isUpdatable := service.performPartialUpdate(&agentTask, restModel)
	if isUpdatable {
		agentTask.UpdatedTime = common.CurrentMillisecond()
		agentTask.UpdatedById = common.GetUserFromCallContext()
		_, err := service.Repository.Update(&agentTask)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		service.PostOperation(&agentTask)
		go service.AfterUpdate(diffMap, agentTask)
		return true, common.CustomError{}
	} else {
		return false, common.CustomError{}
	}
}

func (service AgentTaskService) UpdateAgentTask(agentTask model.AgentTask, patchMap map[string]interface{}) (bool, common.CustomError) {
	taskRest := service.ConvertToRest(agentTask)
	taskRest.PatchMap = patchMap
	return service.Update(agentTask.Id, taskRest)
}

func (service AgentTaskService) GetAgentTaskById(id int64) (rest.AgentTaskRest, error) {
	agentTask, err := service.Repository.GetById(id)
	if err != nil {
		return rest.AgentTaskRest{}, err
	}
	return service.ConvertToRest(agentTask), nil
}

func (service AgentTaskService) GetAllAgentTasks(taskStatus, agentId int64, offset, size int) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var agentTaskList []model.AgentTask
	var err error
	if agentId == 0 && offset == 0 && size == 0 {
		responsePage.TotalCount = service.Repository.Count()
		if responsePage.TotalCount > 0 {
			agentTaskList, err = service.Repository.GetAllAgentTasks()
		} else {
			responsePage.ObjectList = make([]interface{}, 0)
		}
	} else if agentId == 0 && size != 0 {
		responsePage.TotalCount = service.Repository.Count()
		if responsePage.TotalCount > 0 {
			agentTaskList, err = service.Repository.GetByOffsetAndSize(offset, size)
		} else {
			responsePage.ObjectList = make([]interface{}, 0)
		}
	} else if size != 0 {
		responsePage.TotalCount = service.Repository.CountByAgentId(taskStatus, agentId)
		if responsePage.TotalCount > 0 {
			agentTaskList, err = service.Repository.GetByAgentIdByOffsetAndSize(taskStatus, agentId, offset, size)
		} else {
			responsePage.ObjectList = make([]interface{}, 0)
		}
	} else {
		responsePage.TotalCount = service.Repository.CountByAgentId(taskStatus, agentId)
		if responsePage.TotalCount > 0 {
			agentTaskList, err = service.Repository.GetAllByAgentId(taskStatus, agentId)
		} else {
			responsePage.ObjectList = make([]interface{}, 0)
		}
	}
	if err != nil {
		return responsePage, err
	}
	responsePage.ObjectList = service.convertListToRest(agentTaskList)
	return responsePage, nil
}

func (service AgentTaskService) GetAllAgentTaskByView(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var agentTaskList []view.AgentTaskDetailsView
	var err error
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_task_details_view", true, "")
	count := service.Repository.CountByQuery(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_task_details_view", false, "")
		var taskList []view.AgentTaskDetailsView
		agentTaskList, err = service.Repository.GetAllTaskByViewQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		if len(agentTaskList) > 0 {
			for _, agentTask := range agentTaskList {
				if agentTask.AgentTaskResult != "" && !common.IsBase64Encoded(agentTask.AgentTaskResult) {
					agentTask.AgentTaskResult = base64.StdEncoding.EncodeToString([]byte(agentTask.AgentTaskResult))
				}
				taskList = append(taskList, agentTask)
			}
		}
		agentTaskList = nil
		responsePage.ObjectList = taskList
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AgentTaskService) GetAllAgentTaskBySearch(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var agentTaskList []model.AgentTask
	var err error
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_tasks", true, "")
	count := service.Repository.CountByQuery(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_tasks", false, "")
		agentTaskList, err = service.Repository.GetAllTaskByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(agentTaskList)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AgentTaskService) GetAgentTasks(filter rest.SearchFilter) ([]rest.AgentTaskRest, error) {
	var agentTaskList []model.AgentTask
	var agentTaskRestList []rest.AgentTaskRest
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_tasks", false, "")
	agentTaskList, err = service.Repository.GetAllTaskByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return agentTaskRestList, err
	}
	if len(agentTaskList) != 0 {
		for _, task := range agentTaskList {
			taskRest := service.ConvertToRest(task)
			agentTaskRestList = append(agentTaskRestList, taskRest)
		}
	}

	return agentTaskRestList, nil
}

func (service AgentTaskService) GetAllTasks(filter rest.SearchFilter) ([]model.AgentTask, error) {
	var agentTaskList []model.AgentTask
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "agent_tasks", false, "")
	agentTaskList, err = service.Repository.GetAllTaskByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return agentTaskList, err
	}

	return agentTaskList, nil
}

func (service AgentTaskService) GetAgentTaskByRefIdsAndRefModel(requestByIds rest.RequestByIdsAndModel) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var agentTaskList []model.AgentTask
	var err error
	count := service.Repository.CountByRefIdsAndRefModel(requestByIds.Ids, requestByIds.Model)
	if count > 0 {
		agentTaskList, err = service.Repository.GetAllTaskByRefIdsAndRefModel(requestByIds.Ids, requestByIds.Model)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(agentTaskList)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AgentTaskService) convertListToRest(agentTaskList []model.AgentTask) []rest.AgentTaskRest {
	var agentTaskRestList []rest.AgentTaskRest
	if len(agentTaskList) != 0 {
		for _, task := range agentTaskList {
			taskRest := service.ConvertToRest(task)
			agentTaskRestList = append(agentTaskRestList, taskRest)
		}
	}
	return agentTaskRestList
}

func (service AgentTaskService) AfterUpdate(diffMap map[string]map[string]interface{}, task model.AgentTask) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for task id : ", task.Id, err)
		}
	}()

	if task.TaskType == model.DEPLOYMENT && diffMap != nil && diffMap["task_status"] != nil {
		logger.ServiceLogger.Info("agent task updated so need to update deployment status Agent task status :", task.TaskStatus)
		if task.TaskStatus == model.TaskInProgress {
			logger.ServiceLogger.Info("task in-progress for task id :", task.Id)
			deploymentRepository := NewDeploymentService().Repository
			deployment, _ := deploymentRepository.GetById(task.DeploymentId, false)
			if deployment.DeploymentStage == model.Initiated {
				deployment.DeploymentStage = model.InProgress
				deployment.StartTime = time.Now().UnixMilli()
				_, err := deploymentRepository.Update(&deployment)
				if err != nil {
					logger.ServiceLogger.Error("Error while performing after create process for task result : ", task.Id, err)
				}
			}
		} else if task.TaskStatus == model.TaskFailed || task.TaskStatus == model.TaskSuccess || task.TaskStatus == model.TaskCancelled {
			logger.ServiceLogger.Info("task success or failed for task id :", task.Id)
			if task.TaskStatus == model.TaskSuccess && (task.RefModel == common.PATCH.String() || task.RefModel == "Patch") {
				assetPatchRelationService := NewAssetPatchRelationService()
				agentPatchRelations, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{Qualification: []rest.Qualification{
					rest.BuildQualification("asset_id", "equals", task.AgentId, constant.AND.String()),
					rest.BuildQualification("patch_id", "equals", task.RefId, constant.AND.String()),
				}})
				if err == nil && len(agentPatchRelations) > 0 {
					for _, agentPatchRelation := range agentPatchRelations {
						if agentPatchRelation.PatchState == model.Installed.String() {
							agentPatchRelation.PatchState = model.Missing.String()
						} else if agentPatchRelation.PatchState == model.Missing.String() {
							agentPatchRelation.PatchState = model.Installed.String()
						}
						_, err = assetPatchRelationService.UpdateAgentPatchRelation(agentPatchRelation.Id, agentPatchRelation)
						if err != nil {
							logger.ServiceLogger.Error("[AgentTaskService][AfterUpdate]", err)
						}
					}
				}
			}

			filter := rest.SearchFilter{
				Offset:          0,
				Size:            0,
				IncludeArchived: false,
				SortBy:          "",
				Qualification: []rest.Qualification{{
					Column:   "deploymentId",
					Operator: "Equals",
					Value:    strconv.FormatInt(task.DeploymentId, 10),
				}},
			}
			query := rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK.String(), false, "")
			allDeploymentTasks, err := service.Repository.GetAllTaskByQuery(query.Query, query.Parameters)
			logger.ServiceLogger.Info(fmt.Sprintf("total %d task found for deplotmane id %d", len(allDeploymentTasks), task.DeploymentId))

			if err == nil && len(allDeploymentTasks) > 0 {
				isAllTaskCompleted := true
				for _, agentTask := range allDeploymentTasks {
					logger.ServiceLogger.Info("task status : ", agentTask.TaskStatus, "for task id :", agentTask.Id)
					if !(agentTask.TaskStatus == model.TaskSuccess || agentTask.TaskStatus == model.TaskFailed || agentTask.TaskStatus == model.TaskCancelled) {
						isAllTaskCompleted = false
					}
				}
				logger.ServiceLogger.Info("isAllTaskCompleted -> ", isAllTaskCompleted)
				if isAllTaskCompleted {
					deploymentRepository := NewDeploymentService().Repository
					deployment, _ := deploymentRepository.GetById(task.DeploymentId, false)
					if task.HasMultipleExecution {
						deployment.DeploymentStage = model.Idle
						deployment.LastExecutionTime = time.Now().UnixMilli()
					} else {
						deployment.DeploymentStage = model.Completed
						deployment.LastExecutionTime = time.Now().UnixMilli()
						if deployment.Origin == model.AUTO_PATCH_TEST {
							go NewAutoPatchTestService().UpdatePatchTestStatus(deployment.Id)
						}
					}
					_, err := deploymentRepository.Update(&deployment)
					if err != nil {
						logger.ServiceLogger.Error("Error while performing update deployment : ", deployment.Id, err)
					}
					go NewDeploymentService().PrepareDeploymentCompletedReport(task.DeploymentId)
				}

				if task.RefModel == common.COMPLIANCES.String() {
					deploymentRepository := NewDeploymentService().Repository
					deployment, _ := deploymentRepository.GetById(task.DeploymentId, false)
					taskResult := &model.ComplianceTaskResult{
						TaskStatus:   task.TaskStatus,
						DeploymentId: task.DeploymentId,
						FrameworkId:  common.ConvertToInt64(task.CustomTaskDetails["bundleId"]),
						ComplianceId: task.RefId,
						AgentId:      task.AgentId,
						TaskResult:   task.TaskResult,
					}
					taskResult.CreatedTime = deployment.StartTime
					_, err := NewComplianceService().CreateComplianceTaskResult(taskResult)
					if err != nil {
						logger.ServiceLogger.Error("Error while performing after create process for compliance task result : ", task.Id, err)
					}
				} else {
					if task.HasMultipleExecution {
						taskResult := rest.AgentTaskResultRest{
							TaskStatus: task.TaskStatus.String(),
							TaskResult: task.TaskResult,
						}
						taskResult.RefId = task.Id
						_, err := NewAgentTaskResultService().Create(taskResult)
						if err != nil {
							logger.ServiceLogger.Error("Error while performing after create process for task result : ", task.Id, err)
						}
					}
				}
			}
		}
	}
}

func (service AgentTaskService) PostOperation(task *model.AgentTask) {
	taskValue := *task
	if taskValue.TaskStatus == model.TaskReadyToDeploy {
		cache.AddAgentTask(taskValue.AgentId, strconv.FormatInt(taskValue.Id, 10), taskValue)
	} else {
		cache.RemoveAgentTask(taskValue.AgentId, strconv.FormatInt(taskValue.Id, 10))
	}
}

func (service AgentTaskService) CreateApplicationPolicyAgentTask(context map[string]interface{}) {
	logger.ServiceLogger.Info("CreateApplicationPolicyAgentTask : ", context)
	if context != nil && len(context) > 0 {
		agentTask := rest.AgentTaskRest{}
		agentTask.AgentId = int64(context["agent_id"].(float64))
		agentTask.RefId = int64(context["ref_id"].(float64))
		agentTask.CustomTaskDetails = context["custom_task_details"].(map[string]interface{})
		agentTask.RefModel = common.APPLICATION_CONTROL_POLICY.String()
		agentTask.TaskStatus = model.TaskReadyToDeploy.String()
		agentTask.TaskType = model.APPLICATION_CONTROL_POLICY.String()
		_, err := service.Create(agentTask)
		if err != nil {
			logger.ServiceLogger.Error("[AgentTaskService][CreateApplicationPolicyAgentTask]", err)
		}
	}
}
