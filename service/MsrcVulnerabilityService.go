package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
)

type MsrcVulnerabilityService struct {
	Repository *repository.MsrcVulnerabilityRepository
}

func NewMsrcVulnerabilityService() *MsrcVulnerabilityService {
	return &MsrcVulnerabilityService{Repository: repository.NewMsrcVulnerabilityRepository()}
}

func (service MsrcVulnerabilityService) ExecuteMsrcVulnerabilitiesSync() {
	logger.ServiceLogger.Info("syncing msrc vulnerabilities ")
	var payload []byte
	url := fmt.Sprintf("%s/msrc/vulnerabilities/search", common.CentralRepoUrl())
	updatedTime := int64(0)
	qual := rest.Qualification{}
	msrcVulnerability, err := service.Repository.GetLatestMsrcVulnerability()
	if err == nil {
		updatedTime = msrcVulnerability.UpdatedTime
	}

	if updatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(updatedTime, 10)
	}

	offset := 0
	limit := 300
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		var filter rest.SearchFilter
		if updatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "-id,updated_time", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "-id,updated_time"}
		}
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		if success && response != nil && len(response) > 0 {
			var pageResponse rest.ListResponseRest
			err := json.Unmarshal(response, &pageResponse)
			if err == nil && pageResponse.TotalCount > 0 {
				totalCount = pageResponse.TotalCount
				var msrcVulnerabilityRests []rest.MsrcVulnerabilityRest
				data, _ := json.Marshal(pageResponse.ObjectList)
				err1 := json.Unmarshal(data, &msrcVulnerabilityRests)
				if err1 != nil {
					logger.ServiceLogger.Error("[ExecuteMsrcVulnerabilitiesSync]", err1)
				}
				service.BulkCreateOrUpdate(msrcVulnerabilityRests)
			}
		}
		offset += limit
		shouldExecute = offset < totalCount
	}
	logger.ServiceLogger.Info("msrc vulnerabilities completed.")
}

func (service MsrcVulnerabilityService) convertToModel(msrcVulnerabilityRest rest.MsrcVulnerabilityRest) (*model.MsrcVulnerability, error) {
	return &model.MsrcVulnerability{
		Name:            msrcVulnerabilityRest.Name,
		CreatedById:     msrcVulnerabilityRest.CreatedById,
		CreatedTime:     msrcVulnerabilityRest.CreatedTime,
		UpdatedById:     msrcVulnerabilityRest.UpdatedById,
		UpdatedTime:     msrcVulnerabilityRest.UpdatedTime,
		OOB:             msrcVulnerabilityRest.OOB,
		Removed:         msrcVulnerabilityRest.Removed,
		CVE:             msrcVulnerabilityRest.CVE,
		Description:     msrcVulnerabilityRest.Description,
		FAQs:            msrcVulnerabilityRest.FAQs,
		Tag:             msrcVulnerabilityRest.Tag,
		CNA:             msrcVulnerabilityRest.CNA,
		ExploitStatus:   msrcVulnerabilityRest.ExploitStatus,
		Mitigation:      msrcVulnerabilityRest.Mitigation,
		Workaround:      msrcVulnerabilityRest.Workaround,
		Products:        msrcVulnerabilityRest.Products,
		URL:             msrcVulnerabilityRest.URL,
		Acknowledgments: msrcVulnerabilityRest.Acknowledgments,
		Revisions:       msrcVulnerabilityRest.Revisions,
	}, nil
}

func (service MsrcVulnerabilityService) convertToRest(msrcVulnerability model.MsrcVulnerability) rest.MsrcVulnerabilityRest {
	return rest.MsrcVulnerabilityRest{
		Id:              msrcVulnerability.Id,
		Name:            msrcVulnerability.Name,
		CreatedById:     msrcVulnerability.CreatedById,
		CreatedTime:     msrcVulnerability.CreatedTime,
		UpdatedById:     msrcVulnerability.UpdatedById,
		UpdatedTime:     msrcVulnerability.UpdatedTime,
		OOB:             msrcVulnerability.OOB,
		Removed:         msrcVulnerability.Removed,
		CVE:             msrcVulnerability.CVE,
		Description:     msrcVulnerability.Description,
		FAQs:            msrcVulnerability.FAQs,
		Tag:             msrcVulnerability.Tag,
		CNA:             msrcVulnerability.CNA,
		ExploitStatus:   msrcVulnerability.ExploitStatus,
		Mitigation:      msrcVulnerability.Mitigation,
		Workaround:      msrcVulnerability.Workaround,
		Products:        msrcVulnerability.Products,
		URL:             msrcVulnerability.URL,
		Acknowledgments: msrcVulnerability.Acknowledgments,
		Revisions:       msrcVulnerability.Revisions,
	}
}

func (service MsrcVulnerabilityService) BulkCreateOrUpdate(restList []rest.MsrcVulnerabilityRest) {
	if len(restList) > 0 {
		for _, msrcVulnerabilityRest := range restList {
			msrcVulnerability, err := service.Repository.GetByCve(msrcVulnerabilityRest.CVE)
			if err == nil && msrcVulnerability.Id != 0 {
				if msrcVulnerability.UpdatedTime != msrcVulnerabilityRest.UpdatedTime {
					service.Update(msrcVulnerability.Id, msrcVulnerabilityRest)
				}
			} else {
				service.Create(msrcVulnerabilityRest)
			}
		}
	}
}

func (service MsrcVulnerabilityService) BeforeCreate(msrcVulnerabilityRest rest.MsrcVulnerabilityRest) common.CustomError {
	msrcVulnerability, err := service.Repository.GetByCve(msrcVulnerabilityRest.CVE)
	if err == nil && msrcVulnerability.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service MsrcVulnerabilityService) Create(msrcVulnerabilityRest rest.MsrcVulnerabilityRest) (int64, common.CustomError) {
	exception := service.BeforeCreate(msrcVulnerabilityRest)
	if exception.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating MSRC vulnerability: %s", exception.Message))
		return 0, exception
	}

	logger.ServiceLogger.Info("Process started to create MSRC vulnerability")
	if msrcVulnerabilityRest.CreatedTime == 0 {
		msrcVulnerabilityRest.CreatedTime = common.CurrentMillisecond()
	}

	msrcVulnerabilityRest.CreatedById = common.GetUserFromCallContext()
	msrcVulnerability, err := service.convertToModel(msrcVulnerabilityRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating MSRC vulnerability: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(msrcVulnerability)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating MSRC vulnerability: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create MSRC vulnerability process completed successfully")
	return id, common.CustomError{}
}

func (service MsrcVulnerabilityService) Update(id int64, restModel rest.MsrcVulnerabilityRest) (bool, common.CustomError) {

	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update MSRC vulnerability with id - %v", id))
	msrcVulnerability, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating MSRC vulnerability for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if restModel.UpdatedTime == 0 {
		msrcVulnerability.UpdatedTime = common.CurrentMillisecond()
	} else {
		msrcVulnerability.UpdatedTime = restModel.UpdatedTime
	}

	msrcVulnerability.UpdatedById = common.GetUserFromCallContext()
	service.PerformPartialUpdate(restModel, msrcVulnerability)
	_, err = service.Repository.Update(&msrcVulnerability)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating MSRC vulnerability for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update MSRC vulnerability with id - %v", id))
	return true, common.CustomError{}
}

func (service MsrcVulnerabilityService) PerformPartialUpdate(restModel rest.MsrcVulnerabilityRest, msrcVulnerability model.MsrcVulnerability) {
	if restModel.CVE != "" {
		msrcVulnerability.CVE = restModel.CVE
	}
	if restModel.Description != "" {
		msrcVulnerability.Description = restModel.Description
	}
	msrcVulnerability.FAQs = restModel.FAQs
	if restModel.Tag != "" {
		msrcVulnerability.Tag = restModel.Tag
	}
	if restModel.CNA != "" {
		msrcVulnerability.CNA = restModel.CNA
	}
	if restModel.ExploitStatus != "" {
		msrcVulnerability.ExploitStatus = restModel.ExploitStatus
	}
	if restModel.Mitigation != "" {
		msrcVulnerability.Mitigation = restModel.Mitigation
	}
	msrcVulnerability.Products = restModel.Products
	if restModel.URL != "" {
		msrcVulnerability.URL = restModel.URL
	}
	msrcVulnerability.Acknowledgments = restModel.Acknowledgments
	msrcVulnerability.Revisions = restModel.Revisions
}

func (service MsrcVulnerabilityService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.MSRC_VULNERABILITY.String(), true, "")
	var responsePage rest.ListResponseRest
	var appPageList []model.MsrcVulnerability
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.MSRC_VULNERABILITY.String(), false, "")
		appPageList, err = service.Repository.GetAllMsrcVulnerabilities(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(appPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service MsrcVulnerabilityService) GetKBByCVE(cve string) (rest.MsrcVulnerabilityRest, error) {

	msrcVulnerability, err := service.Repository.GetByCve(cve)
	if err != nil {
		logger.ServiceLogger.Info("Error while cve : ", cve, " Error :", err.Error())
		return rest.MsrcVulnerabilityRest{}, nil
	}
	return service.convertToRest(msrcVulnerability), nil
}

func (service MsrcVulnerabilityService) convertListToRest(appList []model.MsrcVulnerability) []rest.MsrcVulnerabilityRest {
	var appRestList []rest.MsrcVulnerabilityRest
	if len(appList) != 0 {
		for _, app := range appList {
			appRest := service.convertToRest(app)
			appRestList = append(appRestList, appRest)
		}
	}
	return appRestList
}
