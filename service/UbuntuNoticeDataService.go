package service

import (
	"deployment/common"
	"deployment/logger"
	pch_model "deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type UbuntuNoticeDataService struct {
	Repository *repository.UbuntuNoticeDataRepository
}

func NewUbuntuNoticeDataService() *UbuntuNoticeDataService {
	return &UbuntuNoticeDataService{Repository: repository.NewUbuntuNoticeDataRepository()}
}

func (service UbuntuNoticeDataService) convertToModel(restModel rest.UbuntuNoticeDataRest) *pch_model.UbuntuNoticeData {
	return &pch_model.UbuntuNoticeData{
		BaseEntityModel: ConvertToBaseEntityModel(restModel.BaseEntityRest),
		CVEsIDs:         restModel.CVEsIDs,
		NoticeId:        restModel.NoticeId,
		Description:     restModel.Description,
		Instructions:    restModel.Instructions,
		IsHidden:        restModel.IsHidden,
		Published:       restModel.Published,
		Summary:         restModel.Summary,
		Title:           restModel.Title,
		Type:            restModel.Type,
		AffectedOS:      restModel.AffectedOS,
		SupportURL:      restModel.SupportURL,
		ReleaseDate:     restModel.ReleaseDate,
	}
}

func (service UbuntuNoticeDataService) convertToRest(data pch_model.UbuntuNoticeData) rest.UbuntuNoticeDataRest {
	return rest.UbuntuNoticeDataRest{
		BaseEntityRest: ConvertToBaseEntityRest(data.BaseEntityModel),
		CVEsIDs:        data.CVEsIDs,
		NoticeId:       data.NoticeId,
		Description:    data.Description,
		Instructions:   data.Instructions,
		IsHidden:       data.IsHidden,
		Published:      data.Published,
		Summary:        data.Summary,
		Title:          data.Title,
		Type:           data.Type,
		AffectedOS:     data.AffectedOS,
		SupportURL:     data.SupportURL,
		ReleaseDate:    data.ReleaseDate,
	}
}

func (service UbuntuNoticeDataService) convertListToRest(patchList []pch_model.UbuntuNoticeData) []rest.UbuntuNoticeDataRest {
	var patchRestList []rest.UbuntuNoticeDataRest
	if len(patchList) != 0 {
		for _, patch := range patchList {
			patchRest := service.convertToRest(patch)
			patchRestList = append(patchRestList, patchRest)
		}
	}
	return patchRestList
}

func (service UbuntuNoticeDataService) BulkCreateOrUpdate(restList []rest.UbuntuNoticeDataRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patch, err := service.Repository.GetByNoticeId(patchRest.NoticeId)
			if err == nil && patch.Id != 0 {
				if patch.UpdatedTime != patchRest.UpdatedTime {
					service.Update(patch.Id, patchRest)
				}
			} else {
				patchRest.Id = 0
				service.Create(patchRest)
			}
		}
	}
}

func (service UbuntuNoticeDataService) Create(rest rest.UbuntuNoticeDataRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create UbuntuNoticeData")

	// Handle base entity fields
	if rest.CreatedTime == 0 {
		rest.CreatedTime = common.CurrentMillisecond()
	}
	rest.CreatedById = common.GetUserFromCallContext()
	// Convert rest to model
	data := service.convertToModel(rest)

	// Call repository create method
	id, err := service.Repository.Create(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating UbuntuNoticeData: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create UbuntuNoticeData process completed successfully")
	return id, common.CustomError{}
}

func (service UbuntuNoticeDataService) Update(id int64, restModel rest.UbuntuNoticeDataRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update UbuntuNoticeData with id - %v", id))

	// Retrieve existing data
	data, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuNoticeData for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Handle base entity fields
	if restModel.UpdatedTime == 0 {
		data.UpdatedTime = common.CurrentMillisecond()
	} else {
		data.UpdatedTime = restModel.UpdatedTime
	}
	data.UpdatedById = common.GetUserFromCallContext()

	// Update fields as needed
	PerformPartialUpdateForBase(&data.BaseEntityModel, restModel.BaseEntityRest)
	data.CVEsIDs = restModel.CVEsIDs
	data.NoticeId = restModel.NoticeId
	data.Description = restModel.Description
	data.Instructions = restModel.Instructions
	data.IsHidden = restModel.IsHidden
	data.Published = restModel.Published
	data.Summary = restModel.Summary
	data.Title = restModel.Title
	data.Type = restModel.Type
	data.AffectedOS = restModel.AffectedOS
	data.SupportURL = restModel.SupportURL
	data.ReleaseDate = restModel.ReleaseDate

	// Call repository update method
	_, err = service.Repository.Update(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuNoticeData for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update UbuntuNoticeData with id - %v", id))
	return true, common.CustomError{}
}

func (service UbuntuNoticeDataService) Delete(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete UbuntuNoticeData for id - %v", id))

	// Check if the data exists
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting UbuntuNoticeData to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	// Call repository delete method
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting UbuntuNoticeData for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete UbuntuNoticeData for id - %v", id))
	return true, nil
}

func (service UbuntuNoticeDataService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.UBUNTU_NOTICE_DATA.String(), true, "")
	var responsePage rest.ListResponseRest
	var dataList []pch_model.UbuntuNoticeData
	var err error

	// Count total records
	count, _ := service.Repository.CountByQuery(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		// Fetch data based on search filter
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.UBUNTU_NOTICE_DATA.String(), false, "")
		dataList, err = service.Repository.GetAllUbuntuNoticeDataByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		// Convert data list to rest list
		responsePage.ObjectList = service.convertListToRest(dataList)

	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}

	responsePage.TotalCount = count
	return responsePage, nil
}
