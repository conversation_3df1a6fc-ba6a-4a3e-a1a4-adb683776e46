package service

import (
	"deployment/cache"
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"encoding/json"
	"strconv"
	"strings"
)

type AgentService struct {
}

func NewAgentService() *AgentService {
	return &AgentService{}
}

func (service AgentService) GetAsset(assetId string) (map[string]interface{}, error) {
	var astDetails map[string]interface{}
	astDetails, _ = cache.AssetCache.Get(assetId)
	return astDetails, nil
}

func (service AgentService) GetAssetMetaDataById(assetId int64) (rest.AssetMetaDataRest, error) {
	var err error
	assetDetails, _ := service.GetAsset(strconv.FormatInt(assetId, 10))
	var response rest.AssetMetaDataRest
	if assetDetails != nil {
		assetDetail, _ := json.Marshal(assetDetails)
		err = json.Unmarshal(assetDetail, &response)
		if err != nil {
			logger.ServiceLogger.Error("GetAssetMetaDataById", err)
		}
	}
	return response, err
}

func (service AgentService) GetAllAssetIdsByScope(scopeFilter model.AgentScopeFilter) ([]int64, error) {
	assetDetailsMap := cache.AssetCache.GetAll()
	var assetIds []int64
	scope := model.Int64ToAgentScope(scopeFilter.Scope)
	if scope == model.AllAssets {
		for id := range assetDetailsMap {
			assetId, _ := common.ConvertToLong(id)
			assetIds = append(assetIds, assetId)
		}
	} else if scope == model.SpecificAssets {
		assetIds = scopeFilter.Assets
	} else {
		platform := "Linux"
		if scope == model.Windows {
			platform = "Windows"
		} else if scope == model.Mac {
			platform = "Mac"
		}
		for id, assetDetails := range assetDetailsMap {
			if platformMatches(assetDetails, platform, scopeFilter.PlatformVersions) {
				assetId, _ := common.ConvertToLong(id)
				assetIds = append(assetIds, assetId)
			}
		}
	}
	return assetIds, nil
}

func platformMatches(assetDetails map[string]interface{}, platform string, platformVersions []string) bool {
	isValidAsset := false
	platformVal, ok := assetDetails["platform"].(string)
	if !ok {
		return false
	}
	isValidAsset = strings.EqualFold(retrievePlatformType(platformVal), platform)
	if isValidAsset && platformVersions != nil && len(platformVersions) > 0 {
		platformVersion, ok := assetDetails["platform_version"].(string)
		if !ok {
			isValidAsset = false
		} else {
			isValidAsset = common.StringContainsInList(platformVersions, platformVersion)
		}
	}
	return isValidAsset
}

func retrievePlatformType(platform string) string {
	if strings.EqualFold(platform, "windows") {
		return "Windows"
	} else if strings.EqualFold(platform, "darwin") || strings.EqualFold(platform, "mac") {
		return "Mac"
	} else {
		return "Linux"
	}
}

func GetAllEndpointIdsFromCGFilter(cgFilter model.ComputerGroupFilter) []int64 {
	var endpointIds []int64
	if cgFilter.CGFilterType == model.AllCg {
		groups, err := NewComputerGroupService().GetAllComputerGroups(rest.SearchFilter{})
		if err == nil && groups.TotalCount > 0 {
			for _, cg := range groups.ObjectList.([]rest.ComputerGroupRest) {
				endpointIds = append(endpointIds, cg.AssetIds...)
			}
		}
	} else if cgFilter.CGFilterType == model.SpecificCg {
		groups, err := NewComputerGroupService().GetAllComputerGroups(rest.SearchFilter{
			Qualification: []rest.Qualification{rest.BuildQualification("id", "in", cgFilter.ComputerGroupIds, "and")},
		})
		if err == nil && groups.TotalCount > 0 {
			for _, cg := range groups.ObjectList.([]rest.ComputerGroupRest) {
				endpointIds = append(endpointIds, cg.AssetIds...)
			}
		}
	} else if cgFilter.CGFilterType == model.Scope {
		assetIds, err := NewAgentService().GetAllAssetIdsByScope(cgFilter.AgentScopeFilter)
		if err == nil {
			endpointIds = append(endpointIds, assetIds...)
		}
	}
	return endpointIds
}

func (service AgentService) GetPatchPlatformByScope(scopeStr string, values []interface{}) []int64 {
	scope := model.Int64ToAgentScope(common.ConvertToInt64(scopeStr))
	if scope == model.Windows {
		return []int64{int64(common.Windows)}
	} else if scope == model.Mac {
		return []int64{int64(common.MacOS)}
	} else if scope == model.Linux {
		return []int64{int64(common.Ubuntu)}
	} else if scope == model.SpecificAssets {
		if values == nil || len(values) == 0 {
			return nil
		}
		var platforms []int64
		seen := make(map[int64]bool)
		for _, assetId := range values {
			assetDetails, _ := cache.AssetCache.Get(common.ConvertIntToString(int64(assetId.(float64))))
			if assetDetails != nil {
				if platformVal, ok := assetDetails["platform"].(string); ok {
					platform := int64(common.Ubuntu)
					if strings.EqualFold(platformVal, "darwin") || strings.EqualFold(platformVal, "mac") {
						platform = int64(common.MacOS)
					} else if strings.EqualFold(platformVal, "windows") {
						platform = int64(common.Windows)
					}
					if !seen[platform] {
						platforms = append(platforms, platform)
						seen[platform] = true
					}
				}
			}
		}
		return platforms
	} else if scope == model.SpecificDepartment {
		if values == nil || len(values) == 0 {
			return nil
		}
		var platforms []int64
		seen := make(map[int64]bool)
		var departmentSet map[int64]struct{}

		departmentSet = make(map[int64]struct{})
		for _, v := range values {
			departmentSet[int64(v.(float64))] = struct{}{}
		}
		assetDetailsMap := cache.AssetCache.GetAll()
		if assetDetailsMap != nil && len(assetDetailsMap) > 0 {
			for _, assetDetails := range assetDetailsMap {
				if assetDetails == nil {
					continue
				}
				if deptID, ok := assetDetails["department"].(int64); ok {
					if _, found := departmentSet[deptID]; found {
						if platformVal, ok := assetDetails["platform"].(string); ok {
							platform := int64(common.Ubuntu)
							if strings.EqualFold(platformVal, "darwin") || strings.EqualFold(platformVal, "mac") {
								platform = int64(common.MacOS)
							} else if strings.EqualFold(platformVal, "windows") {
								platform = int64(common.Windows)
							}
							if !seen[platform] {
								platforms = append(platforms, platform)
								seen[platform] = true
							}
						}
					}
				}

			}
		}
		return platforms
	}
	return nil
}
