package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type LanguageService struct {
	Repository *repository.LanguageRepository
}

func NewLanguageService() *LanguageService {
	return &LanguageService{Repository: repository.NewLanguageRepository()}
}

func (service LanguageService) convertToModel(languageRest rest.LanguageRest) (*model.Language, error) {
	languageRest.Name = languageRest.EnglishName
	return &model.Language{
		BaseEntityModel: ConvertToBaseEntityModel(languageRest.BaseEntityRest),
		Code:            languageRest.Code,
		EnglishName:     languageRest.EnglishName,
	}, nil
}

func (service LanguageService) convertToRest(language model.Language) rest.LanguageRest {
	return rest.LanguageRest{
		BaseEntityRest: ConvertToBaseEntityRest(language.BaseEntityModel),
		Code:           language.Code,
		EnglishName:    language.EnglishName,
	}
}

func (service LanguageService) BeforeCreate(languageRest rest.LanguageRest) common.CustomError {
	language, err := service.Repository.GetByCode(languageRest.Code)
	if err == nil && language.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service LanguageService) Create(rest rest.LanguageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Debug("Process started to create patch language ", rest.EnglishName)
	if rest.CreatedTime == 0 {
		rest.CreatedTime = common.CurrentMillisecond()
	}
	rest.CreatedById = common.GetUserFromCallContext()
	language, err := service.convertToModel(rest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch language: %s : %s", rest.EnglishName, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(language)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch language: %s : %s", rest.EnglishName, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Debug("patch language ", rest.EnglishName, " created successfully.")
	return id, common.CustomError{}
}

func (service LanguageService) Update(id int64, restModel rest.LanguageRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update language with id - %v", id))
	language, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating language for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	if restModel.UpdatedTime == 0 {
		language.UpdatedTime = common.CurrentMillisecond()
	} else {
		language.UpdatedTime = restModel.UpdatedTime
	}
	language.UpdatedById = common.GetUserFromCallContext()

	if restModel.Code != 0 {
		language.Code = restModel.Code
	}
	if restModel.EnglishName != "" {
		language.EnglishName = restModel.EnglishName
	}

	_, err = service.Repository.Update(&language)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating language for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update language with id - %v", id))
	return true, common.CustomError{}
}

func (service LanguageService) GetLanguage(id int64, includeArchive bool) (rest.LanguageRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get language for id %v", id))
	var languageRest rest.LanguageRest
	language, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting language for id - %v, Error: %s", id, err.Error()))
		return languageRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get language for id %v", id))
	return service.convertToRest(language), nil
}

func (service LanguageService) GetLanguageByCode(code int64) (rest.LanguageRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get language for code %v", code))
	var languageRest rest.LanguageRest
	language, err := service.Repository.GetByCode(code)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting language for code - %v, Error: %s", code, err.Error()))
		return languageRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get language for code %v", code))
	return service.convertToRest(language), nil
}

func (service LanguageService) DeleteLanguage(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete language for id - %v", id))
	_, err := service.Repository.GetById(id, permanentDelete)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting language to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.PermanentDeleteById(id)

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting language for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete language for id - %v", id))
	return true, nil
}

func (service LanguageService) BulkCreate(restList []rest.LanguageRest) {
	if len(restList) > 0 {
		for _, languageRest := range restList {
			language, err := service.Repository.GetByCode(languageRest.Code)
			if err == nil && language.Id != 0 {
				if language.UpdatedTime != languageRest.UpdatedTime {
					service.Update(language.Id, languageRest)
				}
			} else {
				languageRest.Id = 0
				service.Create(languageRest)
			}
		}
	}
}

func (service LanguageService) GetAllLanguage(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_LANGUAGE.String(), true, "")
	var responsePage rest.ListResponseRest
	var languagePageList []model.Language
	var err error
	count := service.Repository.Count(countQuery.Query)
	if count > 0 {
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_LANGUAGE.String(), false, "")
		languagePageList, err = service.Repository.GetAllLanguage(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(languagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service LanguageService) convertListToRest(languageList []model.Language) []rest.LanguageRest {
	var languageRestList []rest.LanguageRest
	if len(languageList) != 0 {
		for _, pkg := range languageList {
			packageRest := service.convertToRest(pkg)
			languageRestList = append(languageRestList, packageRest)
		}
	}
	return languageRestList
}
