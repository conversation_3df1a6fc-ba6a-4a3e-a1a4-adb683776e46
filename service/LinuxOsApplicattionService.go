package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type LinuxOsApplicationService struct {
	Repository *repository.LinuxOsApplicationRepository
}

func NewLinuxOsApplicationService() *LinuxOsApplicationService {
	return &LinuxOsApplicationService{Repository: repository.NewLinuxOsApplicationRepository()}
}

func (service LinuxOsApplicationService) convertToModel(rest rest.LinuxOsApplicationRest) *model.LinuxOsApplication {
	var productType model.PatchProductType
	productType = productType.ToPatchProductType(rest.ProductType)

	var os common.OsType
	os = os.ToOsType(rest.Platform)

	return &model.LinuxOsApplication{
		BaseEntityModel: ConvertToBaseEntityModel(rest.BaseEntityRest),
		Version:         rest.Version,
		Distribution:    rest.Distribution,
		Description:     rest.Description,
		Arch:            rest.Arch,
		Hidden:          rest.Hidden,
		ProductType:     productType,
		NameWithDistro:  rest.NameWithDistro,
		NameWithVersion: rest.NameWithVersion,
		Platform:        os,
	}
}

func (service LinuxOsApplicationService) convertToRest(model model.LinuxOsApplication) rest.LinuxOsApplicationRest {
	return rest.LinuxOsApplicationRest{
		BaseEntityRest:  ConvertToBaseEntityRest(model.BaseEntityModel),
		Version:         model.Version,
		Distribution:    model.Distribution,
		Description:     model.Description,
		Arch:            model.Arch,
		Hidden:          model.Hidden,
		ProductType:     model.ProductType.String(),
		NameWithDistro:  model.NameWithDistro,
		NameWithVersion: model.NameWithVersion,
		Platform:        model.Platform.String(),
	}
}

func (service LinuxOsApplicationService) Create(rest rest.LinuxOsApplicationRest) (int64, common.CustomError) {

	logger.ServiceLogger.Info("Process started to create Linux OS application")
	if rest.CreatedTime == 0 {
		rest.CreatedTime = common.CurrentMillisecond()
	}
	rest.CreatedById = common.GetUserFromCallContext()
	app := service.convertToModel(rest)

	id, err := service.Repository.Create(app)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Linux OS application: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create Linux OS application process completed successfully")
	return id, common.CustomError{}
}

func (service LinuxOsApplicationService) Update(id int64, restModel rest.LinuxOsApplicationRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update Linux OS application with id - %v", id))
	app, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Linux OS application for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if restModel.UpdatedTime == 0 {
		app.UpdatedTime = common.CurrentMillisecond()
	} else {
		app.UpdatedTime = restModel.UpdatedTime
	}
	app.UpdatedById = common.GetUserFromCallContext()

	if restModel.Version != "" {
		app.Version = restModel.Version
	}
	if restModel.Distribution != "" {
		app.Distribution = restModel.Distribution
	}
	if restModel.Description != "" {
		app.Description = restModel.Description
	}
	if restModel.Arch != "" {
		app.Arch = restModel.Arch
	}
	app.Hidden = restModel.Hidden
	if restModel.ProductType != "" {
		var productType model.PatchProductType
		productType = productType.ToPatchProductType(restModel.ProductType)
		if app.ProductType != productType {
			app.ProductType = productType
		}
	}
	if restModel.NameWithDistro != "" {
		app.NameWithDistro = restModel.NameWithDistro
	}
	if restModel.NameWithVersion != "" {
		app.NameWithVersion = restModel.NameWithVersion
	}
	if restModel.Platform != "" {
		var platform common.OsType
		platform = platform.ToOsType(restModel.Platform)
		if app.Platform != platform {
			app.Platform = platform
		}
	}

	_, err = service.Repository.Update(&app)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Linux OS application for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update Linux OS application with id - %v", id))
	return true, common.CustomError{}
}

func (service LinuxOsApplicationService) Get(id int64) (rest.LinuxOsApplicationRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Linux OS application for id %v", id))
	var appRest rest.LinuxOsApplicationRest
	app, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Linux OS application for id - %v, Error: %s", id, err.Error()))
		return appRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get Linux OS application for id %v", id))
	return service.convertToRest(app), nil
}

func (service LinuxOsApplicationService) Delete(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete Linux OS application for id - %v", id))
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Linux OS application to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.DeleteById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting Linux OS application for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete Linux OS application for id - %v", id))
	return success, nil
}

func (service LinuxOsApplicationService) BulkCreateOrUpdate(restList []rest.LinuxOsApplicationRest) {
	if len(restList) > 0 {
		for _, appRest := range restList {
			existingApp, err := service.Repository.GetByNameWithVersion(appRest.NameWithVersion)
			if err == nil && existingApp.Id != 0 {
				service.Update(existingApp.Id, appRest)
			} else {
				service.Create(appRest)
			}
		}
	}
}

func (service LinuxOsApplicationService) CreateOrUpdate(restModel rest.LinuxOsApplicationRest) int64 {
	if restModel.NameWithVersion != "" {
		existingApp, err := service.Repository.GetByNameWithVersion(restModel.NameWithVersion)
		if err == nil && existingApp.Id != 0 {
			service.Update(existingApp.Id, restModel)
			return existingApp.Id
		} else {
			id, _ := service.Create(restModel)
			return id
		}
	}
	return 0
}

func (service LinuxOsApplicationService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.LINUX_OS_APPLICATION.String(), true, "")
	var responsePage rest.ListResponseRest
	var appPageList []model.LinuxOsApplication
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.LINUX_OS_APPLICATION.String(), false, "")
		appPageList, err = service.Repository.GetAllLinuxOsApplicationsByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(appPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service LinuxOsApplicationService) GetAllLinuxOsApplications(filter rest.SearchFilter) ([]model.LinuxOsApplication, error) {
	var result []model.LinuxOsApplication
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.LINUX_OS_APPLICATION.String(), false, "")
	result, err = service.Repository.GetAllLinuxOsApplicationsByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return result, err
	}
	return result, nil
}

func (service LinuxOsApplicationService) convertListToRest(appList []model.LinuxOsApplication) []rest.LinuxOsApplicationRest {
	var appRestList []rest.LinuxOsApplicationRest
	if len(appList) != 0 {
		for _, app := range appList {
			appRest := service.convertToRest(app)
			appRestList = append(appRestList, appRest)
		}
	}
	return appRestList
}
