package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type ThirdPartyPackageService struct {
	Repository *repository.ThirdPartyPackageRepository
}

func NewThirdPartyPackageService() *ThirdPartyPackageService {
	return &ThirdPartyPackageService{
		Repository: repository.NewThirdPartyPackageRepository(),
	}
}

// Convert REST to Model
func (service *ThirdPartyPackageService) convertToModel(pkgRest rest.ThirdPartyPackageRest) (*model.ThirdPartyPackage, error) {
	var os common.OsType
	os = os.ToOsType(pkgRest.Os)

	var osArch common.OsArchitecture
	osArch = osArch.ToOsArch(pkgRest.Arch)

	return &model.ThirdPartyPackage{
		BaseEntityModel:  ConvertToBaseEntityModel(pkgRest.BaseEntityRest),
		Description:      pkgRest.Description,
		Version:          pkgRest.Version,
		Os:               os,
		Arch:             osArch,
		LanguageCode:     pkgRest.LanguageCode,
		PkgFileData:      pkgRest.PkgFileData,
		LatestPackageUrl: pkgRest.LatestPackageUrl,
		Publisher:        pkgRest.Publisher,
		SupportUrl:       pkgRest.SupportUrl,
		ReleaseNote:      pkgRest.ReleaseNote,
		ReleaseDate:      pkgRest.ReleaseDate,
		Application:      pkgRest.Application,
		CveDetails:       pkgRest.CveDetails,
		Uuid:             pkgRest.Uuid,
		OsVersion:        pkgRest.OsVersion,
		ProductCode:      pkgRest.ProductCode,
		InstallCommand:   pkgRest.InstallCommand,
		UnInstallCommand: pkgRest.UnInstallCommand,
		DetectionScript:  pkgRest.DetectionScript,
	}, nil
}

// Convert Model to REST
func (service *ThirdPartyPackageService) convertToRest(pkg *model.ThirdPartyPackage) rest.ThirdPartyPackageRest {
	return rest.ThirdPartyPackageRest{
		BaseEntityRest:   ConvertToBaseEntityRest(pkg.BaseEntityModel),
		Description:      pkg.Description,
		Version:          pkg.Version,
		Os:               pkg.Os.String(),
		Arch:             pkg.Arch.String(),
		LanguageCode:     pkg.LanguageCode,
		PkgFileData:      pkg.PkgFileData,
		LatestPackageUrl: pkg.LatestPackageUrl,
		Publisher:        pkg.Publisher,
		SupportUrl:       pkg.SupportUrl,
		ReleaseNote:      pkg.ReleaseNote,
		ReleaseDate:      pkg.ReleaseDate,
		Application:      pkg.Application,
		CveDetails:       pkg.CveDetails,
		Uuid:             pkg.Uuid,
		OsVersion:        pkg.OsVersion,
		ProductCode:      pkg.ProductCode,
		InstallCommand:   pkg.InstallCommand,
		UnInstallCommand: pkg.UnInstallCommand,
		DetectionScript:  pkg.DetectionScript,
	}
}

// Create a new ThirdPartyPackage
func (service *ThirdPartyPackageService) Create(pkgRest rest.ThirdPartyPackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create Third Party Package ", pkgRest.Uuid)

	if pkgRest.CreatedTime == 0 {
		pkgRest.CreatedTime = common.CurrentMillisecond()
	}
	pkgRest.CreatedById = common.GetUserFromCallContext()
	pack, err := service.convertToModel(pkgRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating ThirdPartyPackage: %s : %s", pkgRest.Uuid, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(pack)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating ThirdPartyPackage: %s : %s", pkgRest.Uuid, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Third Party Package ", pkgRest.Uuid, " created successfully")
	return id, common.CustomError{}
}

// Update an existing ThirdPartyPackage
func (service *ThirdPartyPackageService) Update(id int64, pkgRest rest.ThirdPartyPackageRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update ThirdPartyPackage with id - %v", id))
	pack, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating ThirdPartyPackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if pkgRest.UpdatedTime == 0 {
		pack.UpdatedTime = common.CurrentMillisecond()
	} else {
		pack.UpdatedTime = pkgRest.UpdatedTime
	}
	pack.UpdatedById = common.GetUserFromCallContext()

	var os common.OsType
	os = os.ToOsType(pkgRest.Os)

	var osArch common.OsArchitecture
	osArch = osArch.ToOsArch(pkgRest.Arch)

	// Update fields
	pack.Description = pkgRest.Description
	pack.Version = pkgRest.Version
	pack.Os = os
	pack.Arch = osArch
	pack.LanguageCode = pkgRest.LanguageCode
	pack.PkgFileData = pkgRest.PkgFileData
	pack.LatestPackageUrl = pkgRest.LatestPackageUrl
	pack.Publisher = pkgRest.Publisher
	pack.SupportUrl = pkgRest.SupportUrl
	pack.ReleaseNote = pkgRest.ReleaseNote
	pack.ReleaseDate = pkgRest.ReleaseDate
	pack.Application = pkgRest.Application
	pack.CveDetails = pkgRest.CveDetails
	pack.Uuid = pkgRest.Uuid

	_, err = service.Repository.Update(&pack)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating ThirdPartyPackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update ThirdPartyPackage with id - %v", id))
	return true, common.CustomError{}
}

// Delete a ThirdPartyPackage by ID
func (service *ThirdPartyPackageService) Delete(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete ThirdPartyPackage for id - %v", id))
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting ThirdPartyPackage to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.PermanentDeleteById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting ThirdPartyPackage for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete ThirdPartyPackage for id - %v", id))
	return true, nil
}

// Get a ThirdPartyPackage by ID
func (service *ThirdPartyPackageService) Get(id int64) (rest.ThirdPartyPackageRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get ThirdPartyPackage for id %v", id))
	var pkgRest rest.ThirdPartyPackageRest
	pack, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting ThirdPartyPackage for id - %v, Error: %s", id, err.Error()))
		return pkgRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get ThirdPartyPackage for id %v", id))
	return service.convertToRest(&pack), nil
}

// List all ThirdPartyPackages with filtering
func (service *ThirdPartyPackageService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.THIRD_PARTY_PACKAGE.String(), true, "")
	var responsePage rest.ListResponseRest
	var packPageList []model.ThirdPartyPackage
	var err error
	count := service.Repository.Count(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.THIRD_PARTY_PACKAGE.String(), false, "")
		packPageList, err = service.Repository.GetAllPackage(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *ThirdPartyPackageService) GetAllThirdPartyPackages(filter rest.SearchFilter) ([]rest.ThirdPartyPackageRest, error) {
	var result []rest.ThirdPartyPackageRest
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.THIRD_PARTY_PACKAGE.String(), false, "")
	packPageList, err := service.Repository.GetAllPackage(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return result, err
	}
	result = service.convertListToRest(packPageList)
	return result, nil
}

// Convert list of models to list of REST objects
func (service *ThirdPartyPackageService) convertListToRest(packList []model.ThirdPartyPackage) []rest.ThirdPartyPackageRest {
	var packRestList []rest.ThirdPartyPackageRest
	if len(packList) != 0 {
		for _, pack := range packList {
			packRest := service.convertToRest(&pack)
			packRestList = append(packRestList, packRest)
		}
	}
	return packRestList
}

func (service ThirdPartyPackageService) BulkCreateOrUpdate(restList []rest.ThirdPartyPackageRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			pkg, _ := service.Repository.GetByUuid(patchRest.Uuid)
			if pkg.Id == 0 {
				patchRest.Id = 0
				service.Create(patchRest)
			} else {
				service.Update(pkg.Id, patchRest)
			}
		}
	}
}

func (service ThirdPartyPackageService) GetThirdPartyPatchByUuid(uuid string) (rest.ThirdPartyPackageRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Windows patch for uuid %v", uuid))
	var patchRest rest.ThirdPartyPackageRest
	patch, err := service.Repository.GetByUuid(uuid)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Windows patch for uuid - %v, Error: %s", uuid, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get Windows patch for uuid %v", uuid))
	return service.convertToRest(&patch), nil
}

// GetThirdPartyPackageMetadata retrieves ThirdPartyPackage records filtered by os type
// Returns only uuid, version, and DetectionScript fields
func (service *ThirdPartyPackageService) GetThirdPartyPackageMetadata(os string) ([]rest.ThirdPartyPackageMetadataRest, error) {
	logger.ServiceLogger.Info("Process started to get third party package metadata")
	var result []rest.ThirdPartyPackageMetadataRest
	patchPreference, err := NewPatchPreferenceService().Get()
	if err == nil && patchPreference.EnableThirdPartyPatching {
		var osType common.OsType
		filter := rest.SearchFilter{
			Qualification: []rest.Qualification{
				{
					Column:    "os",
					Value:     osType.ToOsType(os).String(), // MacOS enum value
					Operator:  "equals",
					Type:      "enum",
					Reference: "os",
				},
			},
		}

		// Use the existing secure query method to get MacOS packages
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.THIRD_PARTY_PACKAGE.String(), false, "")
		packList, err := service.Repository.GetAllPackage(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while getting MacOS third party packages: %s", err.Error()))
			return nil, err
		}
		for _, pack := range packList {
			metadata := rest.ThirdPartyPackageMetadataRest{
				Uuid:            pack.Uuid,
				Version:         pack.Version,
				DetectionScript: pack.DetectionScript,
			}
			result = append(result, metadata)
		}
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get third party package metadata, found %d packages", len(result)))
	return result, nil
}
