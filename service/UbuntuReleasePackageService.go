package service

import (
	"deployment/common"
	"deployment/logger"
	pch_model "deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type UbuntuReleasePackageService struct {
	Repository *repository.UbuntuReleasePackageRepository
}

func NewUbuntuReleasePackageService() *UbuntuReleasePackageService {
	return &UbuntuReleasePackageService{Repository: repository.NewUbuntuReleasePackageRepository()}
}

func (service UbuntuReleasePackageService) convertToModel(restModel rest.UbuntuReleasePackageRest) *pch_model.UbuntuReleasePackage {
	return &pch_model.UbuntuReleasePackage{
		BaseEntityModel:         ConvertToBaseEntityModel(restModel.BaseEntityRest),
		NoticeID:                restModel.NoticeID,
		OSVersion:               restModel.OSVersion,
		Description:             restModel.Description,
		IsSource:                restModel.IsSource,
		IsVisible:               restModel.IsVisible,
		Pocket:                  restModel.Pocket,
		SourceLink:              restModel.SourceLink,
		Version:                 restModel.Version,
		VersionLink:             restModel.VersionLink,
		NameAndVersion:          restModel.NameAndVersion,
		NameAndVersionUniqueKey: restModel.NameAndVersionUniqueKey,
	}
}

func (service UbuntuReleasePackageService) convertToRest(data pch_model.UbuntuReleasePackage) rest.UbuntuReleasePackageRest {
	return rest.UbuntuReleasePackageRest{
		BaseEntityRest:          ConvertToBaseEntityRest(data.BaseEntityModel),
		NoticeID:                data.NoticeID,
		OSVersion:               data.OSVersion,
		Description:             data.Description,
		IsSource:                data.IsSource,
		IsVisible:               data.IsVisible,
		Pocket:                  data.Pocket,
		SourceLink:              data.SourceLink,
		Version:                 data.Version,
		VersionLink:             data.VersionLink,
		NameAndVersion:          data.NameAndVersion,
		NameAndVersionUniqueKey: data.NameAndVersionUniqueKey,
	}
}

func (service UbuntuReleasePackageService) convertListToRest(packageList []pch_model.UbuntuReleasePackage) []rest.UbuntuReleasePackageRest {
	var restList []rest.UbuntuReleasePackageRest
	for _, pkg := range packageList {
		packageRest := service.convertToRest(pkg)
		restList = append(restList, packageRest)
	}
	return restList
}

func (service UbuntuReleasePackageService) BulkCreateOrUpdate(restList []rest.UbuntuReleasePackageRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patch, err := service.Repository.GetByNameAndVersionUniqueKey(patchRest.NameAndVersionUniqueKey)
			if err == nil && patch.Id != 0 {
				if patch.UpdatedTime != patchRest.UpdatedTime {
					service.Update(patch.Id, patchRest)
				}
			} else {
				patchRest.Id = 0
				service.Create(patchRest)
			}
		}
	}
}

func (service UbuntuReleasePackageService) Create(rest rest.UbuntuReleasePackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create Ubuntu Release Package ", rest.NameAndVersionUniqueKey)

	// Handle base entity fields
	if rest.CreatedTime == 0 {
		rest.CreatedTime = common.CurrentMillisecond()
	}
	rest.CreatedById = common.GetUserFromCallContext()
	// Convert rest to model
	data := service.convertToModel(rest)

	// Call repository create method
	id, err := service.Repository.Create(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating UbuntuReleasePackage: %s : %s", rest.NameAndVersionUniqueKey, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Ubuntu Release Package", rest.NameAndVersionUniqueKey, " created successfully.")
	return id, common.CustomError{}
}

func (service UbuntuReleasePackageService) Update(id int64, rest rest.UbuntuReleasePackageRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update UbuntuReleasePackage with id - %v", id))

	// Retrieve existing data
	data, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuReleasePackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Handle base entity fields
	if rest.UpdatedTime == 0 {
		data.UpdatedTime = common.CurrentMillisecond()
	} else {
		data.UpdatedTime = rest.UpdatedTime
	}
	data.UpdatedById = common.GetUserFromCallContext()
	// Update fields as needed
	PerformPartialUpdateForBase(&data.BaseEntityModel, rest.BaseEntityRest)
	data.NoticeID = rest.NoticeID
	data.OSVersion = rest.OSVersion
	data.Description = rest.Description
	data.IsSource = rest.IsSource
	data.IsVisible = rest.IsVisible
	data.Pocket = rest.Pocket
	data.SourceLink = rest.SourceLink
	data.Version = rest.Version
	data.VersionLink = rest.VersionLink
	data.NameAndVersion = rest.NameAndVersion
	data.NameAndVersionUniqueKey = rest.NameAndVersionUniqueKey

	// Call repository update method
	_, err = service.Repository.Update(data)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuReleasePackage for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update UbuntuReleasePackage with id - %v", id))
	return true, common.CustomError{}
}

func (service UbuntuReleasePackageService) Delete(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete UbuntuReleasePackage for id - %v", id))

	// Check if the data exists
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting UbuntuReleasePackage to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	// Call repository delete method
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting UbuntuReleasePackage for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete UbuntuReleasePackage for id - %v", id))
	return true, nil
}

func (service UbuntuReleasePackageService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.UBUNTU_RELEASE_PACKAGES.String(), true, "")
	var responsePage rest.ListResponseRest
	var dataList []pch_model.UbuntuReleasePackage
	var err error

	// Count total records
	count, _ := service.Repository.CountByQuery(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		// Fetch data based on search filter
		// Use secure parameterized queries to prevent SQL injection
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.UBUNTU_RELEASE_PACKAGES.String(), false, "")
		dataList, err = service.Repository.GetAllUbuntuReleasePackagesByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(dataList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}

	responsePage.TotalCount = count
	return responsePage, nil
}
