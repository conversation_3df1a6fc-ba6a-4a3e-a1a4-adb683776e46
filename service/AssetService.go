package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"encoding/base64"
	"github.com/google/uuid"
	"reflect"
	"strconv"
	"strings"
)

type AssetService struct {
	Repository *repository.AssetRepository
}

func NewAssetService() *AssetService {
	return &AssetService{Repository: repository.NewAssetRepository()}
}

func (service AssetService) GetAsset(id int64) (rest.AssetRest, error) {
	domainModel, err := service.Repository.GetById(id, false)
	if err != nil {
		asset := service.convertToModel(rest.AssetRest{AssetId: id})
		_, err = service.Repository.Create(&asset)
		if err != nil {
			logger.ServiceLogger.Error("[GetAsset]", err)
		}
		domainModel, err = service.Repository.GetById(id, false)
		return service.convertToRest(domainModel), nil
	}
	return service.convertToRest(domainModel), nil
}

func (service AssetService) UpdateAsset(id int64, restModel rest.AssetRest) (bool, error) {
	domainModel, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, err
	}

	_, updated := service.performPartialUpdate(&domainModel, restModel)
	if !updated {
		return false, nil
	}

	_, err = service.Repository.Update(&domainModel)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (service AssetService) convertToModel(restModel rest.AssetRest) model.Asset {
	restModel.Name = uuid.New().String()
	return model.Asset{
		BaseEntityModel:       ConvertToBaseEntityModel(restModel.BaseEntityRest),
		AssetId:               restModel.AssetId,
		SysInfo:               restModel.SysInfo,
		AntiMalwareVersion:    restModel.AntiMalwareVersion,
		SpywareVersion:        restModel.SpywareVersion,
		MrtVersion:            restModel.MrtVersion,
		LastPatchScanTime:     restModel.LastPatchScanTime,
		DisplayVersion:        restModel.DisplayVersion,
		InstalledSoftwareList: rest.ConvertToAssetInstallPkgMetadataList(restModel.InstalledSoftwareList),
	}
}

func (service AssetService) convertToRest(domainModel model.Asset) rest.AssetRest {
	return rest.AssetRest{
		BaseEntityRest:        ConvertToBaseEntityRest(domainModel.BaseEntityModel),
		AssetId:               domainModel.AssetId,
		SysInfo:               domainModel.SysInfo,
		AntiMalwareVersion:    domainModel.AntiMalwareVersion,
		SpywareVersion:        domainModel.SpywareVersion,
		MrtVersion:            domainModel.MrtVersion,
		LastPatchScanTime:     domainModel.LastPatchScanTime,
		DisplayVersion:        domainModel.DisplayVersion,
		InstalledSoftwareList: rest.ConvertToAssetInstallPkgMetadataRestList(domainModel.InstalledSoftwareList),
	}
}

func (service AssetService) performPartialUpdate(domainModel *model.Asset, restModel rest.AssetRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if domainModel.AssetId != restModel.AssetId {
		common.PrepareInDiffMap("assetId", domainModel.AssetId, restModel.AssetId, &diffMap)
		domainModel.AssetId = restModel.AssetId
	}

	if !strings.EqualFold(domainModel.SysInfo, restModel.SysInfo) {
		common.PrepareInDiffMap("sysInfo", domainModel.SysInfo, restModel.SysInfo, &diffMap)
		domainModel.SysInfo = restModel.SysInfo
	}

	if !strings.EqualFold(domainModel.AntiMalwareVersion, restModel.AntiMalwareVersion) {
		common.PrepareInDiffMap("antiMalwareVersion", domainModel.AntiMalwareVersion, restModel.AntiMalwareVersion, &diffMap)
		domainModel.AntiMalwareVersion = restModel.AntiMalwareVersion
	}

	if !strings.EqualFold(domainModel.SpywareVersion, restModel.SpywareVersion) {
		common.PrepareInDiffMap("spywareVersion", domainModel.SpywareVersion, restModel.SpywareVersion, &diffMap)
		domainModel.SpywareVersion = restModel.SpywareVersion
	}

	if !strings.EqualFold(domainModel.MrtVersion, restModel.MrtVersion) {
		common.PrepareInDiffMap("mrtVersion", domainModel.MrtVersion, restModel.MrtVersion, &diffMap)
		domainModel.MrtVersion = restModel.MrtVersion
	}

	if !reflect.DeepEqual(domainModel.InstalledKbList, restModel.InstalledKbList) {
		common.PrepareInDiffMap("installedKbList", domainModel.InstalledKbList, restModel.InstalledKbList, &diffMap)
		domainModel.InstalledKbList = restModel.InstalledKbList
	}

	if !reflect.DeepEqual(domainModel.LastPatchScanTime, restModel.LastPatchScanTime) {
		common.PrepareInDiffMap("lastPatchScanTime", domainModel.LastPatchScanTime, restModel.LastPatchScanTime, &diffMap)
		domainModel.LastPatchScanTime = restModel.LastPatchScanTime
	}

	if !reflect.DeepEqual(domainModel.DisplayVersion, restModel.DisplayVersion) {
		common.PrepareInDiffMap("displayVersion", domainModel.DisplayVersion, restModel.DisplayVersion, &diffMap)
		domainModel.DisplayVersion = restModel.DisplayVersion
	}

	if !reflect.DeepEqual(domainModel.InstalledSoftwareList, restModel.InstalledSoftwareList) {
		common.PrepareInDiffMap("installedSoftwareList", domainModel.InstalledSoftwareList, restModel.InstalledSoftwareList, &diffMap)
		domainModel.InstalledSoftwareList = rest.ConvertToAssetInstallPkgMetadataList(restModel.InstalledSoftwareList)
	}

	return diffMap, len(diffMap) != 0
}

func (service AssetService) GetAssetComplianceHeatMap(assetId int64) (map[string]interface{}, error) {
	heatMap := map[string]interface{}{}
	var deploymentIds []string
	complianceService := NewComplianceService()
	deploymentIds, err := complianceService.Repository.GetComplianceDeploymentIds(assetId)
	if err != nil || len(deploymentIds) == 0 {
		return heatMap, err
	}
	filter := rest.SearchFilter{
		Qualification: []rest.Qualification{
			rest.BuildQualification("id", "in", deploymentIds, "AND"),
		},
	}
	query := rest.PrepareSecureQueryFromSearchFilter(filter, "deployments", false, "")
	deployments, err := NewDeploymentService().Repository.GetAllDeployments(query.Query, query.Parameters)
	if err != nil || len(deployments) == 0 {
		return heatMap, err
	}
	bundleService := NewDeploymentBundleService()
	for _, deployment := range deployments {
		hasMultipleExecution := false
		policy, err := NewDeploymentPolicyService().Repository.GetById(deployment.DeploymentPolicyId, false)
		if err == nil && policy.Type == model.Schedule && policy.InitiateDeploymentOn == model.Recurring {
			hasMultipleExecution = true
		}
		deploymentState := map[string]interface{}{}
		var lastExecutionTime int64
		if deployment.RefIds != nil && len(deployment.RefIds) > 0 {
			ids := make([]int64, len(deployment.RefIds))
			for i, id := range deployment.RefIds {
				ids[i] = id
			}
			filter = rest.SearchFilter{
				Qualification: []rest.Qualification{
					rest.BuildQualification("id", "in", ids, "AND"),
				},
			}
			var bundlesList []model.DeploymentBundle
			// Use secure parameterized queries to prevent SQL injection
			searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.DEPLOYMENT_BUNDLE.String(), false, "")
			bundlesList, err = bundleService.Repository.GetAllBundles(searchQueryResult.Query, searchQueryResult.Parameters)
			if err != nil {
				return heatMap, err
			}
			for _, bundle := range bundlesList {
				taskState := map[string]interface{}{}
				ids = make([]int64, len(bundle.ReferenceIds))
				for i, id := range bundle.ReferenceIds {
					ids[i] = id
				}
				complianceTaskResults, _ := complianceService.Repository.GetLatestComplianceTaskResults(assetId, ids, deployment.Id, bundle.Id)
				if complianceTaskResults != nil {
					filter = rest.SearchFilter{
						Qualification: []rest.Qualification{
							rest.BuildQualification("id", "in", ids, "AND"),
						},
					}
					// Use secure parameterized queries to prevent SQL injection
					searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.COMPLIANCES.String(), false, "")
					complianceMap := map[string]interface{}{}
					compliances, _ := complianceService.Repository.GetAllCompliance(searchQueryResult.Query, searchQueryResult.Parameters)
					for _, compliance := range compliances {
						complianceMap[common.ConvertIntToString(compliance.Id)] = compliance
					}
					for _, complianceTaskResult := range complianceTaskResults {
						taskStatusState := map[string]interface{}{}
						var taskResult string
						if val, exists := complianceTaskResult["task_result"]; exists && val != nil {
							if strVal, ok := val.(string); ok {
								taskResult = strVal
							}
						}
						if !common.IsBase64Encoded(taskResult) {
							taskResult = base64.StdEncoding.EncodeToString([]byte(taskResult))
						}
						lastExecutionTime = common.ConvertToInt64(complianceTaskResult["created_time"])
						status, _ := model.AgentTaskStatusFromInt(common.ToInt(complianceTaskResult["task_status"]))
						taskStatusState["status"] = status.String()
						compliance, _ := complianceMap[common.ConvertIntToString(complianceTaskResult["compliance_id"])].(model.Compliance)
						taskStatusState["metadata"] = map[string]interface{}{
							"name":        compliance.DisplayName,
							"bindings":    compliance.Bindings,
							"description": compliance.Description,
							"ruleType":    compliance.RuleType.String(),
							"impact":      compliance.Impact.String(),
							"taskResult":  taskResult,
						}
						taskState[common.ConvertIntToString(complianceTaskResult["compliance_id"])] = taskStatusState
					}
				}
				taskState["metadata"] = map[string]interface{}{
					"name":        bundle.Name,
					"iconFile":    bundle.IconFile,
					"description": bundle.Description,
				}
				deploymentState[common.ConvertIntToString(bundle.Id)] = taskState
			}
		}
		deploymentState["metadata"] = map[string]interface{}{
			"name":              deployment.DisplayName,
			"description":       deployment.Description,
			"lastExecutionTime": lastExecutionTime,
			"recurring":         hasMultipleExecution,
		}
		heatMap[common.ConvertIntToString(deployment.Id)] = deploymentState
	}

	return heatMap, nil
}

func (service AssetService) GetAssetComplianceHistory(assetId int64, deploymentId int64, count int64) (map[string]interface{}, error) {
	heatMap := map[string]interface{}{}
	var createdTimes []int64
	complianceService := NewComplianceService()
	createdTimes, err := complianceService.Repository.GetComplianceTimes(assetId, deploymentId, count)
	if err != nil || len(createdTimes) == 0 {
		return heatMap, err
	}
	deployment, err := NewDeploymentService().Repository.GetById(deploymentId, false)
	if err != nil {
		return heatMap, err
	}
	bundleService := NewDeploymentBundleService()
	for i := len(createdTimes) - 1; i >= 0; i-- {
		createdTime := createdTimes[i]
		deploymentState := map[string]interface{}{}
		if deployment.RefIds != nil && len(deployment.RefIds) > 0 {
			ids := make([]int64, len(deployment.RefIds))
			for i, id := range deployment.RefIds {
				ids[i] = id
			}
			filter := rest.SearchFilter{
				Qualification: []rest.Qualification{
					rest.BuildQualification("id", "in", ids, "AND"),
				},
			}
			var bundlesList []model.DeploymentBundle
			// Use secure parameterized queries to prevent SQL injection
			searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.DEPLOYMENT_BUNDLE.String(), false, "")
			bundlesList, err = bundleService.Repository.GetAllBundles(searchQueryResult.Query, searchQueryResult.Parameters)
			if err != nil {
				return heatMap, err
			}
			for _, bundle := range bundlesList {
				taskState := map[string]interface{}{}
				ids = make([]int64, len(bundle.ReferenceIds))
				for i, id := range bundle.ReferenceIds {
					ids[i] = id
				}
				complianceTaskResults, _ := complianceService.Repository.GetComplianceTaskResult(assetId, ids, deployment.Id, bundle.Id, createdTime)
				if complianceTaskResults != nil {
					filter = rest.SearchFilter{
						Qualification: []rest.Qualification{
							rest.BuildQualification("id", "in", ids, "AND"),
						},
					}
					// Use secure parameterized queries to prevent SQL injection
					searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.COMPLIANCES.String(), false, "")
					complianceMap := map[string]interface{}{}
					compliances, _ := complianceService.Repository.GetAllCompliance(searchQueryResult.Query, searchQueryResult.Parameters)
					for _, compliance := range compliances {
						complianceMap[common.ConvertIntToString(compliance.Id)] = compliance
					}
					for _, complianceTaskResult := range complianceTaskResults {
						taskStatusState := map[string]interface{}{}
						var taskResult string
						if val, exists := complianceTaskResult["task_result"]; exists && val != nil {
							if strVal, ok := val.(string); ok {
								taskResult = strVal
							}
						}
						if !common.IsBase64Encoded(taskResult) {
							taskResult = base64.StdEncoding.EncodeToString([]byte(taskResult))
						}
						status, _ := model.AgentTaskStatusFromInt(common.ToInt(complianceTaskResult["task_status"]))
						taskStatusState["status"] = status.String()
						compliance, _ := complianceMap[common.ConvertIntToString(complianceTaskResult["compliance_id"])].(model.Compliance)
						taskStatusState["metadata"] = map[string]interface{}{
							"name":        compliance.DisplayName,
							"bindings":    compliance.Bindings,
							"description": compliance.Description,
							"ruleType":    compliance.RuleType.String(),
							"impact":      compliance.Impact.String(),
							"taskResult":  taskResult,
						}
						taskState[common.ConvertIntToString(complianceTaskResult["compliance_id"])] = taskStatusState
					}
				}
				taskState["metadata"] = map[string]interface{}{
					"name":        bundle.Name,
					"iconFile":    bundle.IconFile,
					"description": bundle.Description,
				}
				deploymentState[common.ConvertIntToString(bundle.Id)] = taskState
			}
		}
		deploymentState["metadata"] = map[string]interface{}{
			"name":              deployment.DisplayName,
			"description":       deployment.Description,
			"lastExecutionTime": createdTime,
		}
		key := common.ConvertIntToString(deployment.Id) + "_" + strconv.FormatInt(createdTime, 10)
		heatMap[key] = deploymentState
	}

	return heatMap, nil
}
