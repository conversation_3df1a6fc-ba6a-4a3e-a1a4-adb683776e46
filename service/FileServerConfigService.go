package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type FileServerConfigService struct {
	Repository *repository.FileServerConfigRepo
}

func NewFileServerConfigService() *FileServerConfigService {
	return &FileServerConfigService{
		Repository: repository.NewFileServerConfigRepo(),
	}
}

func (service FileServerConfigService) convertToModel(fileServerConfigRest rest.FileServerConfigRest) *model.FileServerConfig {
	return &model.FileServerConfig{
		BaseEntityModel: ConvertToBaseEntityModel(fileServerConfigRest.BaseEntityRest),
		Description:     fileServerConfigRest.Description,
		Url:             fileServerConfigRest.Url,
		LocationId:      fileServerConfigRest.LocationId,
	}
}

func (service FileServerConfigService) convertToRest(fileServerConfigInfo model.FileServerConfig) rest.FileServerConfigRest {
	return rest.FileServerConfigRest{
		BaseEntityRest: ConvertToBaseEntityRest(fileServerConfigInfo.BaseEntityModel),
		Description:    fileServerConfigInfo.Description,
		Url:            fileServerConfigInfo.Url,
		LocationId:     fileServerConfigInfo.LocationId,
	}
}

func (service FileServerConfigService) BulkCreate(restList []rest.FileServerConfigRest) {
	if len(restList) > 0 {
		for _, fileServerConfigRest := range restList {
			service.Create(fileServerConfigRest)
		}
	}
}

func (service FileServerConfigService) Create(rest rest.FileServerConfigRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to create FileServerConfig"))

	existingConfig, err := service.Repository.GetByLocation(rest.LocationId, false)
	if err == nil && existingConfig.Id > 0 {
		return 0, common.CustomError{
			Message: "Fileserver configuration already exist with same location",
			Code:    0,
		}
	}

	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	config := service.convertToModel(rest)

	id, err := service.Repository.Create(config)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating FileServerConfig ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Create FileServerConfig process completed successfully"))
	return id, common.CustomError{}
}

func (service FileServerConfigService) Update(id int64, restModel rest.FileServerConfigRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update FileServerConfig with id - %v", id))
	pkg, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating FileServerConfig for id - %v ,Error : %s ", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	pkg.UpdatedTime = common.CurrentMillisecond()
	pkg.UpdatedById = common.GetUserFromCallContext()
	_, isUpdatable := service.performPartialUpdate(&pkg, restModel)
	if isUpdatable {
		_, err := service.Repository.Update(&pkg)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating FileServerConfig for id - %v ,Error : %s ", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		return true, common.CustomError{}
	} else {
		logger.ServiceLogger.Info(fmt.Sprintf("No fields need to updated"))
		logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update FileServerConfig with id - %v", id))
		return isUpdatable, common.CustomError{}
	}
}

func (service FileServerConfigService) GetFileServerConfig(id int64, includeArchive bool) (rest.FileServerConfigRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get FileServerConfig for id %v", id))
	var pkgRest rest.FileServerConfigRest
	pkg, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get FileServerConfig for id - %v, Error : %s ", id, err.Error()))
		return pkgRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get FileServerConfig for id %v", id))
	return service.convertToRest(pkg), nil
}

func (service FileServerConfigService) DeleteFileServerConfig(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info("Process started to delete FileServerConfig for id - ", id)
	_, err := service.Repository.GetById(id, permanentDelete)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get FileServerConfig to delete for id - %v, Error : %s ", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.PermanentDeleteById(id)

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while delete FileServerConfig for id - %v, Error : %s ", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info("Process Completed to delete FileServerConfig for id - ", id)
	return true, nil
}

func (service FileServerConfigService) performPartialUpdate(model *model.FileServerConfig, restModel rest.FileServerConfigRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&model.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchMap["url"] != nil && model.Url != restModel.Url {
		common.PrepareInDiffMap("url", model.Url, restModel.Url, &diffMap)
		model.Url = restModel.Url
	}

	if restModel.PatchMap["description"] != nil && model.Description != restModel.Description {
		common.PrepareInDiffMap("description", model.Description, restModel.Description, &diffMap)
		model.Description = restModel.Description
	}

	return diffMap, len(diffMap) != 0
}

func (service FileServerConfigService) GetAllFileServerConfig(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.FILE_SERVER_CONFIG.String(), true, "")
	var responsePage rest.ListResponseRest
	var FileServerConfigPageList []model.FileServerConfig
	var err error
	count := service.Repository.Count(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.FILE_SERVER_CONFIG.String(), false, "")
		FileServerConfigPageList, err = service.Repository.GetAll(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(FileServerConfigPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service FileServerConfigService) convertListToRest(FileServerConfigs []model.FileServerConfig) []rest.FileServerConfigRest {
	var FileServerConfigRestList []rest.FileServerConfigRest
	if len(FileServerConfigs) != 0 {
		for _, pkg := range FileServerConfigs {
			FileServerConfigRest := service.convertToRest(pkg)
			FileServerConfigRestList = append(FileServerConfigRestList, FileServerConfigRest)
		}
	}
	return FileServerConfigRestList
}
