package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"strconv"
)

type AssetPatchRelationService struct {
	Repository *repository.AssetPatchRelationRepository
}

func NewAssetPatchRelationService() *AssetPatchRelationService {
	return &AssetPatchRelationService{
		Repository: repository.NewAgentPatchRelationRepository(),
	}
}

func (service AssetPatchRelationService) GetAgentPatchRelation(id int64) (rest.AssetPatchRelationRest, error) {
	relation, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.AssetPatchRelationRest{}, err
	}
	return service.convertToRest(relation, false), nil
}

func (service AssetPatchRelationService) BulkCreateAgentPatchRelation(relationRest rest.AssetPatchRelationRest) (rest.BulkActionResponse, error) {
	var successIds []int64
	failedIdMap := map[int64]string{}
	computerIds, err := NewAgentService().GetAllAssetIdsByScope(ConvertToAgentScopeFilter(relationRest.AgentScopeFilterRest))
	if err == nil {
		for _, computerId := range computerIds {
			relationRest.AssetId = computerId
			_, err := service.CreateAgentPatchRelation(relationRest)
			if err != nil {
				failedIdMap[computerId] = err.Error()
			} else {
				successIds = append(successIds, computerId)
			}
		}
	}
	return rest.BulkActionResponse{
		SuccessIds:  successIds,
		FailedIdMap: failedIdMap,
	}, nil
}

func (service AssetPatchRelationService) CreateAgentPatchRelation(relationRest rest.AssetPatchRelationRest) (int64, error) {
	relation := service.convertToModel(relationRest)
	existingRelation, err := service.Repository.GetByPatchIdAssetIdAndPatchState(relation.PatchId, relation.AssetId, int64(relation.PatchState))
	if err == nil && existingRelation.Id != 0 {
		return existingRelation.Id, err
	}
	id, err := service.Repository.Create(&relation)
	if err != nil {
		logger.ServiceLogger.Error("Error while creating asset patch relation")
		return 0, err
	}
	return id, nil
}

func (service AssetPatchRelationService) UpdateAgentPatchRelation(id int64, relationRest rest.AssetPatchRelationRest) (bool, error) {
	relation, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, err
	}
	_, updated := service.performPartialUpdate(&relation, relationRest)
	if !updated {
		return false, nil
	}
	_, err = service.Repository.Update(&relation)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (service AssetPatchRelationService) DeleteAgentPatchRelation(id int64) (bool, error) {
	_, err := service.Repository.PermanentDeleteById(id)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (service AssetPatchRelationService) GetAllAgentPatchView(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.ASSET_PATCH_VIEW.String(), true, "")
	var responsePage rest.ListResponseRest
	var relationPageList []model.AssetPatchRelation
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.ASSET_PATCH_VIEW.String(), false, "")
		relationPageList, err = service.Repository.GetAllAgentPatchView(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(relationPageList, true)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AssetPatchRelationService) GetAllAgentPatchRelation(filter rest.SearchFilter) ([]rest.AssetPatchRelationRest, error) {
	var assetPatchRelationRests []rest.AssetPatchRelationRest
	var assetPatchRelations []model.AssetPatchRelation
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.ASSET_PATCH_RELATION.String(), false, "")
	assetPatchRelations, err = service.Repository.GetAllAgentPatchRelation(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return assetPatchRelationRests, err
	}
	assetPatchRelationRests = service.convertListToRest(assetPatchRelations, false)
	return assetPatchRelationRests, nil
}

func (service AssetPatchRelationService) GetAllAgentPatchRelations(filter rest.SearchFilter) ([]model.AssetPatchRelation, error) {
	var relationPageList []model.AssetPatchRelation
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.ASSET_PATCH_RELATION.String(), false, "")
	relationPageList, err = service.Repository.GetAllAgentPatchRelation(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return relationPageList, err
	}
	return relationPageList, nil
}

func (service AssetPatchRelationService) convertToModel(relationRest rest.AssetPatchRelationRest) model.AssetPatchRelation {
	var patchState model.PatchState
	var exeType model.ExceptionType
	return model.AssetPatchRelation{
		BaseEntityModel: ConvertToBaseEntityModel(relationRest.BaseEntityRest),
		PatchId:         relationRest.PatchId,
		AssetId:         relationRest.AssetId,
		PatchState:      patchState.ToPatchState(relationRest.PatchState),
		IsOld:           relationRest.IsOld,
		IsDeclined:      relationRest.IsDeclined,
		ExceptionType:   exeType.ToExceptionType(relationRest.ExceptionType),
		ExceptionReason: relationRest.ExceptionReason,
	}
}

func (service AssetPatchRelationService) convertToRest(relation model.AssetPatchRelation, shouldAddEndpoint bool) rest.AssetPatchRelationRest {
	endpoint := map[string]interface{}{}
	patch := rest.PatchRest{}
	if shouldAddEndpoint {
		endpoint, _ = NewAgentService().GetAsset(strconv.FormatInt(relation.AssetId, 10))
	}
	if relation.PatchId != 0 {
		patch, _ = NewPatchService().GetPatch(relation.PatchId, false)
	}
	return rest.AssetPatchRelationRest{
		BaseEntityRest:  ConvertToBaseEntityRest(relation.BaseEntityModel),
		PatchId:         relation.PatchId,
		AssetId:         relation.AssetId,
		PatchState:      relation.PatchState.String(),
		IsOld:           relation.IsOld,
		IsDeclined:      relation.IsDeclined,
		ExceptionType:   relation.ExceptionType.String(),
		ExceptionReason: relation.ExceptionReason,
		Endpoint:        endpoint,
		Patch:           patch,
	}
}

func (service AssetPatchRelationService) convertListToRest(relations []model.AssetPatchRelation, shouldAddEndpoint bool) []rest.AssetPatchRelationRest {
	var relationRestList []rest.AssetPatchRelationRest
	if len(relations) != 0 {
		for _, relation := range relations {
			relationRest := service.convertToRest(relation, shouldAddEndpoint)
			relationRestList = append(relationRestList, relationRest)
		}
	}
	return relationRestList
}

func (service AssetPatchRelationService) performPartialUpdate(domainModel *model.AssetPatchRelation, restModel rest.AssetPatchRelationRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchState != "" && domainModel.PatchState.String() != restModel.PatchState {
		var state model.PatchState
		common.PrepareInDiffMap("patch_state", domainModel.PatchState, restModel.PatchState, &diffMap)
		domainModel.PatchState = state.ToPatchState(restModel.PatchState)
	}

	if domainModel.IsOld != restModel.IsOld {
		common.PrepareInDiffMap("is_old", domainModel.IsOld, restModel.IsOld, &diffMap)
		domainModel.IsOld = restModel.IsOld
	}

	if domainModel.IsDeclined != restModel.IsDeclined {
		common.PrepareInDiffMap("is_declined", domainModel.IsDeclined, restModel.IsDeclined, &diffMap)
		domainModel.IsDeclined = restModel.IsDeclined
	}

	if restModel.PatchMap["exceptionReason"] != nil && domainModel.ExceptionReason != restModel.ExceptionReason {
		common.PrepareInDiffMap("exception_reason", domainModel.ExceptionReason, restModel.ExceptionReason, &diffMap)
		domainModel.ExceptionReason = restModel.ExceptionReason
	}

	if restModel.PatchMap["exceptionType"] != nil {
		var exepType model.ExceptionType
		common.PrepareInDiffMap("exception_type", domainModel.ExceptionType, restModel.ExceptionType, &diffMap)
		domainModel.ExceptionType = exepType.ToExceptionType(restModel.ExceptionType)
	}

	return diffMap, len(diffMap) != 0
}
