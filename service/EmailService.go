package service

import (
	"bytes"
	"context"
	"deployment/common"
	"deployment/db"
	"deployment/logger"
	"fmt"
	"net/smtp"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
)

type EmailConfig struct {
	bun.BaseModel `bun:"tbl_mail_server"`
	Host          string `bun:"mail_server_host"`
	Port          int    `bun:"mail_server_port"`
	Protocol      string `bun:"mail_server_protocol"`
	SenderEmail   string `bun:"mail_server_email"`
	Password      string `bun:"password"`
}

func (service EmailConfig) GetMailConfig() (EmailConfig, error) {
	var result EmailConfig
	config, err := pgx.ParseConfig(db.MainServerDBConnString)
	if err != nil {
		panic(err)
	}
	config.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	sqlDB := stdlib.OpenDB(*config)
	newDB := bun.NewDB(sqlDB, pgdialect.New())
	err = newDB.NewSelect().Model(&result).Where("id = ?", 0).Scan(context.Background())
	if err != nil {
		return result, err
	}
	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newDB)
	return result, nil
}

func (service EmailConfig) SendMail(recipients []string, subject, body string) {
	config, err := service.GetMailConfig()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching mail config :", err.Error())
		return
	}
	auth := smtp.PlainAuth("", config.SenderEmail, common.Decrypt(config.Password), config.Host)

	var message bytes.Buffer
	message.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	message.WriteString("MIME-Version: 1.0\r\n")
	message.WriteString("Content-Type: multipart/related; boundary=boundary1\r\n\r\n")
	message.WriteString("--boundary1\r\n")
	message.WriteString("Content-Type: text/html; charset=UTF-8\r\n\r\n")
	message.WriteString(body + "\r\n\r\n")
	message.WriteString("--boundary1\r\n")
	message.WriteString("Content-Type: image/png\r\n")
	message.WriteString("Content-ID: <uniqueID>\r\n")
	message.WriteString("Content-Transfer-Encoding: base64\r\n\r\n")
	message.Write([]byte("iVBORw0KGgoAAAANSUhEUgAACpIAAAHwCAQAAAAFlqZkAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAD/h4/MvwAAAAlwSFlzAAAuIwAALiMBeKU/dgAAAAd0SU1FB+cMFhE5G+4S6ckAAAABb3JOVAHPoneaAABwvElEQVR42u3df6xc+Xnf9w9t2hYNGXeEOA1dp7mjrlEjhdk7GwToBqV9z0KtYtRZcNgssDaqlkNri0RxWg4VCKtFW3DoqJYWbr3DZIG2iCwOE7sy4wo7F3bj1omz59qMsPkh7Ln2H06BlTW3SGq6DbpzW0F0bEa3f/CSy0uee++ZmXPO8/1+n/dLsCythjOfczl35pznPN/vc2pfAAAAAAAAAODXt1gHAAAAAAAAAABLFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrFEkBAAAAAAAAuEaRFAAAAAAAAIBrp60DAKhJJqmnzsF/KjPT7ODf5yqs4wIAAAAAAtdVVz111FGv9H/PH/17obl1WJjrqHfwXjnqHfPg3fKgJpFbx33SqX3rBACW13v0r7WF/+yuZspVqNDM+jAAAAAAAMHoKFOmnjYX/HPbKg7+BU96ytRTd+H3i7SnQrlmysOoS1AkBWLUVV+ZsiVKo2V2lSsP5UMJAAAAAGCkp7762ljxWfaUK9eUa8zE9ZSpv0RptMyepvZ1CYqkQFx6Gqiv9Uaee0cTvsYAAAAAwKGOBhqsXB49bEcTTViGn6DmKhOm7xmKpEAsOhpo2FB59HE7GmvK1xgAAAAAONHVSJcae/ZbmoS3+ySW1FFfw5qL6U/b0tjiPRNXkTSzDoDW5dYBAtHsV9bT9jTRmJ7SJWTWAZLC1u8AEI6jxy8gXbl1gEh11bWOgIX5HmzbztXmtkZ8qkSvo6GGNW37d7JdjTRp9wDDLpL21DuYo7bMWBqkY1tSrrkKl0WTtguk77ulEYXSY3XUU08dZRKfUo3aPXgnPvgEePDvuXUoAEhc9ug7rp69xhCnPRWaq9BMM757j5Cp++hfza/6QpN2Dt7thaN3e0dDXWvt1bY05AozWu0WSB/a1VDT9l4uxCLpslPU4MOuCuXKndzp62hsVCB96LrGDgvTJ3kwuy/jNNjcrmaH/gW04Zz+gqRzWtfv69v1PZL29a8kfdA6WAD+QH+g05Kk/0f/nz6g/1e/Lul/1G9ZB8MCugdn4k0vo0Ocdg7OxGfWQQLw4HyQ35V07RwMHkpbX+OWr2n2NNLY+rCxhPbfK+/b1rCtClBYRdKu+rXNxULqHkw+S3vvzKFGAXQntnznJmgd9ZWpH8DfCsrtaK6cXhfU7kVl+mF9i87qW/Xt1mEi9U39vr6u9/R7+i39ZeswOEJPA2UUfFDJrnJNnZ4hcj7ozZamiV512jXkbGvArZaodDTRBeMM1zVq42XCKZIONKA8iiVsadr2LhWt6GoS0G/ElgZJnhgsoq+B+RcDFrGr4uBfM+soiNJ5/Yj+jP64/jV9l3WUBO3rG/q/9Af6e3SZBqKJecbwYE9TV8NYOuqrz/mgS3uaJrcRWU9Tw3Vxexo4vc0So0zTIG4L7ajf/G9hCEVSm30NkJL0xgz1NQnsd2JXfSdbHDytqwGfUVHbU36wOBA4ybr+ov6MNrSmU9ZR3NjXv9Q/1/9KudRMVyN64rCSXY01Sf52ek9DflPc29IomSuigcbm7+eWOgOxolGLe9aeZE/9pq/prIukFEhRn3TGDI11xTpCqctJ9uwez25sFprwYG+pPPkLOSzuvAbKtH6wnyZs3Ne/0Jc1oljaIr7lUJc9jRPex76vYUArvGArjWvOoV63jiCJ9YoxmAR3ntBwVcK2SBrC3QukJf4vrRB2+zjaLQ2sI7SIS8d0+diIH1Ws6y/qh/UDFEeDcl/v6W+zc2kLRrQqoFZpFkozTRjViUPiHzwUUtlrR1lynxrp6CgPciueRnuQ7YqkYe24iJTEPI891I+h93kpk9Ll7sOWck0jv7GCZb2kT+v7dcY6Bo7xnt6ir7QxtnvRIV3xl48e19OYK1aU2lY/2ivOkEqkku9t3cIWcm2iwaqEVZE0vB0XkZJdDaLcfTDkj6H3eSiT0jPgy66mmnBy5sh5/RX9OXpHo/EN/ROKFLULaX8xpGdbwwS+VTsaBboBFsKwpyzK93loJVIp3p9l6vKgz78aW3RvUyQNdcdFpOSGRpHd3YujRCqlXibllNgrSqUenNdf0Z+lezRK7+l/YgF+TcLe2AepuBp5Pym91jjZnobRTWwY6KZ1hFKUScMTYjn9sIbKpO0XSTsaB//DRhp2NIjoozaeEqmUcpm0p0lEfw+o366mGrMAP0mf1UBnrUNgJd/U1/Rp/c/WMSIX1/kGYhbzcmR6rVFVXINt+3rTOsKRKJOGJZTRXsdp6D3TdpGUEzO0Kaa7e9PIujpi7w8ox0YgeGBHY02jvbTDk87rrzOaKSH39Cv689YhokV3HNoU506D9FpjMfGUSbsqgr7SoUwajkxvWUeoZFe9+q/Y2i2SUiJF+xqdfFabGO9XPx/lvq/HCXX5CWzsaaoxp2rR+7z+vDrWIVC7ff0feoGRTgvraBb0BTLSE1PDwgNcr2JR8ZT2iuDf2/H8LNPWURHNDdUt9et+ynaLpOHvaoAUhb80POSFD0fbUzepTjv2SkaZHY0ju7zDQ+saM54pcXt6mcX3C6D4Axvx9NlJPeXcSMDC4rgqiqMphzJpCOJa43pR03qfsM0iKSUIWAm7TBpvX0cD923McAsHR9vTWOMITn7xvnXd0g/plHUMtOCevsBAp4rCnlKLlMVSJqVEimVtK7OOcIKe3rGOUBFlUmuxLLV/qPabFN/SWvQ+JVKYuRT0qVm8u2BeCLr4vAhKpDjOmq7pPU3UtQ6CSs7rXc20SYnUiTP6Cf1LvWEdIwIjSqQwczOKM0ZKpFjepobWEU4wsQ5Q2ZpyzrlNTawDLGit7u0V2+okjbdXDqkItZs0zqX2D8WxuOQk7EWKqm5ppJl1CBxjXVP1rEPAxB/ob9BReozY+kKQntC7SWPagw8h2lMv4HPEGCaVP25HWQLXmHGK7b3ywIfr/O1rq0ga164GSFOII5ziPyELtfhcXdxlarSPQmm4cpbYO/cH+o/Zo7RU/GcbSEHt+8bViB17sbpwNyKLsWGNMqmNGN8rUs01iXaW2/cpkSIA1wIs5w2jv2i5FPlyiF7gfQ0IzyV9jaX3Afqs/pAl9u59u35Rc71oHSNA8Z9tIAWTgDv9x5RIsbILwe5LOoyw7LWhsXUEl2J8r0g11yTa6SSdcWqGIIS2DXSsd2oOC/eu6cnoG8DyrjPMKRjreltnrUMgILvcxjikq69ZRwAkhdsbxsZLqEeY45viveK8EfxOr+mJt3JXYy9pG52ko2h/0EjNWmBdg+NIv7AOuxDxxeiIEimWdk0zTt2C8Fl9jRIpDlnXNxnl9JiRdQDgQJi9Yb0gUyFGm0EWSQfRXnFeCXAdaNoGEVfuLqlT11O10Uk6j/bXEikK545UvHf1nhTrvqSMscDqdjRUbh3CsXX9mp6xDoFA3dN/yg6loo8UoQlvZ9KCW+aoTYhXRfH2BkrS85xntyjXpnWEFVyt64ZX852k8d65QJquBLMfUqw7fjwtzn1JO4H1FSNOG3pLk/ruXGIhL+m3KZHiSGf0i/pN6xABGFkHAA4J7TtzSIkUNQrvqqgfdYlUmgb3E01XN+oSqeprhWu+SDpq/BWAxUysAxwYWgeo0cA6wBIYY4G6XGLhvYmhvqgz1iEQuHP6l84HOXV0yToCcMhaUFeH3aDSIAV96wBPGFgHWNGapoHdWEnXwDrAitbraoZrukiaUYZAcDaC+ABIq8d6YB1gYV1ds46AhKzpdeXc6W7V5/U6s+xRwbfrF7VtHcLQwDoA8JRwVnVJo6TOxxGCgXWAQzq6YB1hZWHuZZyigXWAUI6g6SJpTTGBWo0DuB/Vtw5Qq/XojmdkHQDJ2VRBP2lrPq+PW0dARH5I39A56xBGBtYBgBJj6wAHunRao3YbQd0071sHqMUlzrBb0E2gvbFfz9M0XSStKSZQqzXzD9oU7uod1rcOsBBOitGEB/2kHesYDlAixaLOaMflvPseuy0iSKHMAB9bB0CS+tYBAs2yitcD+cxIWd86QA3W67lF0WyRtM8CBgRqaPz6fesfgPMjGlkHQLI2NYvstyE+lEixjFP6CYdjnDLrAMARRtYBJHWTa1lAGDLrAI+k1JbDAKemZdYBatGv40maLZJmLfwYgGWsGS9A61v/AGq3FtExMcYCTVrTm/SmNOgl/bh1BETrnLtl9wPrAMARNgMoeIysAyBRmXWAAJOsjgFOTcusA4RzFBRJ4dXQ8LVTuqv3vsw6QGVD6wBI3hUVAVz+pWhdX2RcE1ZwRu/oFesQremw2B4BGxm/PrfM0ZS1YM4BM+sAtWKAU5N6iawBz+p4kiaLpJyaIWQbhpM1M+uDb0TfOkBlA+sAcGBDRaK/6bZ+mxIpVvSt+pyb3Ukz6wDAMfrGXWED6x8AEtazDnAgsw5QMwY4NSezDlCTWm5RNFkkzVr6QQDLGZi9cmZ96I2oaaPkxvUTmNyHGKzpLU7lavaOzlhHQBJ+QtvWEVrRsw4AHMN6o6ah9Q8ACetZB5CUZssaA5ya0rMOENKRNFkkrSEe0KCB2Stn1ofekJ51gEr61gHgyOuasH9SbYaRfMYgBj+kuXWEFmTWAYBj9Q1fu8ctczSoZx0goBR1Y4BTM7rWAWrTW/0pKJLCrzWzy4f07uo9kFkHqKRvHQCuXFJOmbQW6/pp6whIypqDMmnPOgBwrAuG348D64NH0jrWASTFcmW2KAY4NWPTOkBteqs/RZNF0m5LPwZgWX2TV82sD7sxPesAFfQT2ZQa8dhQEcXvRuhu6bR1BCRmTb+f9Kz7Dt93CF5m9sp960NH0sIoOPWsAzSEAU7161oHCOtYmiySptoth3RkJq/asz7sxoRxQnC8zDoAHFpXnvDvfTvOR/H5gth8h95JuEzasw4AnKhv9LostocHXesAjWGAU9261gFqVEMVsrkiabe9nwOwpA2TZv2e9WE3qGsd4ESZdQC4tKacrpWV/KJ1ACTqWxMuk3asAwAnypy9LvzoWgdQ2i1rDHCqV9c6QK06qz4BRVL4lhm8Ztf6oB0fWzfp0wWEbE1vsgPa0oY6ax0ByfpW/UPrCA3pWQcATrRuVMzvWx84kte1DhBAgmYxwKlOXesAteqt+gTNFUk7Lf4YgGX1nLxmWzLrACfoWQeAazcpky7pmnUAJO2M3rWOALjVM3lVNnBB+rrWARrGACc0prkiac/60IAKMoPXZJCCncw6AJyjTLqMlzgJRsOeSbJM2rMOAFSQGbxmz/qggRZ0rAM0jgFO9cmsA9Squ+oTNDm4CQhft/VXzKwP2fXR9awDwD3KpIv7b6wDwIFn9I51hNp1rAMAFfQMXjOzPmigBT3rAC1ggBPKdFd9Aoqk8I3Zlr6wvAr2KJMuZl3PWEeACz193joC4FDH4DV71gcNoCYMcEIDKJLCu17ir8fRva9rHQCQRJl0MWPrAHDj43SkAK2zuH3dtT5ooAVd6wAtYYATasfgJnjXSfz12hX2fqtd6wDAgZuB31AIyZ+1DgBHfkYvWUcA0DjWFcGDrnWAljDAqQ5d6wBhYXATvOtYB0BretYBgEdy3o+VnNcZ6whw5JRusg0P0LJu4q8HoFkMcFpdWuc+nVWfgOX28K7X8ut1rQ/Y8fF1rAMAj6wp5x1ZwV+xDgBnzugr1hEAZ7qJvx6ApjHACY/rrfoEFEmBdnWtAzg+vsw6APAYyqRVPGcdAO78Ef2udQQADepaBwBQOwY4oUbNFUlz60MDACBgG5pYRwjeWesAcOistq0jAGhM1zoAgAYwwAm1oZMUgBdd6wDAEy6wi9KxhtYB4NS/bh0AQGM61gHgwsw6gDsMcEJtKJIC8CKtLamRhisaWEcIGBOIYeGe/n3rCAAa07MOABdm1gEcYoATakKRFAAAO2Mu2I50zjoAXLqsXesIAABgQZc0so6AFFAkBQDAzpomLA86wh+xDgCHtnXbOkJNZtYBAMApdra2ck196wiIH4ObAACwxPKgo3SsA8CdeULzcWfWAYAg9awDwIG5dQDHJvyOY1V0kgLwIbMOABzpEjuTlmAXYbRtXy9YRwDQsDXrAHCgsA7gGCu0sLLmiqSF9aEBleTWAQBAY3WtIwTnonUAuPOa7lhHqFFuHQCopLAOANSusA7g2oYm1hEQt+aKpHPrQwMAIBJrnNABxu7qVesIgENz6wBA7QrrAM5dYIATVtHkcns2LEYM5tYBAEDSJid0T/hj1gHgyn09Zx2hZrl1AABwaY89oc0xwAkraLJIOrM+OKCCwjoAAEiSrjnaar5XYZfgP2MdEq58SrvWEWqX3hEhPTTVID25dQCIAU5YAUVS+MYFBIBwjK0DtGZiHQA4ZDvJ377COgBworl1AKB2uXUAiAFOWEGTRdLc+uCAE82sAwDAI5saWkdoxVAb1hGAx9yr0Nkco8I6AHCiwjoAULupdQBIYoATlkaRFL4V1gEA4DEjB3e9O+y+iqDs67J1hIbk1gGAE82sAwA12+VdHQwGOGEpTRZJ2WUG4ZtZBwCAx6wluej3sLHWKj3uPeugcGJLt60jNCS3DgCcqLAOANRsah0Aj2GAE5bQbJE0tz484ASFdQAAOORSogt/H+rpUsVHcqMVbZjronWEBm1ZBwBOUFgHAGo2tg6AQxjghIU1WySdWh8ecILcOgAAPGFsHYCjgxv7iV885dYBgGNxMwyp2WadYmAY4ISFNVskLZgdjqDtWAcAgKdsaGAdoTGZNg/+U/fEx/6udVg48FrlM9V+lAMgptYBgGMV1gGAmk2sA+ApDHDCgpotknJyhrDl1gEAoES645smj/5T98THprpPJMJR6NWKj+xqEuVWGDM69RC0wjoAUKtdynFBYoATFtJ0kXRsfYDAMXLrAABQYl1D6wiNGGh9ocfvWwdG0u4vMM5hojUpyousiXUA4Bi5dQCgViPrADgCA5ywgKaLpNzBRshy6wAAUGqYZC/p6LH/nFV4/J51YCTtY5WX2o8OtonYjLCXdMrvEYK1y+6NSAp9pCFjgBMqa7pIyh1shGtHc+sIAFBqLcFe0sN9pJ0Kf2JmHRkJ2668oUNP1x7955F17IXNORNHsKbWAYBaDa0D4BgMcEJlbRRJGd6EMOXWAQDgSOn1ko4O/beNCn/iK9aRkax55Z7QzqEiY4y9pGPrAMARcusAQI22KfsHjgFOqKj5ImmMd93hw8Q6AAAcKbVe0qf3I+2d+Gcm1qGRqH29UPmxoycK+iPr8Aub6ZZ1BKDU1DoAUJs9Dawj4EQMcEIlbRRJJ9qxPkzgKXtM1AQQtLR6SYdP/ZPuiX/mju5bx0aSvqA7FR/Z15Un/kmMvaRD9iVFgLasAwA1GrBFUBQY4IQK2iiSsj8HQjS1DgAAx1pLqCshK1le36vw59iwB/W7q5crPrJT2s08sj6Ahc1Zco8ATa0DALW5xfs5GgxwwonaKZLmLPRBcCbWAQDgBEPrAI0eSVbhz/2idXAkZ1/PVX7sVGsl/3SzQhd0aEas6kJwptYBgJpsJ3RTO30McMKJ2imSstAHodlls3gAwVtP5LS7qwsl/3Szwp98VfvW4ZGYT1buTx4e+R4dWR/EEgbWAYBDtjS3jgDUYocF3JFhgBNO0FaRdM6HB4IytQ4AABUMrAPUYnTEP88q/NnfsQ6PpBSVF573jimFXoqwC6XQdesIwGMm1gGAWuwoo+AfHQY44VhtFUmlnJMzBGRsHQAAKthMYOekzpG3SfsV/vQb1vGRkHt6tvJjJ6VL7R8aWB/KEkYMykEw9mhXQBIokcaKAU44RntFUmnEzqQIxDbzBwFEYmgdYGX9I4tNWYU/PWbCPWpzufIjxyWjxh43tD6UpQzYmRSBGFsHAGpwSz1KpNFigBOO1GaRlJMzhGJiHQAAKupHuLD3sOGR/8tGpQE43GBFPaa6XfGRma6c8Ij1KHtQ5sqYEYAgTKwDACva0+Uo1xTgIQY44UjtFkmljDIpzO1yagYgGmtRFmPe1zu2I6/Ksb3M8CbUYK6LFR/ZqXSWMLA+oCV/CpRJYW+LFV2I3LZ6XE9GjwFOOELbRdK5Mm1bHzScm1gHAIAFDK0DNJh+UOk5ft36IBC9fb1Q+bETrVd41IVIO1AK9WhYgLGxdQBgBbu6qIxCfxIY4IRSbRdJH5RJWToHO3ucmgGISrVF6aHq13Bsl9iXFCt6TXcqPnKgC5UfGacZ67pgalu5dQRgSbu6rC5jxxLCACeUaL9IKkmDBbbOB+o1ZoNtAJEZWAdYWv/YCeFStT7ZXW6uYiVf1asVH9ld4EbqwPqwljZXTzesQ8CtiXUAYCnbuqwu79/kMMAJT7EpkkoTPctdbBigjxRAfAbWARpM3q/0PC9zewtLu6+PVH7s5MSy/vvi7vEe6iK7k8IAkwEQnx1d1YeV8d5NEgOc8BSrIumDPZGuWx8+3KGPFEB81iO9y92psHB5vWIJ+C9aHwyi9SntVnzkSJsLPXPf+tBWMlVXW9Yh4M7IOgBQ2Y5u6bI+rJ7G7EGaMAY44QmnjEfGdjWuvPcTsKo9dc2LpPmCl2CxeT7YnaYyvWUdoWGh3nbqHCrwpf3+b86NKMc3DXSzwqO2lVV6ttQ/PdGMqu8vqad3FnzunUhvXzwuW7g0DCwvhN8Z44tfBG1PhaRccxXBXtFUxVnTIq47v4GT1udi9TO/I5w2PoCZ+so0pFCKVtBHipSNrAMsoKeOuuqqo566lSZJe9ePskjar/SoTWWVLkUyfUNnrA8JkblX+US5s8Qojg11o+8uypWpryEX02jF0DpAsq6qsI4QuYKrRNeuqWAgFx6yLpJKUq5cXY0qjHcAVsF+pEAoiif+e6aueupxmX6k9QiLMVUW2z8wqljIuqwv6pT1Ya3svr5uHWEJH9AHrCMsYV8frfzY8VK3a/pJnFlMNVWmgS5ZB0HimGvfnPj7HgFbE2XcasADIRRJJWmmgaSB+vSUojFD7hACgcof/aeeMmXKuGn2lPiKMf3Kj6zaS3pb/4E+bn1YC7mvr2umPf2mfkd5IiffmaQf1ln19N36XuswJ9jSnYqP7C9ZIIzv9/IouXINNdBAG9ZRkKyhdQAAOMKaJsqoFkCy35O0TF+ZMk7RULMQdkGS0t8fhj1J7cTfYfe4ngZ8Exyy8v46rZssUHaqfnTvBPJZfpz72tVvaTuZ8tlxuvqoxoFug3BX31PxkR3Nlr4x86HkLqk6B2fibISCet2qOKavaQFe/NYg3PNvWEn9mrMJW5EPZFxeWp+L0e9JWmaqqR6M+sjUU4dfb9RiaB0AwAIKDSV11deQS3VJ0qY6kRVj+gsdXbVeUulZvatnrA/tCPv6HeWaVO5eTMFMHwp0If6+nqv82OkKvev95KbizjXRRDrYBCVTh9tVqMEeZ+IAAndBo6hmPKAhIRZJH5grf3TB9GA2ck+dx/4bVuFvUMoW91eBCM001lg9Ddm1WlIW1Zbyi26ZMFG34iO/T+8dnA+E45v6TX1Ot61jmPhMoF3sr2m34iNXG1qUJVckfWim2aPPnK660qPOjG7l31YcpefuO20U2W0+AB4xwAkKuUj6uLlyiSJXbTrKnRVJ9wJZ4ANgGYUG6miogbNPrif1ozptyxZ8/PoCd+97+u2AFnj/nv62/gvrEGbeDfRMstCrFR/Z0+srvVLf+lBbMdNMnInXp+fuZ7njYvsRAPFjgBP0LdYBYGDibuEUd6+B2M01UleXK/eGpSizDrCQ/sJ/Yli5O21Xf1J3rQ9QkrSjH9RZxyXSNwLd+uDeAu+/yYqvtcb6Jiyos9IGD3EaWAcAgErWNAluvRJaRpHUn5EuWEdomY/hGYAHE3V1VXvWMYysR7TEdZldDNcWKFft6nv0VdMj3Ne2uuq52n/0Sef0CesIR6h+O2Vcw23jzPpwEZmpu1UR1+nLAhCNjWS30UFFFEm96euadYSWsdQeSMtYXd2wDmEksw7QcNLNhQZ7fJ+2zY5vRx9W5rqvWZK2Az2L3K68O2ymKzW8XmZ9wIjK2N1I2h0GoQCIygU+tXwL8/QWTek5vC8y0sw6AoBazTXU89qxjmGgZx2gsmzJPzdaqFs20+e03/qxzfWD6rkvkEpf0oesI5SaV373dWra5bfq6wFSv5bCfFwG1gEAYEHXnOw4jlIUST3paOJuDySW2gNpytVz2E+aWQdoPOnagmWrV/VDutfice3rc/qQ6yX2D53Tf2QdodS+Xqj82LrOidiVFFV5bFZgqT2AGE34bveLIqkn/gY27XEPCEjYUM872580ns/w5ZNuLHhr646+s7Vl94U+XHlmeur+sXWAI3yhcgl7UOMO7T3rw0YUPDYrsNQeQJwY4OQYRVI//A1skgZMtQeSlqvnbNl9Zh2ghZRXFl6cmekHG/+0v6erepZF9gd+U99hHaHUXb1c8ZHdWteZZNYHjijQrAAA8WCAk1sUSb3wN7BJulHTbmMAwjVTpi3rEC3KrAO0kvLmwp15d/QhfU73Gzqe+/pZfSebtzzyis5ZRyh1X89Vfuy01o6+nvWhIwIemxWGzAUAEDEGODlFkdQHj3sg7Sw0JRlArObq65Z1iNb0rAO0lDJfaIDTA6/q2/SztRdK72tb31e5P9GHz1gHOMKnKnf6jmru6PPWH4jFeWxWuOXw6gNAWhjg5BJFUg887oHEAh/Ak4GbMmnPOkBLKdc0XWonqJf1bfpcbUvv7+ln9W3KWGR/yO/qtHWEUkXlXt9eA+WqzPrwETSaFQAgTgxwcogiqQf+9kCSBizwAVzxUiZdtw5QQaeWlBvKl9ww/1V9SD+oYqWe0n0V+lF9Jx2kT3lDZ60jlLqnZys+stPIVjw96x8AAua1WWFuHQIAVsYAJ4cokqZv6HAPpOvsRgq4M3AywimzDnCiXk3Ps3yZVLqjZ/Vt+lFtL3yZfk+FflTfomd1u6kfUMTO6RPWEUrt63Llx44budXQs/4RIGA0KwBAvBjg5A5F0tRlet06Quu22GIZcMnHsuiudYATZbU90yplUkm6rUwf0ild1VRfPbZcOtddbeuquvpOyqPHeDvQ88atyn9nfV1qJEHX+keAYHkc2ESzAoCUMMDJmTD3lUJdug5PUnY0sI4AwMRcfeXJL2rsWgdoNeGGcmUrL9ocP7Zb5X+of+vQ//a7lEQr+5K+0zpCqbkuVnxkt7FukE3rHwIC5XFgE80KAFJzTYXDuopbFElT1tE0+XLBk/Y0YA8kwK1Co+S75zPrACfq1vpsG8o1UFHb8/0d/Z12fxzJeLFyKbJd+wssdW9yZ8guy4vxFJ8DmwbWEQCgdhNlNZ6NImhhLptCPcYu90AqrCMAMDTWtnWEhnWsA5yo7p66DeXs+BiAn9Mp6wilXqu8zcaw0X7PrvUPAsHxObCJZgUAKWKAkyMUSdM1bGjfrZCxBxKAgfasIzQq9Jtf3Qaec03v0Jtk7Cv6DusIpb6qVys+stdwl3lm/aNAcHwObCqsIwBAIxjg5AZF0lQxsAmAT7PH9p9MU9c6gEm6m9zBN/SK/pR1hFL39ZGKj+w0fmnTsf1RIDgMbAKAtDDAyQmKpGliYBMAv0aJT7nvWgc4Vq+xZ77Esnszn7EOcISPVf5dHzXe09cz/lkgLBkDmwAgOdfUt46A5lEkTREDmwD4NrIO0KiedYBjdRp87g3lGlofoEO/G+iYz23drvjITFcaT9M1/VkgLDQrAECaJoGfh6MGFElT5HFg05A9kAA8Mkm6l7RjHeBYvUaffU2vK6cc1ao3dNY6Qql7lXcB7bRSsFq3+1EgMDQrAECqGODkAEXS9Hgc2HSDbZQBHDKyDtCgrnWAY3Uaf4VNfU0jTlBbck6fsI5Qal8frfzYtiaMd4x+FgiNx2YFBjYB8IIBTsmjSJoajwObtll+CeAJk4Rn3HetAwSQ7poKlna24u1AzxS/oDsVHzlsbXxOz+hngbB4bFZgYBMATxjglLgwT32xLI97IO2yfTKAEhPrAE61teh4XTdVVF5wjeVs6zutI5S6q5crPrLLhQxa5bFZgYFNALxhgFPSKJKmxOceSH32QAJQYmwdoDGb1gGCsaG3lHOa2pgX9YPWEUrt67nKj23zvCgz+FkgLB6bFRjYBMAjBjgljCJpSjzugcTAJgDlZtqxjuBQ1vorbupNzTRgP8gG/JxOWUco9cnKg9lGDs+LYMdnswIDmwB4xACnhFEkTYfHPZAY2ATgaBPrAGjJum5qxj39mr2r77COUKqo3CWe6VqryTpt/ygQGI/NCgxsAuAVA5ySRZE0FT2HeyAxsAnAcXLrAI3JrAMEaE2X9I5mGlMqrcUresY6Qql7lbdX6LR+8dJr+fUQFp/NClPrCABghgFOiaJImoZOwsWAozCwCcDxisqLclGXzPj113VF72imiQbqWv8wInZOn7GOcITLlX+rJ60NEQN8DmyiWQGAdwxwShJF0jTkDvdAYmATgJPk1gFgYl2XdFNf00wTDZWxDHphv6rT1hFKTXW74iP7umAdFo54HNhEswIAMMApSWGeBGMxE4d7IDGwCcDJCofLH/G+dV169A7Y1sOiefHYLbaeOpJm7Cp1yBs6ax2h1FwXKz6ya/I32jV4TYTA58AmmhUA4MEAp4zPw7RQJI3fwGERgIFNAKrIrQM0JEv2yJqzefB/Za5ahwvKOf2EdYRS+3qh8mMnJiUrlvd75XFgE80KAPDAhiZ01qeF5fax6+mmdYTWsQcSgGoK6wDuZNYBljKxDhCUL1sHOMJrulPxkaMjy+FA/XwObJpYRwCAYDDAKTEUSePGwCYAOM6OdQAEb4tFUo/Z1getI5S6q1crPrKna9Zh4QgDmwAADHBKCkXSuDGwCQCOM7MOgOBNrAME5EX9oHWEUvf1XMVHdvj7RIs6DGwCAIgBTkmhSBozBjYBwPEK6wAI3tQ6QED+lk5ZRyj1Ke1WfOTI4ZkR7NCsAACQHgxw6liHQD0oksbL48CmW3SIAFjI3DoAArdlHSAg7+oD1hFKbWtc8ZF9XTFN2jF9dbSNZgUAwEMbVCpSQZE0Vh4HNu1oYB0BQGQK6wAI3NQ6QDBe0TPWEUrdqzwOzH6pfc/49dEmj80KDGwCgKMxwCkRFEnj5HFg016kU5MBAOHKrQME4pw+Yx2h1L4uV37sxN3SZ9jx2KzAwCYAOB4DnJJAkTRO/vZAkjKWzQJY2Mw6QCN61gGSsZvoO2Rxv6LT1hFKbel2xUcOdcE6LNzw2KzAwCYAOBkDnBJAkTRGHvdAusyiWQBLmFkHaETHOkAycusAgXhD32sdodRdXaz4yB5L3NAif80KDGwCgCoY4JQAiqTx8bgHEgObAAD1K6wDBOGcfsI6Qql9PVf5sSy1R3s8NiswsAkAqmGAU/QoksbG4x5IDGwCADShsA4QhC9bBzjCJ7Vb8ZFjh0UrWKFZAQBwHAY4RY4iaVw6DufwMrAJAA7LrQMkI7cOEIDf1AetI5QqNK74yExXrMPCDZoVAAAnYYBT1CiSxmWqdesIrWNgEwCgCTvWAQLwon7AOkKpe3q24iM79LihNR4HNtGsAACLY4BTxCiSxmSsTesIrWNgEwCgGXPrAAH4ok5ZRyh1ufIjJw5vH8OKv4FNNCsAwDIY4BQxiqTxGDhcTsYeSACApuTWAcy9q9PWEUpNdbviIwe6YB0Wbngc2ESzAgAshwFO0aJIGote5b250sEeSACA5sytAxh7Rc9YRyg118WKj+w6PDeCFQY2AQAWwQCnSFEkjUNHU3cLfNgDCQDQpMI6gKlz+inrCKX2F9jFa+Lu3AhWGNgEAFjUNT5HY0SRNA4MbAKAZWTWAYBA/Uqg54CvabfiI0cOd2qHDQY2AQCWMWaAU3zCPEHGYR4HNl113uEDAEebWwdIRGEdwNCX9L3WEUp9Va9WfGRP16zDwg1/67loVgCAOqxpygCn2FAkDZ/PgU1j6wgAktCxDtCIwjpAIubWAcycq7zrZ7vu6yMVH9nR1Dos3PDYrMDAJgCoxzpnLLGhSBo6nwObhtYRACSiZx0ACNCXdco6QqmPLbDU3t82RLDhs1lhYh0BAJKx6bCiEzWKpGHzObCp77i7BwCAZn1FH7SOUGpbtys+su+wbAUbPpsVBtYRACApV/hcjQlF0rB5HNjU18w6AoBkZNYBGjGzDpCEbesARl7Us9YRSt2r/NvaocsNLfHZrJBZRwCA5DDAKSIUSUPmcQ+kqw7nhwJoTtc6QCNm1gEQsS8GudR+Xx+t/Fh/ZStY8diswMAmAKgfA5wiQpE0XD73QBpbRwCQFH8XuMBx3tVp6wiltnSn4iOHDm8gw4bHZgUGNgFAMxjgFA2KpKHyuQfS0DoCgKRk1gEaUXW0DfCkV/SMdYRSd3Wx4iN7GlmHhRM+mxUm1hEAIFkMcIoERdIwdTRxt5iMgU0A6tazDtCImXUAROqcfso6Qql9PVf5sf7OjmCDZgUAQN0Y4BQFiqRhmmjDOkLrGNgEoG6ZdQAgINuBnvV9snJ39Njh2REs+BzYRLMCADSNAU4RCPN02buRLlhHaB0DmwDUL7MO0IjcOgCi9CV9yDpCqaJyx17mcPkzbHgc2ESzAgA0jwFOEaBIGp6+rllHaB0DmwDUr+euEwg4yrnKu3626576FR/ZYeABWuJxYBPNCgDQDgY4BY8iaWh6DrdMZw8kAE3oWwdoSG4dABH6xzplHaHU5cpL7dmNFO3wObBpbB0BANxggFPgKJKGhYFNAFCXvnUAIBBf0XdYRyi1rdsVHzlwuBERLDCwCQDQNAY4BY0iaVgY2AQA9egm+3maWwdAZF7Rs9YRSs0r7xrcdVi4ggUGNgEA2sAAp4BRJA0JA5sAoC5D6wAN2bMOgOh8Jsil9vt6ofJj/RWuYGPCwCYAQAsY4BQwiqTh8DiwaYveEAAN6VsHaEhhHQCReVenrSOUek13Kj5ylGxXOMJCswIAoC0McAoWRdJQ+BzYNLCOACBRg2T7gQrrAIjKG3rGOkKpu3q14iN7Dm8hw4LHZgUGNgGAHQY4BYoiaRh8DmwasAcSgIYMrAM0ZmYdABE5p09YRyh1X89VfGSHPgu0wmezwtA6AgC4xgCnIFEkDYPHgU0D+qEANCTTpnWExhTWARCRvx/oed6ntFvxken2hCMkPpsVGNgEANYY4BSgME+evfG4B9J1ekMANGZkHaBBhXUARONL+m7rCKW2F1he1rEOCxc8NiswsAkA7DHAKUAUSe153ANpK+kSBgBbg4T7SHfp/EFF53TROkKpe8qsIwCHeGxWYGATAISBAU7BoUhqzeceSAPrCACS1Un6JkxhHQDR+Ec6ZR2hxL4uW0cADvHYrMDAJgAIBwOcAkOR1JbPPZAY2ASgOaOk9zAsrAMgEl/RB6wjlNrSbesIwGN8NisMrSMAAB7DAKegnLYO4NzY4R5IDGwC0JxMV6wjNCq3DoAovKJnrSOUmge6BQC8olkBqRiwkUllheaacz2KwIxV8K4MBUVSS0Ndso7QOgY2AWhOJ/lPmMI6AKLwmUCX2vesIwCHeBzYRLNCmvxdU65uVzPlB/8HWFvTVD1uYYWBIqmdTK9bR2gdA5sANGmaeE/QDidPqOB3Az27e0271hGAx3gc2ESzAvDQutYPBn3uKleuKedYMLWuKR3hYWBPUitdhycpDGwC0KRJwlPtH8itAyACb+isdYRShV61jgA8xuPAJpoVgDLruqSbek9TrlVhigFOgaBIaqOTfL/T09gDCUCTBg4Wm+XWARC8c/qEdYRS99W3jgA8xufApoF1BCBoF3RTc43UsQ4CtxjgFASKpDYY2AQAdRropnWEFuTWARC8Lwd6ZvcxltojIAxsAlBuTdf0nsYUSmFkzP7t9sI8lU4dA5sAoE4+SqTsSIqTfEkftI5Qalu3rSMAj2FgE4DjXNGMzSlgYk1TSvTWKJK2z+PApm2+ZgA0ZuyiREofKU7yoi5aRyg1ZxABgjJkYBOAE6zpmgp6+mBgnc9raxRJ2+ZxYNMuO5EBaEhHE12xDtGS3DoAAve3dMo6Qol9vWAdAXiMx2YFBjYBy9jQOxpah4BDDHAyRpG0XT4HNvVZIgqgET3ljrYvmVoHQNC+og9YRyj1Bd2xjgA84rFZgYFNwPJe14TFz2gdA5xMUSRtl8eBTUP2QALQiKFyR5+pW9YBELRX9KesI5S6q5etIwCP+GxWYGATsIpLyimTonUMcDJEkbRNHgc23dDEOgKABHWV63VXF7tT6wAI2mesA5Ta13PWEYDHeGxWYGATsKoNyqRoHQOcDFEkbY/HPZC22ccFQO06GqnQpnWMluXWARCwf6bT1hFKfVK71hGARzw2KzCwCagDZVK0jwFOZiiStsXjHkgMbAJQv4EKXXPVQypJO5pZR0Cw3tD3WkcoVTB4AAHx2KzAwCagLhvcrEbrGOBkhCJpO3zugcTAJgD1Gmimm1q3jmFgYh0AwTqnT1hHKHVPz1pHAB7x2awwsI4AJGSDczG0jgFOJiiStsPjHkgMbAJQn46GbgukEjuS4mhfDvRc7rJ1AOARmhUArO4SqyQN7VkHMMIAJwNhnlinZuBwDyQGNgGoS08TzfS62wKptM1iexzh5/VB6wilprptHQF4hGYFAHWYqGsdwa2B013OGeBkgCJp83q6aR2hdQxsAlCHnsaa6R1dctcDdNjEOgAC9aJ+zDpCqbkuWkcAHvE4sIlmBaAJa+wRaWauvtNuUgY4tY4iadM6Djd5ZmATgFX1D8qjVxz3j75vah0AgfpbOmUdocS+XrCOADzicWATzQpAUy4os47gVuH2k40BTi07bR0gebm7/if2QAKwrI56ypRp0zpIUG7xmYpS7+oD1hFKvaY71hGAAz4HNvWtIwAJY8m9nYl6umIdwsQVFawPaA9F0mZN2AMJAE7QVVeZeurRNVpqYh0AQXpFz1hHKPVVvWodATjAwCYAdVvXgDMzM0P1nLZSjFVQZWkLRdImMbAJAJ7UVffg3zvqqePwVtJidh1u2oKTndNftY5Q6r4+Yh0BeISBTQDqN+J611BfhcumijVN1eMWWDsokjaHgU0A2pRbBzhB1+UpzerG1gEQpF/Rt1lHKPUpp9NnESKaFQA0YV1Z8Gfd6Zqr73BDQ+nBAKfMOoQPFEmb4nFg0x57IAGGfC4+Sd/EOgAC9Ia+1zpCqW2K+ggGzQoAmjJweKUfjkJDh5/v0oMBTkPrEB4w3b4pHu9vZDSAA0CtGNqEp53TX7KOUOoeHQ4IhsdmBQY2AW25pI51BNcmumEdwcgVDawjeECRtBkeBzZdZg8kAKjZyDoAAvRlnbKOUGJfH7WOADzir1mBgU1Am/rWAZwbats6gpGxetYR0keRtAke90C6xZJQAKjZtmbWERCcv6sPWkcotaU71hGAAx6bFRjYBLSpbx3Avb72rCOYWNOUPuamUSStn8c9kHZo/AaA2o2sAyA4LwY6Pf6uLlpHAA54bFZgYBPQrgvWAdybu93iZ11T6wipo0haN497IO25/YgCgOZsO/w+wUn+ZqBL7Z+zjgAc8NiswMAmoH2ZdQD3Cl22jmBkkzGZzaJIWjd/eyAxsAkAmjCyDoDgvKsz1hFKvaZd6wiAJJ/NCgxsAixk1gGgiW5ZRzDCAKdGUSSt19jhHkgMbAKA+tFHiie9omesI5Qq9Kp1BOCAx2YFBjYBFjLrAJA00I51BCMMcGoQRdI6DXTFOkLrGNgEAE0YWQdAYM7pJ60jlLpHFxuC4XFgE80KgI2edQBIkjIGOKFuFEnr03O4NwQDmwCgCfSR4km/om+3jlDqMkvtEQiPA5toVgCsrKlrHQFigBMaQJG0Lh1N3S3wYWATADRjZB0AgXlD32sdodS2bltHACT5HNhEswJgqWsdAJIY4ITaUSSty1Tr1hFax8AmAGgCfaQ47Jz+knWEUn77NxAajwObaFYAbPWsA+AAA5xQK4qk9Rhr0zpC69gDCQCaMbAOgMD8A52yjlBiXy9YRwAOeBzYRLMCYKtjHQCPMMAJNaJIWgcGNgEA6nJDM+sICMpX9F3WEUp9QXesIwCSGNgEwELPOgAewwAn1IYi6eoY2AQAqMse+5HikBf1rHWEUnf1snUEQBIDmwDY6FgHwGP8bgDEAKfaUSRdlc+BTX3rCACQqBELKHHIF4Ncan9fz1lHACTRrAAAkBjghNpQJF2Vx4FNfZaCAkAjtjnNwSHv6rR1hFKf0q51BEBemxUy6wgAxHT78DDACbWgSLoajwObrjqcHwoA7RhaB0BQXtEz1hFKFRTzEQiPzQoMbALC4O/TJ3wMcEINKJKuwufAprF1BABI1HUGceAx5/RT1hFK3Qt0l1T447FZgYFNAHA0BjhhZRRJl+dzD6ShdQQASNSuw28VHOeXgzxL23e75xdC47NZYWIdAQACxgAnrCzE0+84+NwDqc8CHwBoyIBPWDzmS/oT1hFKbem2dQRAXpsVBtYRACBwDHDCiiiSLsvjHkgMbAKAplxnv2c85pwuWkcoNQ80F7zx2ayQWUcAgAgwwAkroUi6HI97IDGwCQCasqORdQQE5R/olHWEEvuMBUAgfDYrzK0jAEAUGOCEFVAkXUbf5R5IY+sIAJCoPe774pCv6LusI5R6TbvWEQDRrAAAOB4DnLA0iqSL6zncMp2BTQDQnCHTivGYFwOdHv9VvWodAZDXgU1j6wgAnuCzCBcLBjhhaRRJF9XRxOEeSCzwAYCmMK0Yh30xyKX29/UR6wiAvA5sGlpHAPCUwjoAjsUAJyyJIumiJtqwjtA6BjYBQFO4+MVh/1SnrSOU+hhL7REAnwObaFYAgMUxwAlLoUi6mJEuWEdoHXsgAUBT9jTg4hePeUXfbx2h1LZuW0cA5HVg08w6AgBEiQFOWAJF0kX0dc06QuvYAwkAmjNgsRYec04/ZR2h1D23O3shLAxsAhCO3DoAKmCAExZGkbQ6BjYBAOp0na3VccgvB3letq+PWkcAxMAmAMCiGOCEhYV4Mh4mBjYBAOp0SyPrCAjKl/QnrCOU2tId6wgAA5sABCa3DoBKGOCEBVEkrcrjwKYBeyABQEN22FIdh5zTResIpe4Gmgu+0KwAIDRz6wCoiAFOWAhF0mo8DmxiGSgANGXH7eIfHOUf6ZR1hBL7es46AiCfzQoMbALCVlgHQGUMcMICKJJW4XFg0xbLQAGgIXvK6D/AIV/RB6wjlPqkdq0jAC6bFRjYBIRt2zoAFsIAJ1RGkfRkPgc2DawjAECiKJHiSa/oWesIpQp2s0IAPDYrMLAJCF1hHQALYYATKqNIehKfeyANuIAHgEbsKePEGk/4q0Eutb+nvnUEwGmzwtA6AoATFNYBsCAGOKEiiqQn8bgH0oAPfQBoBCVSPO2f6tusI5S6zFJ7mPPZrMDAJiB8U+sAWBgDnFAJRdLjedwDiYFNANAMSqR42hv6fusIpaa6bR0BcNmswMAmIHw73MqI0pABTjgZRdLjZA73QGJgEwA0gxIpnnZOn7COUGqui9YRAKfNCrl1BAAnmlgHwFLm6jPACSehSHq0rsOOSgY2AUAzKJGizN8P8kxsXy9YRwBcDmyiWQGIw9Q6AJY0c7vfOgOcKgvx1DwMHU0d7oHEwCYAaAIlUpT5eX23dYRSr+mOdQS453Ng08A6AoAKdtgUI2K5rlpHMMIAp4ookh5l7HAPJAY2AUATdtTl8xVPOacfs45Q6q5etY4A93wObKJZAYjD2DoAVjJmgBOOQ5G03FCXrCO0joFNANCEbWVc+KLEP9Qp6wgl7us56wiAy4FNNCsAcdjjqjl6fgc43WSA08kokpbJ9Lp1hNaxBxIANOEWJVKU+orOWEco9SntWkeAez4HNk2tIwCoZMx5XfT8DnCScgY4nYQi6dMY2AQAqMdVPl1R6hU9ax2h1DaLCGGOgU0AwrXH92QS/A5wWlNuHSF0FEmfxMAmAEAd9nSRE2kc4SeDXGp/T5l1BLjXZWATgIDRR5oKvwOcNhx+zy6EIumTPA5sGrIHEgDUbEc9h+sSUM0/1bdbRyixr8vWEeAezQoAQrZL13dC/A5wusStueNQJD3M48CmG9xJAICa3VKmmXUIBOoNfb91hFJbum0dAe55bFZgYBMQj4F1ANSKAU4oQZH0cR4HNm1raB0BAJKyp8v0BeFI5/QJ6wil5rpoHQHueWxWYGATEI8b7OaYGAY4oQRF0vd5HNi063bDYgBoxo4y+vNxjC8Hee61T0cBzHlsVmBgExCPHZqLEsQAJzwlxBN1Gz73QOrT6wQANbquHgsncYyf1wetI5R6TbvWEeCcx2YFBjYB8dhzW0xLHQOc8ASKpA953AOJgU0AUJ9dPU9PEI71on7MOkKpQq9aR4BzPpsV2JgFiMUee80njAFOOIQi6QMe90BiYBMA1OeGeixbwQn+pk5ZRyhxn+4YmPPYrMDAJiAWe8r4fU0aA5zwGIqkks89kBjYBAB12dGzGtIRhBN8RWesI5T6GEvtYcxns8LUOgKASiiRpo8BTngMRdIHC3y8YWATANRjT1fZhxQVvKI/ZR2h1LZuW0eAczQrAAgXJVIfGOCERyiSSrnDPZAY2AQAdbilnsbWIRCFn7QOUGquzDoCnPM4sIlmBSAW2+pSInXC8wAnHEKRdOLwTcHAJgBY3bae14Bt/FHJrr7dOkKJfb1gHQHO+RzYRLMCEIfryvhtdcTvACcc4r1IOnC5B9LEOgIARG5Xl5WxPAUVvaE/YR2h1Bd0xzoCnPM4sIlmBSAG23pWI+sQaJnfAU54jO8iaU83rSO0jj2QAGA1e7quLrebUNm6/oJ1hFJ39bJ1BDjnc2DTxDoCgBM8uBVeWMdA6zwPcMIjnoukHYc9QOyBBACreFAgHVnHQFTe1mnrCCX29Zx1BDjHwCYA4dnVZW6FO+Z3gBMe8VwkZWATAKC6hwXSuXUQROWzOmsdodQntWsdAa4xsAlAaG7peQqk7vkd4IQDIfY2tIOBTQCAqnY10ZjyKBZ2Xq9YRyhVaGwdAc4xsAlAKPaUa6opv6GQJI3Vc7gZDB7xWiT1OLDpFnfFAGAJuxrx+Ykl/ZJOWUcocU/PWkeAczQrALC3q0K5cn438YSheg6/pXDAZ5HU48CmHQ2sIwBAdLY0drh/NeqSq2MdodRl6wBwzmOzAgObAGs7B52iuaRCc87vcKS5+ircrXfAAY9FUo8Dm/aUWUcAgKjsaqKJZtYxELGXtGkdodRUt60jwDWPzQoMbEKznnd4fQs0aaa+3rIOARseBzf5G9gkZeywAgCVbemiuhpRIsUK1gMtBM110ToCXPPYrMDAJgCIDQOc3PLXSepxD6TL7LMCAJXsaKIJt5VQg6nOWEcosa+edQQ4569ZgYFNABAjBjg55a1I6nEPJAY2AcDJdjTRlN5R1GQYaDHyNe1aR4BrHpsVGNgEAHFigJNLvoqkHvdAYmATAByP8ijqtq6fsY5Q6qt61ToCXPPYrMDAJgCIFQOcXPJUJPW4BxIDmwDgaHsaKqc8itq9rVPWEUrc10esI8A1n80KQ+sIAIClzTTQm9Yh0C5Pg5umDu8BMLAJAI62prFGDNRAzT6vs9YRSn2KpfYwRLMCACA+U123joB2+SmSjrVpHaF1DGwCgOOt6ZLe1FwTLmVRk/P6cesIpbY1to4A1/wNbKJZAQBSMNKWdQS0yUuRdKAr1hFax8AmAKhmTZf0lmYaqmMdBdH71SCX2t/jNgBMeRzYRLMCAKRhoB3rCGiPjyJpz2H3BAObAGAx63pd72mirnUQRCzXGesIJfb1UesIcM3jwCaaFQAgFXMNtGcdAm3xUCTtONyNlD2QAGA5l/Q15XyGYikvBbq1z5buWEeAYz4HNg2sIwAAalPwqe6HhyLpVOvWEVrHHkgAsLxNvUWhFAtb189ZRyh1VxetI8AxBjYBAOLHACc30i+SehzYdJU9kABgRZt6i6X3WMhUp60jlNjXc9YR4BoDmwAAKWCAkxOpF0l9DmwaW0cAgCRc0tc0YpgTKvmsetYRSr2mXesIcGzMwCYAQCIY4ORCiD0P9fE5sGloHQGAifqWgGSSpK7DrUrKXNNAQ02tYyBw63rFOkKpQq9aR4BjPpsVJtYRAACNmGvgcn2EMykXSX0ObOqzwAdwatTAc3bUU09d9RxuXPK+db2pLQ34dMUxCp2yjlDinvrWEeCYz2aFgXUEAEBjCg30pnUINCvlIqnHgU19zawjAEjIXPmjkRs9ZcqUubv59MAFzTSgnxRHeDPQTRkus9QeZnw2K2TWEQAAjZrquq5Zh0CT0t2T1OfAptw6AoBkFRqrr46e1w2XhZc1valJoKUw2HpJF6wjlNrWbesIcMxjswIDmwAgfQxwSlyqRVKfeyCNrSMAcCDXUF1d1C3rIAYuKQ90OA8s3Qxyqf2cnjYY8tiswMAmAPCBAU5JS7NI6nMPpKF1BACOTDXQh3Rde9ZBWrahnB3ncEiuM9YRSuzrBesIcMxns8LEOgIAoBVzDdxdAzmSYpHU5x5IDGwC0La5Ruq6K5Su6abDG3E4yjDQfrnXdMc6AtyiWQEAkLaCpol0pVgknTjcA4mBTQBsPCiUelt6f0U5u5NC0rp+2jpCqbt61ToC3KJZAQCQvqmuW0dAM9Irko4CHZ/QJAY2AbA010DPOxvmtEmZFJJ+TaetI5S4r+esI8AxjwObaFYAAH8Y4JSo1IqkfV2zjtA6BjYBsJer5+xEYUMzhjg591k9Yx2h1Kec3bJASDwObKJZAQB8YoBTktIqkvYcbpnOHkgAwjBX39mykzVm3bt2Xq9YRyhVcOsUZnwObBpbRwAAmGCAU5JSKpJ2NGEPJAAwNNJl6witokzq2S/plHWEEvf0rHUEuMXAJgCALwxwSlBKRdKJNqwjtI49kACEZUKZFC68GeSetPvOfv8QEgY2AQD8YYBTctIpkjKwCQBCQJkU6XtJfesIpbZ02zoC3GJgEwDAIwY4JSaVIqnHgU1bDhc1AYgBZVKk7qZ1gFJzXbSOALdGDGwCADjFAKekpFEk9TmwaWAdAQCOMNFV6witWtM0yMXXaMY7OmMdocQ+pXqY8diswMAmAMADDHBKSgpFUp8DmwbsgQQgYGPdso7QqnXllEmdGAZajHxNu9YR4JTPZoWhdQQAQDAY4JSQFIqkHgc2DVRYRwCAYw2dLTzZ0NQ6Alqwrp+2jlDqq3rVOgKc8tmswMAmAMDjGOCUjPiLpB4HNl3nUhxA8PwtPNl02E3lz9s6bR2hxH19xDoC3PLYrMDAJgDAkxjglIjYi6Qe90Da0sg6AgBUULj7tLrEUpuILDNm5vM6ax271MdYag8jHpsVGNgEACjDAKckxF0k9bkH0sA6AgBUNNa2dYSW3Qx0v0rU4bx+3DpCqW3dto4Apzw2KzCwCQBQzt86uiTFXCT1uQcSA5sAxGRoHaB1zLlP1y/plHWEEveUWUdoUMrHFj+fzQpD6wgAgGAxwCkBMRdJPe6BxMAmAHEp3G1ivu6wbBCr3kKPzoMsf+/ro9YR4BTNCgAAPIkBTtGLt0g6dLgHEgObAMRn7G7ZyQU6jSLRWeCxLy21h2nzvqA71hHgFM0KAAA8jQFOkYu1SJrpdesIrWNgE4AYzR2WDF9nZ9Io9Co/cl03rcOWuquXrSM0rGcdAEfwOLCJZgUAQBUDBmrGLM4iadfhSQoDmwDEauLwRGES5NJsPG5ngfErU52xjltiX89ZR2ict+XcsfA4sIlmBQBANXP13a2kS0iMRdKOpu5OmtkDCUDMhtYBWrfB5XTg9ha49TgMtJ/xk8nffuhaB0ApnwObBtYRAADRKBxe/SQjxiLpmD2QACAqU21bR2jdFeZyB21U+Xt1XT9jHbZUsUAnbKy61gFQgoFNAACcZKIb1hGwnPiKpENdso7QOvZAAhC7kXUAAxPrADjS1gIFxrd1yjpuiXvqW0doQc86AEowsAkAgJMNHTaJJCG2IqnHgU3bLosLANKSO5zzuM6nd6AWWWr/ps5axy11Ofml9hKdpCFiYBMAANX0XZytJSeuIqnHgU27LnpFAKRvaB3AwDU64YLUr7xw9nygBaGpbltHaEXPOgCekDGwCQCAihjgFKWYiqQ+BzZVv5QDgJDNdMs6goGxdQA85Ybyyo/91SCX2s910TpCSzatA+AQj80KDGwCACyPAU4RiqlI6nFg05A9kAAkY+jwXuomF9iB2VngZDXXGeu4Jfb1gnWElvSsA+AQn80KDGwCAKyCAU7RiadI6nFg0w3GfgBIyNxlX+VIHesIeGSR3UhfCrSP8TXdsY7Qkp51ABzisVmBgU0AgFUxwCkysRRJfQ5sGlpHAIBajR32kq7zWR6QUeWSx7p+zjpsqbt61TpCazLrAHiMx2YFBjYBAOrAAKeoxFEk9bgHEgObAKRn7nIAxpAZ3YHYWqCX+dd02jpuift6zjpCizLrAHjEY7MCA5sAAPVggFNUYiiS+twDiYFNAFI0dngndY1L7SDsLrDU/rN6xjpuqU85+v3pad06wsLm1gEa4rNZYWAdAQCQDAY4RSSGIqnHPZAY2AQgVSPrAAYusbtiAKoPYFnXK9ZhS2272tU3sw6whMI6QCNoVgAAYFUMcIpG+EVSj3sgMbAJQLomLjcvH1sHcO+G8sqPLXTKOm6Je1GWDZc3sA6AAzQrAACwOgY4RSL0ImnP4R5IDGwCkLaRdQADm84KXKHZWeCb9U11rOOW2Ndl6wit6joszIWJZgUAAOrBAKcohF0k7SzQ95EKBjYBSF3u8j7q2DqAa4PKj3xJF6zDltrSbesIrepbB4AknwObaFYAADSDAU5RCLtImrMHEgAkaGAdwMCGi6OeWQcodXWBpbM3g1xqf1cXrSO0bGgdAPI6sKlvHQEAkCwGOEUg5CLpxOFSK/ZAAuDBTLesIxgYBbmMu14z6wAlFhl3lOuMddwS+3rOOkLLsggn26eHgU0AANSNAU7BO20d4EgD9kACgGSN1Hd3+b2uocv9WG3tLdAXNtSmddxSr7nbwWqm5yUVT5SrsoN/76inboBl1NS2EWFgEwAA9RuqF+gZJySFWyTt6aZ1hNaxBxIAP2Ya65p1iNYNNaZLqWWDyj/xdf20ddhShV61jtC6WWlPcv7Yv0sd9ZSpp8zd7ZZ2MLAJAIBm9FUEeLMXB8Jcbu9xYNMivS4AEL+xw43L15If3zS3DvCEWwvsqfh2kDeO73N2cIS5co3UV0fP6mpyXZzWGNgEAEBTGOAUtDCLpP4GNklZcJeWANCkucul55fUtY7QqMI6wCG7lYse63pTZ63jlvqYu6X2iys0VqYP6bJxqXRu/YOoTYeBTQAANIgBTgELsUjqcWDT5cAuLAGgeWOX5Z+JdQBHqo9gmQZaINnWbesI0Zhrokwf1nWzz5XC+kdQG3/NCgxsAgC0iwFOwQqvSOpxYNMtLpoBuDSyDmBg82D8TJrm1gEec71y0eqz6lmHLTVP+r3SjJlG6pr3lMbNY7MCA5sAAG0bcrYSptCKpB4HNu1oYB0BAExMXJ4cjK0DNKiwDvDITuUS/LpesQ5bal8vWEeI1kSZnm/902Vmfdi18NiswMAmAICFvstVdcELq0jqc2BTZh0BAMyMrAMY2ODWWOMWGYZY6JR13FJf0B3rCFHLWy+UzqwPuQYemxUY2AQAsMEApyCFVST1twcSA5sA+Ja77CUdqWMdoTFh/H2OKhes3gz07+KuXraOkIBcmS7SpVGZx2YFBjYBAOwwwClAIRVJPe6BxMAmAN4NrAMYWOeEqFFblbc0eEkXrMOW2tdz1hGSMVVP11t5pdz6UGs4An/NCgxsAgBYYoBTcMIpknrcA4mBTQAw0y3rCAaGgfYvrq6wDqC9BQrvNwNdav9Juh9rNNdIHw6kxzlkNCsAANA+BjgFJpQiaS/pQRblGNgEAJI0crgbz1qy33pz6wALdIblOmMdtlSR7LvDzkxZw/2ksV/g0KwAAIANBjgFJYwiaUdTdwt8GNgEAA/MXJaELqlrHaERhfHr36i86HmoTeOs5e7pWesIiRrp2QYvQubWh7cSjwObaFYAAISBAU5BCaNIOtW6dYTWMbAJAB4auzwxmFgHaMTc9NV3NKr4yHX9tGnSo122DpCwQj1tNfbc8fI4sIlmBQBAOBjgFJAQiqTjQHs5msQeSADwvnnl0lZKNpO8SM9NX31QuUj7tk6bJj3KVLetIyRtrr6uNvLMM+tDW4G/9Vw0KwAAwjJxOaUhSPZF0r6uWEdoHXsgAcBhY5d78YytAzTC7m/yeuUbkJ/VWbOUx5nronUEB8a62EDv+sz6sJY2olkBAABzA+1YR4BkXyTtOiwXsgcSADxtZB3AwEaS3wczo9fdrvweOq9XjDIeb1896whOTJXVXiYtrA9qSZmuWUdoHc0KAIAQ1X92giVYLzbzt8BnT33rCAAQoIkGDvuZRpomt+gzN/l73Fug4PxLOmWQ8GSvueynftq6LkolZ0vvaVu/W9N2BIUy5TWeg+5F+nvc0dQ6QutoVgAAhGmuTO9Yh4BtkXSkDesfQOv6ES/IAoAmjfSWdYTWrWuYXA/tzORVB5VfN1fHJOFJvqpXrSOYWtdF9bWhtWNK2H1Jv6B7+j/1W/qFFculhbrKazsPLdr6MdXMY7NCZh0BAIAjFLqsm9YhvLNcbu9xgc9Vh/NDAaCaXNvWEQwMAy3ZLW9m8JpblfvhXgq0X/m+PmIdwcx5van3NNPr2lSnQpfvGT2jvn5B39S7+uwKrztXVlvvbt7GD6p2w0B/G5rEwCYAQMgY4GTOrkjacbgf0K1Eh3QAQD0G1gEMrCX3zZC3/oq7ld8568Henf+U06X2n9d7+g31l7pVcErP6NP6pt7Vy0u++lz9mnb/mjX082lSL7ku9pMxsAkAEDoGOBmzK5KOtG598C3b0dA6AgAEbeby3uklda0j1KztU7tB5d6wqc60nK2a7eRK5SdbV65v6uMrd1Kf0jP6G/qGPr/Uny5qGpJQ1PzTacPE3VJ7BjYBAGLAACdTVkXSTFesD71le+qzwAcATjByeVIwsQ5Qs6LVV7teuXf1s4FOj7/nbpfEdeX6mjZrHKB1Rh/XHy5VKC1qGahZ1HYkbfE3F4CBTQCAOMzdnRkGxaZI6nGpPQObAOBkM4cdddJmYqdCeYuvtVN5yfC6Xmn/R1HBvj5qHaFlb9ZcIH3otD6uP1xil9Jcl1d85fh2U+66mwvAwCYAQDyKlc9NsDSbIunQ3VJ7BjYBQDVjl72kY+sAtSpae6W9BXrD3m6gLFeHLd2xjtCiob6hfoN/E6f1ab2n8wv+qVWHJOSNHU9TJtYBWsd6LgBATBjgZOa0wWv23N29lvq1LObCYbNH3bm5pILTXyAJc430unWI1m1okFDZotBeS7sdjioXZN/UWbOfx3Hu6qJ1hNasa9rKhgcd/bq2Fvy5DtRbYfl50cJR1Wngbqr9nsMhVW14eO49V6EYbxYAQMhWOzfB0k7tt/+aubtTM7RlRzMVyoMumKb+/n8+2JPkTG9ZR2hYmF1yy5m5W28g7aoX8CfXoqa60MKrbFdeQPuSvhjkb8i+Puxmqv1f019u9e/grp5b6GfbVbF0af9DUf3udjRzN7IJbdnWTIWKYM8FJcng4rcF4Z5/w0pq15w+3+N8Yy+j+vXBEdpfbu/v7jXas6ELuqa39J5mmqi/8sxcADZG1gEMrGtoHaFGeQuvsbfAGo2bQZZIpU86KZEO9Z7+85b/Ds7qXb20wONnS4/12YmqRCqNuOBCYzZ1Sa/rLe2r0Jh9YAFgJQxwMtF2kbTj8tIX7VvXJb2p9zTVgFIpEJ1JhINQVjdM6NMqb+E1BpVLU7nO2P0ojlEkthdtuc/rG3rd5L19Wr+w0Lz7qbaWep3c4NiW19UV6whwYUNX9JbmmrDlGAAsjQFOBtoukvob2QRbF3RTM01a2QUNQH1G1gEMrCVUMisaH791Q9OKjxwGuoLlnoPSwVDv6eOmJeqP680FHj1Y6n2bGx7f4ibWAeDKmi7pTc00Utc6CgBEiQFOrWt3T1L2VICdbY2CuJBJbX+YJ4W7Xwx7ksYm9d+Vch9+NJAuds3uSlp9B9d1vWsypPJkP6rb1hEa9Xn9+UB6o7+q76v82IFuLvz8Me1Imv43IcJ1S6MgvuHS3JP0anQD5EKTWwdo4IjSOo8O9xqzDQUDnBaw8p6k7V44sAsS7GzqLW1ryCkEEImh3rGOYGCSzN5DzRZJ+5XLUm8HWiLdTrhEel5/XRsB3bZ5Ru9WLpNOFt47P64dSUfWAeDYJV3SLQ2j+o2Jx+vWARKxp0JSrnng48fgTUazYZvaXG7PLkiwtql3NA6kswXA8QqXi0s2kymS5g0+9/XKt7s+q7PWP4hSqW7Ev67P6xv6DfUCKpFK0jN6u/Jjhws+d259cAvIEusrQnwuaZbUkEKkZk2b2tS1R+PHJky3QBBSPW8MVJtF0pH1wQKSrqjgQwaIwsg6gImxdYCazLTT0DNvV35nnNcr1j+GUvt6wTpC7YbK9Q3NjHcgPdq/W3mE06K3ZybWh7aAkXUAQGt6XQWzAhCFDV3STb2nQkN21YUxBji1qL0iaVeXrA8WkCSt661kyhBAyma6bh3BwIYG1hFqkjfyrHsL/Hx+KbB+xode0x3rCDV5SZ/XO3pP+3pdm4GWRx/6eOX+tdEC45v2ItrEhz5ShGJDeTLfdPBgQ6/ra7xrYYwBTq1pr0g6sD5U4DFXlLN4AgjeuPEZ6SEaJfLpNGnkWYeVB3+8GejP8a5etY6wlBf1ht7QT+pN5XpH7+kPta9f0MfVC/Tn/LSf0flKj5stcCN1an1QCxhZBwAeWdPNqLqwAWlTNzWjpgFDg8ZWaeGQtoqkHfafQWA2lbPYBwjc3GXX93oi35iFdmt/zq3Kl9UvqW/9AzjCWe1H+a9f1E/oJ/Rfq69N9dQJdCDWcU7pVys+svrtman1QVVGHylCc4mGBURnXTc1C/b8AunLXLaPtK6tIumAaVwIzgZlUiB4owYKbeEbJnLhOK35+aovtV/XTeuDR4DOVNwEYl7xvbsXUZF0YB0AeMomZVJEaF1vKmePUphggFMr2iqSDq0PFCixRpkUCN7IOoCBtUQ6aCc1P19f84qPnAa+PyasbFY8Ix1VelRufTiVMRkAYdqgTIoobarg1hNMMMCpBe0USftatz5QoNSaJpycAUGbuNx/51ISPQr1Lri/UbkkNeT2F470U5UeNdNWhUdNrA+msoF1AOAIGxH9HgHvY19dWGGAU+PaKZIOrA8TONJGRJ0ggE9D6wAmJtYBajGt7Zl2Kr8P1vUz1oeNgFVdcj858REstgfqcCGRtRPwh311YYMBTg1ro0ja1QXrwwSOscHJGRC0XNvWEQxsJrHr0Li2ZxpUfuTbOmV92AjaD1Va3zQ9sQ96an0glbGiC2G7QhkfkWJfXdhggFOj2iiS9q0PEjjBFd6lQNCG1gFMjK0D1GBW073uqyoqPvJNnbU+aATulH6t0uOmJ/zvE+sDqWxgHQA4wTiJLWbgEfvqwgIDnBrVRpF0YH2QwInYmRQIWeFy952NJL4/xzU8x3blZznP2hVU8IzOV3jU+Nj/dTeazXo6/FYgeGsR3XQADtuIaF0B0sEApwY1XyTtasP6IIETrbmcoA3EY2QdwOioO9YRVjZdeUHQ3gK9/r/KUntUMqnwmOP7oKs8Qxj61gGACjaTuC0InzaTWPuD2DDAqTHNF0n71ocIVHKFachAwGa6bh3BwHoCGw3MV+6wGGhe8ZG5zlgfLiLxb1bapTM/5n+bWB9CZX3rAEAl4wRuC8Irtm6DBQY4NaT5IunA+hCBisbWAQAcY+xyi/JhAheNk5X+9K3KRdahNq0PFdE4Vek7f3Lk/7KlmfUhVMRie8RiLYHbgvBrwr66MMAAp0Y0XSRlsT3ikcYsaSBVc5c3MtYSOOr8xCnhR9utfNG8rp+2PlBE5c9VeExx5MXHxDp+ZZl1AKCyFG4Lwiv21YWFOT3MTWi6SJpZHyCwgIF1AADHGK1QbIvXpQR6E0ZL/8nqS+1/TaetDxNROa2XKjwqL/2nuxGN6ehbBwAqW+NMHBHb5PMWBnJdtY6QHoqkwPtSKEYAKRtZBzAxsQ6wsmWHN12vPD/8s3rG+iARnU9XeExe+k/H1tEXkFkHABYwtA4ArGBsHQAujRngVDeKpMDjBtYBABxj4nKD8vi3ApkvVejdqVwUP69XrA8REfqBCo/JS/7ZXkQ3LrqVBlQBoVinFw8RW3d6Mx/Whi6vjxrUbJGUUzPEZmAdAMCxhtYBTIysA6xsvPCf2FvgUvmXdMr6ABGhKgvui5J/Nq68CYS9zDoAsKC+dQBgBUPrAHBprj4DnOrUbJE0sz48YEHr6llHAHCMXNvWEQzEv8/VbOGlQKPK08PfZNQHlvSjFR7z9CfOxDr2AnrWAYAFXeITHRFjX13YmEV/pRCUZoukPevDAxY2sA4A4FhD6wAmxtYBWj6CrcqPf4nTQiztuQqPKZ7477cql+9DkFkHABaWWQcAVjC0DgCnGOBUI4qkwGGZdQAAxypcbk++Hv0NnGKBHuC9BY72pvWBIWJ/rMJj5k/895F16IVsWAcAFta3DgCsYIMKCIwwwKk2zRZJN60PD1jYBhPugcCNrAOYGFgHWNlogWOdV3zkOzpjfViI2CmdP/Ex+aH/Flcfac86ALCEzDoAsJKBdQC4xQCnmjRZJO1aHxywlMw6AIBjzXTdOoKB+GfcV91P9oamFZ9xSBEIK/rTJz5ifui/jawDL6RnHQBYwjrXkIha3zoA3GKAU00okgJP6lkHAHCCsctTgKF1gJWNKjxmp3IZal0/Y31AiF7/xEcUj/3nuPpIORNHrDLrAMAKGAMMOwxwqkWTRdLM+uCApfSsAwA4wTyBQUaLuxB9ySPX1omPqb7U/m2dsj4guLIXWR8p5zOIVc86ALCSvnUAOMYApxo0uycpECP20gXCN9KudQQDQ+sAjR/B9admiR/l8zprfTBIQJXBRg93+BpH1kcqdawDAEvpWQcAVpJZB4BrDHBaGZ2kwNO61gEAnGhkHcDAwDrAyma6ccz/ul35b/W8ftz6UODGXJK0F2H/es86ALCUnnUAYCU03MAWA5xWRCcp8LSudQAAJ5o4PAFYS2AJ1+jI/WT3FigC/ypL7dGyUeWNIMKxZh0AWArvXMSuZx0ArjHAaUVNFkk71gcHLKlnHQBABUPrAAb61gFWNj+yW3RQeTFzrjPWh4FEdCo+bifCPlIgXpl1AGAlPesAcI4BTitpskhaZZ8nIEQd6wAAKsi1bR2hdZcS+Hwal/YAb2la8c+/xEI2tG5oHWAJmXUAAHCqax0A7jHAaQUstwee1rEOAKCSoXUAA5l1gBoMn/onu5WX2q/r56zjw51byq0jAK70GnxuFqGieZl1AIABTsujSAo8rWcdAEAlhcOv/751gBrkT41vGlTe7/HXdNo6PpyZu7wdA1jqNPjchfXBwYGOdQBADHBaGkVSAEC8RtYBWte3DlCLkXYf+283KvfpfVbPWEdHUvYrPGYY4cgmAIAdth1ECBjgtCSKpACAeM103TpCy9aS6HWfP7a8fqdyn966XrEOjsRUuXiYWYcEAABYGAOclkKRFABS4Hc5xdjdPdK+dYBaPFxyv1d5N1Kp0Cnr2ACAxnUbfO7C+uDgQs86ACCJAU5LoUgKPK1jHQANmFkHaNjcOoDhkY+tI7Qssw5QkwdL7keVL1jf5LMZtft96wAASnQbfO659cHBhY51AOAAA5wWRpEUeBr7yKRoZh0AjRkf2t8yfT3rADWZq6/tyiXulxLpoEVY/nfrAABaNrMOAACtYoDTgposknpbAAlUMbMO0LDCOgAcmjsb35TGrqSSVCzQFXvTOiyS9J51gAYV1gGAIM2sAwBAqxjgtKAmi6SF9cEBS2ryXsvM+uAaNrcOAJcmznpJu9YBWveOzlhHQJK2rQM0aG4dAAjSzDoAALSMAU4LYbk98LS5dQA0Iu07aDPrAMYG1gFa1bMO0LKhuyNGW960DgCgxCzS5waAMDHAaQF0kgLtmlkHaFTYZcjCOkCjZtYBjOVJd4Q9qWcdoFXr+hnrCEjU/cR70D19KiIts0afnd8MAP74GeCUr/oETRZJ5639GIB4zKwDNKqwDgDHRtYBWtSxDtCqt3XKOgISlXaJlDNxoNzMOgCAhc2tAySAAU4VNVkkza0PDgjQ3DqAY4V1AI6uUbmb+6PSpnWAFn1eZ60jIFm5dYCGFdYBgCAV1gGAFsytA9SssA6QAAY4VdRkkXRmfXDAkmYNPndhfXCNyq0DHGtuHYCja9jIOgBqd14/bh0BCZtYB2hYYR0AWNK80WcvrA8PaEFhHQABYoBTJc0WSalTI06zRp895eV9c+sAxyqsA3B0DZvphnWE1nStA7Tkl1hqj8bc0x3rCA2bWQcAllQ0+uy59eEBgBEGOFXQ7HT7wvrwgKXMG332mfXhNaiwDnCsmXWABu0FXqBuy8jNzbmudYBW5M52X0W7/jfrAI0r3HwiAothdBPSN7MOUCt+Z+sz1pZ1hIblqz5Bs0XSleMBJoqIn93WzDrAsQrrABxb4+YaW0dAbV5ytfcq2vffWQdoQW4dAFhK3vDzT60PEMkrrAMEflUGSwMGOB2v2SLp1PrwgKXMGn32wvrwGrMX/Ndxul8IhXWAYIyT3tDCk3X9nHUEJG2e/GJ7iSIp4tR8B3RufYhI3tw6QPBXZYsprAMkZa5B0itNZqs+QdPL7VP+4SNds0afvbA+PMdHFn7CZc2sAwRjzvimREx12joCkvYl6wCtyK0DAEsoWngFbqiiSSHUQGbWAWo1tw6QmEID6wgNmq36BM0WSeklRYya3vOksD7AxuTWAU5UWAdoTG4dICCThDuG/fisetYRkLR9vWwdoRWUghCjooXXmFofJJJWWAeQlNYKutw6QHKmum4doSE1vO8pkgJPKhp/hVS3ni6sA5wotw7QmMI6QFCG1gGwonW9Yh0Bift16wCtmVoHABZWtPAaE+uDRNJm1gECSsGxhGqU6ACn+epP0XyRNIRmc2ARReOvkFsfotvjSnULkFTL7svKE/3S9+Pf0CnrCEjavi5ZR2jNxDoAsLCildegyxrNmVkHkJRSC0X4cy/ilOYAp3z1p2i6SModbMQnb/wVptaH2IidKHaLmVoH4KhaMUy0HP6+uXWARt3RNesISNqvOyqPFEleAiFley2VdibWB4qE5dYBAkpRh8I6QKLSHOBUrP4UzRdJx42/AlCn3RbuVKXZz5hbB0goJUe1qlny3z2FdYCG/SRlUjTmvqM+UolSEGIzbel1JtYHioQV1gECSlGH3DpAslIc4FSs/hTNF0kLFoIiKtOEXqVdE+sAlUytAzRgN6GToPqM6J6KHGVSNOWWoz5SSZokeWMW6cpbep2ZblkfKhK1G8h6n3ky58KFdYCEpTbAqZaGt+aLpLGUToAH8lZeZWp9mLVra3HUquYJ7laZWwcI1MA6QINSOek93k/qY/pX1iGQnLmTufaPH/HEOgKwgGlrrzSyPlQkKrcOEGASjiNcaQ1wyut4knaKpL7u2CNme611kqbW1zG1DpBg0qom1gECVeiGdYTGzKwDtOTn9TxlUtRqXy9YRzAwtg4AVLbVYg/ejPWOaERuHSDAJKvYDqQzN10pDXDK63iSNoqk3KdDPCYJvhLHc1hqBerdRE6AmpDukvvCOkBrfkN/Wl+3DoGEbOmOdQQDs4RvGSE101ZfbWh9uEjS1DrAY0lSuOqZWgdIXkoDnPI6nqSdIumE+3SIxCTBV2pDTIW6eWJftRPrAAGbJ7vkPrcO0KJC/4b+hXUIJOKuLlpHMDJK5vIHadtr+aymYF9S1C6svsepdQCOIQqF+tYRarFTz2q7doqk9JIiDtst9melNdJsbB0g4bQnmVgHCFqR2GbkD+XWAVo11x/VP7AOgQTc13PWEczME/vmQ6rGrb8iNxBQt4l1gEOm1gFWVlPZCyfIddk6Qg0m9TxNW0XSnPt0iMC41VebWB+u22NJqUC9xYnDCUYJ/W0/lN4Rney8/lvrCIjcvj7meo/8dLcfQUomrb/ijBsIqNnUOsATaWK/DTCxDuDGJIGK3bSep2mrSCoNo/8FRep2W/5SS2ek2a2glpVUMbIOUJuxdYAIpLPLzkNT6wAmPqUXdN86BCL2mm5bRzA2sA4AnOCWyY1fbiCgTuFdFU2sA6xoah3AkUHkc+636/oOaa9IOk9knwOka9D6K46sD9ntceSJ9OJtO1t2vZxZct8+U+sARn5Zf1T/3DoEIvWzetU6grlUtx9BKvbMzifTu5kKO2PrABEkWgRr5toV95z7SV1P1F6RVMqZrYmAWZSb0ugltbnvv6qRdQCOokV5UqWB2u6SRmiuP+6+GxDL+Fm9bB0hCCluP4J0jM2+3QrOp1CTNudbVDWL+pN/bB3AmbmyaOsTu3EWSaVh1JVppG3g6FXrNbIOsJQUeknpI61uFPnykcdNrAMY+1G9oN+3DoGoUCJ9X5+OOQRq17QYMk5gLz6EYGQdIKJUVXCt0755tGcKk/qeqt0iqSKuTCNt143uXsdfqrP6ya1uYB1gZUPrAFGJe/nI+2q8SxqtX9b36NetQyAalEgfN1cW6cUPUjcw3smRVh6sbivQkl6815sj6wAuFVGeKezVeaOt7SJpvJVppGzb8CM47n2QbO/7r2YW+RLsGwEu6AlZKqWBsXWAIMy1qR+jnxQn2tePUiJ9QsENNgTohnlxKeZFpgjDXsCfriPrAEuhj9RKjGXScZ032k7tt38APeVaa/9lgSPsqWfaDTnU69Y/gqVdjHyETKEN6whL2lUvuOmZ4Yv/22dXXesIAeloSz9kHQIBu6eP6o51iCANdNM6AvCYHfWsI0hK4SwBlq4GfSN7okvWERb2PEVSQ3GdKdR8Zdx2J6kUZ2UaKesbLxgfR7sEYivyEmnMS+6tl6XFKf5vn5F1gKA86CeN+28Uzflt/UlKpEeY6LJ1BOCRPWXWEQ7Ef5YAO9tBl0ilYXTv7VuUSE3FdaYwrPfK2KJIylcQQnI5gA/gODeh2I24xPhQoavWEZZivywtVnEvNN1mP9Kn/II6+oK+aR0Dgbmvz+nfZunsMeK6+EHK9pQFdNuXa1QsZ0996wgnmEd2oz3kzQu8iOdMYbvuxi2L5fYP9DSJdqEr0nE5kKJDpresIyzs2UT2xIxv+Ukoy9JiFdfykfdZbwwSsnVtcUaBR7aD6UsLW6YpS4thbE9ZcGeTHeV8o2AhIb6Py0x1wTpCZbFv6ZaKGK6aGrhCsukklR7cqYt1kTFSEUqJVMqjuVPz0OUoTgaqiG2eafj3qkMXz33Rw0aUSI+0q55+RL9nHQMBmOsHKZFWlCuL7PsPqQmztDRXpi3rEIjKMMD3cZlBNCssblEiDcREF4Pvrh/Wf4VkVyR98BUU93RpxC2cEqkkTXTLOsICbgX1s1vNPLLLxIxS2comej74L/wn3Qp8ryt7f0dn9Z8FtGgT7Zvrqj7EPqQLKCgGwVCYJVJJmqvPNSoqC+uK8jjzSLZ422GpfUCmgW9C0khVwm65/UOZJlq3DgF3wjwxi2Xh960EdiM9rKsikkWH8ZyIhS6uGbY7Qe3ZFrb/Sv+lPmAdAq2b6zo3EpY01CiiT0OkIvzvNa5RcbI9DSM7Mw9/+fSeuoF/NvgT7lVTQ98klp2kD+Tq6YZ1CDizrW6AJVJpEEU36U5yJVJpFvg9socokdaniKiDeDf4S8mQfEZn9Jq+bh0DLfq6rupDlEiXNlaPDbDQshvqBf+9xjUqTrKnLLoz89A3nQprlBseKNQL8qppT/1m3i32RVJprqGeD/LHjhTt6WrAH77hl0m3Et3tLdQP/8dRIq1XLAtNGzsBSNin9V26qn9hHQMt+H29pu+iQLqimTJdjuJWIVKwq4uRLKada6hnuYWAI4TadHOSkMukYa71xIPzhNA+C/ea24QuhCKp9OBe3eVothJGvLbUC/xiaqCr1hGOcSvhcs0s6M7CPUqkDYhh1zFOF5c11h/Vj+ir1jHQoN/TazqjT1vHSMREXV2nUIrG3VAvqpEshTI9zzUqnhB2081JQi2Tcs4bsrmyoHrrG3232O9JethAI3Z/QUO2NVJuHaKSviZB7vtxXSPrCI0Lc19YThqalGka5O+bFMOebeH7d/TX9O/ptHUM1Oy39ZP6BesQCepoqGGwn4iI3S2Noh0+OdBAm9YhEIiY38kPDTQO7LOeq50YhPK+afjdElqRVOJLCE2Ip0D6QE8TbViHOGRPg6ju/C8vlA//920n3L8bho4mumAdogR/8/X5a/pP1LEOgVrc01R/id+MBnU00JCWBdQshbJSpkGQt9LRpi2NEinl9TQN6JOetoBYhFClaLygHmKRVJK6Gqof0K8t4rWnqcZRfpmNdcU6wiPbGkR/altdCB/+7/PQvxuCvsaBfefwN1+3dY31Z3XGOgaWtq8dfU63rWM4kWmgfmC3DBGnXU00SeYssqO++kHeWEXT9jRNoNT/uHCaBG5Esk8xJKmjkWmVYkeDpms7oRZJH+ipr35AxQrEZU9TTaPufsw0DuD9v6dR4Pu4NmEUxILDbQ2jLPDHqaOhrlmHOLCrQVS97zE5r7+uH2D5fXTuaqJXrUM41FdG0wKWtqupJkmex3QOfjfszxTRji1NNU2y09F+kzfOeWNk977Z0qD538Swi6QPdJQpU48l+KhoT4Vy5Yl83A41Mv3quqVhkqcEJ+tqbHp3dU9DRjW1rqtRAEvpbmjk9LeuPS/p05RKI3FX/4v+KmNTTHUPzsTtb9siDrsHZ+KFdZDG9ZSpx+9GwnaUK4+65eZkHY0Nz305542VRT9pa61bMRRJ39dTVz111VWXu9o4ZE+F5ipUqEhqGYRkOUYhhf2jVpNpZHJ7Zk9jjTllMGJbKPW1tYW1l/RpfT8L8AO1r9/RL+p/oDwalExdddVTRz166HDIrmaaaaZcM5ffYu//bnCVGrsdzVVopiKRhpsqbK54tjR0+WmRjp7GLb5vWrxGiqtI+qQeYxggufgCa79QSoH0oba36d/VhAKpuY6GGrR+mbOlsYvPs9Cc10A/ou+mrzQYc+3ov2fv0ShwJg5p7qBjdHFdda0jYGG+38vtFkpvacI5bxLaed+0vClD3EVSwJeBBq18CI01oUh3SFeDVkpmW5okvqQnLn31W9pxbE8TjbktYey8Bsq0TrHUzFw7mjrcAxsAgBB0NWr8zJdz3vQ0+77Z1ajtLegokgJx6aqvQUN7H6W7wX49miyZbWtKaTpQzZZK4x8wl57z+hH9sLpa0ynrKC7MNdNX9HfpHAUAIAAD9ZU1cObLOW/aBurXPs/DaJUdRVIgRg+GKGQ19TbuHWxKPrM+rCj01FdWW0fvjgpNlVMeDV7v4HeuvlNGD8MAYreui9pUV12WFNfs9/V/63f095TrjnUUAADwlL4yZTU15nDO60dd75stTTW1ukKmSArErKueesqW2iR+92B7/YLe0aU8mGfaXapcunOwHXxBcTQ6D37neuou+fW/o0Iz/u4j1NGL+j49p7P6I/qgPmAdJzpf157+me7qn2hbv2EdBgAAVNBRT9mS571c7/jVOahQLH6lvK1CuXUDEUVSIBUPps12dPQghQfzRmeaOd+YvG6dg5+9Dv69zIPTg0Jzp1Nf0/Tg7zuTdNz4Ev7u09RRT2f1ffo+SWfV0dmDf/4BfYd1NDN7B/9/fvCvr+tdvat/xvcNAADR6x786+jBZO9fa3LOi4cev17KSh/xsDoRzLuGIikAAAAAAAAA177FOgAAAAAAAAAAWKJICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXKNICgAAAAAAAMA1iqQAAAAAAAAAXPv/AQmmVAseL8hBAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIzLTEyLTIyVDE3OjU3OjE1KzAwOjAwms48jQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMy0xMi0yMlQxNzo1NzoxNSswMDowMOuThDEAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjMtMTItMjJUMTc6NTc6MjcrMDA6MDCllrMkAAAAAElFTkSuQmCC"))
	message.WriteString("\r\n\r\n")
	message.WriteString("--boundary1--\r\n")

	for _, recipient := range recipients {
		err := smtp.SendMail(config.Host+":"+fmt.Sprint(config.Port), auth, config.SenderEmail, []string{recipient}, message.Bytes())
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Failed to send email to %s: %v", recipient, err))
			continue
		}
		logger.ServiceLogger.Info(fmt.Sprintf("Email sent successfully to %s", recipient))
	}

}

func DeploymentCompleteReportFormat() string {
	return `<table style="width: 100%">

                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              ID: {{$deployment_name}} 
                            </td>
                            <td colspan="2" style="padding: 10px 0px;">
                              Name: {{$deployment_display_name}} 
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Type: {{$deployment_type}} 
                            </td>
                            <td colspan="2" style="padding: 10px 0px;">
                              Stage: {{$deployment_stage}}
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Created By: {{$deployment_created_by}}
                            </td>
                            <td colspan="2" style="padding: 10px 0px;">
                              Created On: {{$deployment_created_on}}
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Ploicy: {{$deployment_policy_name}}
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Description: {{$deployment_description}}
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Total Installation count : {{$deployment_total_task}}
                            </td>
                          </tr>
                          <tr>
                            <td colspan="2" style="padding: 10px 0px;">
                              Success count : {{$deployment_success_task}}
                            </td>
                            <td colspan="2" style="padding: 10px 0px;">
                              Failed count : {{$deployment_failed_task}}
                            </td>
                          </tr>
 </table>
                            <p>  To check Status: <a href="{{$server_url}}">click here...</a> </p>
                          

                       `
}
func DefaultReportFormat() string {
	return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <title>ZIROZEN EndpointOps</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="color-scheme" content="light">
  <meta name="supported-color-schemes" content="light">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500&amp;display=swap" rel="stylesheet">
  <style>
    .clear, .low {
      background-color: #89c540;
    }
    .medium {
      background-color: #f5bc18;
    }
    .high {
      background-color: #f58518;
    }
    .critical {
      background-color: #f04e3e;
    }
    @media only screen and (max-width: 600px) {
      .inner-body {
        width: 100% !important;
      }

      .footer {
        width: 100% !important;
      }
    }

    @media only screen and (max-width: 500px) {
      .button {
        width: 100% !important;
      }
    }
  </style>
</head>

<body style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; background-color: #ffffff; height: 100%; line-height: 1.4; margin: 0; padding: 0; width: 100% !important; color: #000000;">

<table class="wrapper" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #ffffff; margin: 0; padding: 0; width: 100%;">
  <tr>
    <td align="center" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
      <table class="content" width="100%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; margin: 0; padding: 0; width: 100%;">


        <!-- Email Body -->
        <tr>
          <td class="body" width="100%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%; background-color: #ffffff; border-bottom: 1px solid #ffffff; border-top: 1px solid #ffffff; margin: 0; padding: 0; width: 100%; border: hidden !important;">
            <table style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">

            </table>
            <div style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; padding-top: 32px; padding-bottom: 32px;">
              <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 600px; border-radius: 2px; border-width: 1px; margin: 0 auto; padding: 0; width: 600px; background-color: white; border-bottom-left-radius: 30px; border-bottom-right-radius: 30px;">
                <!-- Body content -->
                <tr>
                  <td class="content-cell" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw;">
                    <div style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
                      <!-- header content-->
                      <div style="background-color: #3584DC; padding: 20px; border-top-left-radius: 30px;border-top-right-radius: 30px;">
                        <table>
                          <tr>
                            <td style="border-right: 1px solid white;width: 33%">
                              <div style="padding-right: 12px;">
                                <!-- logo of branding goes here just replace with client logo base64-->
                              <img width="100%" src="cid:uniqueID" />
							  </div>
                            </td>
                            <td style="width: 67%">
                              <div style="color: white;padding-left: 12px">

                              </div>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                    <div class="content-wrapper" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; background-color: #ffffff; border: 1px solid #dee5ed; padding: 32px;">
                      <div class="font-bold" style="box-sizing: border-box; font-family: 'Nunito', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-weight: bold; font-size: 24px;">
                        <table style="width: 100%">
                          <tr>
                            <td align="right" style="vertical-align: middle;width: 20%">

                            </td>
                            <td style="width: 80%">
                              <span style="margin: 0 12px; font-size: 24px">{{$report_title}}</span>
                            </td>
                          </tr>
                        </table>
                      </div>
                      <div style="margin: 12px;">
                        {{$report_body}}
                      </div>
                    </div>
                    <div style="background-color: #3584DC; padding: 20px; border-bottom-left-radius: 30px;border-bottom-right-radius: 30px; display: flex; align-items: center;">
                      <div style="text-align: right; font-size: 10px; color: white; width: 100%">
                        Powered By ZIROZEN EndpointOps&trade;
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </div>
          </td>
        </tr>


      </table>
    </td>
  </tr>
</table>
</body>

</html>`
}
