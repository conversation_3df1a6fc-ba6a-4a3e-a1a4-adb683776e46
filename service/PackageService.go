package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"reflect"
	"regexp"
	"strconv"
	"strings"
)

type PackageService struct {
	Repository *repository.PackageRepository
}

func NewPackageService() *PackageService {
	return &PackageService{
		Repository: repository.NewPackageRepository(),
	}
}

func (service PackageService) convertToModel(packageRest rest.PackageRest) (*model.Package, error) {
	var osType common.OsType
	var osArch common.OsArchitecture
	var pkgType common.PkgType
	var pkgLocation common.PkgLocation
	var pkgFileList []model.FileMetaData
	var err error
	if packageRest.Os != "" {
		osType = osType.ToOsType(packageRest.Os)
	}
	if packageRest.OsArch != "" {
		osArch = osArch.ToOsArch(packageRest.OsArch)
	}
	if packageRest.PkgType != "" {
		pkgType, err = pkgType.ToPkgType(packageRest.PkgType)
		if err != nil {
			return nil, err
		}
	}
	if packageRest.PkgLocation != "" {
		pkgLocation, err = pkgLocation.ToPkgLocation(packageRest.PkgLocation)
		if err != nil {
			return nil, err
		}
	}
	packageRest.Name = packageRest.DisplayName
	for _, dataRest := range packageRest.PkgFilePathList {
		pkgFileList = append(pkgFileList, rest.ConvertToFileMetaData(dataRest))
	}
	return &model.Package{
		BaseEntityModel:       ConvertToBaseEntityModel(packageRest.BaseEntityRest),
		DisplayName:           packageRest.DisplayName,
		Description:           packageRest.Description,
		Os:                    osType,
		Arch:                  osArch,
		Version:               packageRest.Version,
		PkgType:               pkgType,
		PkgLocation:           pkgLocation,
		PkgFilePath:           rest.ConvertToFileMetaData(packageRest.PkgFilePath),
		PkgFilePathList:       pkgFileList,
		InstallCommand:        packageRest.InstallCommand,
		UninstallCommand:      packageRest.UninstallCommand,
		UpgradeCommand:        packageRest.UpgradeCommand,
		IconFile:              rest.ConvertToFileMetaData(packageRest.IconFile),
		SelfServiceSupported:  packageRest.SelfServiceSupported,
		UseUserDefinedCommand: packageRest.UseUserDefinedCommand,
		Tags:                  packageRest.Tags,
	}, nil
}

func (service PackageService) convertToRest(packageInfo model.Package) rest.PackageRest {
	var pkgFileDataRest []rest.FileMetaDataRest
	for _, dataRest := range packageInfo.PkgFilePathList {
		pkgFileDataRest = append(pkgFileDataRest, rest.ConvertToFileMetaDataRest(dataRest))
	}
	return rest.PackageRest{
		BaseEntityRest:        ConvertToBaseEntityRest(packageInfo.BaseEntityModel),
		DisplayName:           packageInfo.DisplayName,
		Description:           packageInfo.Description,
		Os:                    packageInfo.Os.String(),
		OsArch:                packageInfo.Arch.String(),
		Version:               packageInfo.Version,
		PkgType:               packageInfo.PkgType.String(),
		PkgLocation:           packageInfo.PkgLocation.String(),
		PkgFilePath:           rest.ConvertToFileMetaDataRest(packageInfo.PkgFilePath),
		PkgFilePathList:       pkgFileDataRest,
		InstallCommand:        packageInfo.InstallCommand,
		UninstallCommand:      packageInfo.UninstallCommand,
		UpgradeCommand:        packageInfo.UpgradeCommand,
		IconFile:              rest.ConvertToFileMetaDataRest(packageInfo.IconFile),
		SelfServiceSupported:  packageInfo.SelfServiceSupported,
		UseUserDefinedCommand: packageInfo.UseUserDefinedCommand,
		Tags:                  packageInfo.Tags,
	}
}

func (service PackageService) BulkCreate(restList []rest.PackageRest) {
	if len(restList) > 0 {
		for _, packageRest := range restList {
			service.Create(packageRest)
		}
	}
}

func (service PackageService) Create(rest rest.PackageRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to create package"))
	customErr := service.BeforeCreate(rest)
	if customErr.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while before creating package ,Error : %s ", customErr.Message))
		return 0, customErr
	}

	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()

	pkg, err := service.convertToModel(rest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating package ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(pkg)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating package ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	go service.AfterCreate(pkg)

	logger.ServiceLogger.Info(fmt.Sprintf("Create package process completed successfully"))
	return id, customErr
}

func (service PackageService) Update(id int64, restModel rest.PackageRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update package with id - %v", id))
	pkg, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating package for id - %v ,Error : %s ", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	customErr := service.BeforeUpdate(pkg, restModel)
	if customErr.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating package for id - %v ,Error : %s ", id, customErr.Message))
		return false, customErr
	}

	pkg.UpdatedTime = common.CurrentMillisecond()
	pkg.UpdatedById = common.GetUserFromCallContext()
	diffMap, isUpdatable := service.performPartialUpdate(&pkg, restModel)
	if isUpdatable {
		if restModel.PatchMap["pkgLocation"] != nil || restModel.PatchMap["pkgFilePath"] != nil || restModel.PatchMap["pkgType"] != nil {
			customError, isSuccess := DownloadFileBeforeUpdatingPkg(pkg, restModel)
			if !isSuccess {
				logger.ServiceLogger.Error(fmt.Sprintf("Error while updating package for id - %v ,Error : %s ", id, customErr.Message))
				return false, customError
			}
		}

		_, err := service.Repository.Update(&pkg)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating package for id - %v ,Error : %s ", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		service.AfterUpdate(diffMap, pkg)
		logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update package with id - %v", id))
		return true, common.CustomError{}
	} else {
		logger.ServiceLogger.Info(fmt.Sprintf("No fields need to updated"))
		logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update package with id - %v", id))
		return isUpdatable, customErr
	}
}

func (service PackageService) GetPackage(id int64, includeArchive bool) (rest.PackageRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Package for id %v", id))
	var pkgRest rest.PackageRest
	pkg, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get package for id - %v, Error : %s ", id, err.Error()))
		return pkgRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get Package for id %v", id))
	return service.convertToRest(pkg), nil
}

func (service PackageService) DeletePackage(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info("Process started to delete package for id - ", id)
	pkg, err := service.Repository.GetById(id, permanentDelete)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get package to delete for id - %v, Error : %s ", id, err.Error()))
		return false, err
	}

	success := false
	if permanentDelete {
		success, err = service.Repository.PermanentDeleteById(id)
	} else {
		success, err = service.Repository.DeleteById(id)
	}

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while delete package for id - %v, Error : %s ", id, err.Error()))
		return success, err
	}

	service.AfterDelete(pkg, permanentDelete)

	logger.ServiceLogger.Info("Process Completed to delete package for id - ", id)
	return true, nil
}

func (service PackageService) performPartialUpdate(model *model.Package, restModel rest.PackageRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&model.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.PatchMap["description"] != nil && model.Description != restModel.Description {
		common.PrepareInDiffMap("description", model.Description, restModel.Description, &diffMap)
		model.Description = restModel.Description
	}

	if restModel.PatchMap["version"] != nil && model.Version != restModel.Version {
		common.PrepareInDiffMap("version", model.Version, restModel.Version, &diffMap)
		model.Version = restModel.Version
	}

	if restModel.PatchMap["displayName"] != nil && model.DisplayName != restModel.DisplayName {
		common.PrepareInDiffMap("display_name", model.DisplayName, restModel.DisplayName, &diffMap)
		model.DisplayName = restModel.DisplayName
	}

	if restModel.PatchMap["pkgType"] != nil {
		var pkgType common.PkgType
		var err error
		pkgType, err = pkgType.ToPkgType(restModel.PkgType)
		if err == nil && model.PkgType != pkgType {
			common.PrepareInDiffMap("pkg_type", model.PkgType.String(), restModel.PkgType, &diffMap)
			model.PkgType = pkgType
		}
	}

	if restModel.PatchMap["pkgLocation"] != nil {
		var pkgLocation common.PkgLocation
		var err error
		pkgLocation, err = pkgLocation.ToPkgLocation(restModel.PkgLocation)
		if err == nil && model.PkgLocation != pkgLocation {
			common.PrepareInDiffMap("pkg_location", model.PkgLocation.String(), restModel.PkgLocation, &diffMap)
			model.PkgLocation = pkgLocation
		}
	}

	if restModel.PatchMap["pkgFilePath"] != nil {
		common.PrepareInDiffMap("pkg_file_path", model.PkgFilePath, restModel.PkgFilePath, &diffMap)
		model.PkgFilePath = rest.ConvertToFileMetaData(restModel.PkgFilePath)
	}

	if restModel.PatchMap["pkgFilePathList"] != nil {
		common.PrepareInDiffMap("pkg_file_path_list", model.PkgFilePathList, restModel.PkgFilePathList, &diffMap)
		model.PkgFilePathList = rest.ConvertToFileMetaDataList(restModel.PkgFilePathList)
	}

	if restModel.PatchMap["installCommand"] != nil && model.InstallCommand != restModel.InstallCommand {
		common.PrepareInDiffMap("install_command", model.InstallCommand, restModel.InstallCommand, &diffMap)
		model.InstallCommand = restModel.InstallCommand
	}

	if restModel.PatchMap["uninstallCommand"] != nil && model.UninstallCommand != restModel.UninstallCommand {
		common.PrepareInDiffMap("uninstall_command", model.UninstallCommand, restModel.UninstallCommand, &diffMap)
		model.UninstallCommand = restModel.UninstallCommand
	}

	if restModel.PatchMap["upgradeCommand"] != nil && model.UpgradeCommand != restModel.UpgradeCommand {
		common.PrepareInDiffMap("upgrade_command", model.UpgradeCommand, restModel.UpgradeCommand, &diffMap)
		model.UpgradeCommand = restModel.UpgradeCommand
	}

	if _, ok := restModel.PatchMap["iconFile"]; ok && restModel.IconFile.RefName != model.IconFile.RefName {
		common.PrepareInDiffMap("icon_file", model.IconFile, restModel.IconFile, &diffMap)
		model.IconFile = rest.ConvertToFileMetaData(restModel.IconFile)
	}

	if restModel.PatchMap["selfServiceSupported"] != nil && model.SelfServiceSupported != restModel.SelfServiceSupported {
		common.PrepareInDiffMap("self_service_supported", strconv.FormatBool(model.SelfServiceSupported), strconv.FormatBool(restModel.SelfServiceSupported), &diffMap)
		model.SelfServiceSupported = restModel.SelfServiceSupported
	}

	if restModel.PatchMap["useUserDefinedCommand"] != nil && model.UseUserDefinedCommand != restModel.UseUserDefinedCommand {
		common.PrepareInDiffMap("use_user_defined_command", strconv.FormatBool(model.UseUserDefinedCommand), strconv.FormatBool(restModel.UseUserDefinedCommand), &diffMap)
		model.UseUserDefinedCommand = restModel.UseUserDefinedCommand
	}

	if restModel.PatchMap["tags"] != nil && !reflect.DeepEqual(model.Tags, restModel.Tags) {
		common.PrepareInDiffMap("tags", model.Tags, restModel.Tags, &diffMap)
		model.Tags = restModel.Tags
	}

	return diffMap, len(diffMap) != 0
}

func (service PackageService) BeforeCreate(pkgRest rest.PackageRest) common.CustomError {
	if "" == pkgRest.Os || "" == pkgRest.PkgType || "" == pkgRest.PkgLocation || "" == pkgRest.OsArch {
		return common.Error("Incompatible Package data", http.StatusInternalServerError)
	}

	if "exe" == pkgRest.PkgType || "msi" == pkgRest.PkgType {
		if "windows" != pkgRest.Os {
			return common.Error(fmt.Sprintf("Incompatible Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	} else if "application" == pkgRest.PkgType {
		if !("linux" == pkgRest.Os || "mac" == pkgRest.Os) {
			return common.Error(fmt.Sprintf("Incompatible Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	}

	if "script" == pkgRest.PkgType {
		fileDataRest, err := NewFileDataService().GetFileDataByFileRefName(pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error("File not found", http.StatusInternalServerError)
		}
		if "windows" == pkgRest.Os &&
			!(strings.Contains(fileDataRest.RealName, ".bat") ||
				strings.Contains(fileDataRest.RealName, ".vbs") ||
				strings.Contains(fileDataRest.RealName, ".reg") ||
				strings.Contains(fileDataRest.RealName, ".ps")) {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if ("linux" == pkgRest.Os || "mac" == pkgRest.Os) &&
			!strings.Contains(fileDataRest.RealName, ".sh") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	}

	if "shared_dir" == pkgRest.PkgLocation {
		expectedPattern := `^\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\.\w+$`

		_, err := regexp.MatchString(expectedPattern, pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error(fmt.Sprintf("Incompatible Package File path '%s', for Package location %s. Expected File path : //192.168.1.10/Test/test.pdf", pkgRest.PkgFilePath, pkgRest.PkgLocation), http.StatusInternalServerError)
		}
	}

	if "local_dir" == pkgRest.PkgLocation {
		fileDataRest, err := NewFileDataService().GetFileDataByFileRefName(pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error("File not found", http.StatusInternalServerError)
		}
		if "exe" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".exe") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if "msi" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".msi") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if "zip" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".zip") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	}
	customErr, isFileDownloaded := DownloadFileBeforeCreatingPkg(&pkgRest)
	if !isFileDownloaded {
		return customErr
	}
	return common.CustomError{}
}

func (service PackageService) BeforeUpdate(pkg model.Package, pkgRest rest.PackageRest) common.CustomError {
	if pkgRest.PatchMap["os"] != nil && "" == pkgRest.Os {
		return common.Error("Invalid request to update OS field in package data", http.StatusInternalServerError)
	}

	if pkgRest.PatchMap["pkgType"] != nil {
		if "exe" == pkgRest.PkgType || "msi" == pkgRest.PkgType {
			if common.Windows != pkg.Os {
				return common.Error(fmt.Sprintf("Incompatible Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
			}
		} else if "application" == pkgRest.PkgType {
			if !(common.Linux == pkg.Os || common.MacOS == pkg.Os) {
				return common.Error(fmt.Sprintf("Incompatible Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
			}
		}
	}

	if pkgRest.PatchMap["pkgLocation"] != nil {
		if pkgRest.PkgLocation == "shareddir" {
			filePath := pkg.PkgFilePath.RefName
			if pkgRest.PatchMap["pkgFilePath"] != nil {
				filePath = pkgRest.PkgFilePath.RefName
			}
			expectedPattern := `^\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\.\w+$`
			_, err := regexp.MatchString(expectedPattern, filePath)
			if err != nil {
				return common.Error(fmt.Sprintf("Incompatible Package File path '%s', for Package location %s. Expected File path : //192.168.1.10/Test/test.pdf", pkgRest.PkgFilePath, pkgRest.PkgLocation), http.StatusInternalServerError)
			}
		}
	}

	if "local_dir" == pkgRest.PkgLocation {
		fileDataRest, err := NewFileDataService().GetFileDataByFileRefName(pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error("File not found", http.StatusInternalServerError)
		}
		if "exe" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".exe") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if "msi" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".msi") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if "zip" == pkgRest.PkgType && !strings.Contains(fileDataRest.RealName, ".zip") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	}

	if "script" == pkgRest.PkgType {
		fileDataRest, err := NewFileDataService().GetFileDataByFileRefName(pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error("File not found", http.StatusInternalServerError)
		}
		if "windows" == pkgRest.Os &&
			!(strings.Contains(fileDataRest.RealName, ".bat") ||
				strings.Contains(fileDataRest.RealName, ".vbs") ||
				strings.Contains(fileDataRest.RealName, ".reg") ||
				strings.Contains(fileDataRest.RealName, ".ps")) {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		} else if ("linux" == pkgRest.Os || "mac" == pkgRest.Os) &&
			!strings.Contains(fileDataRest.RealName, ".sh") {
			return common.Error(fmt.Sprintf("Incompatible File with Package type '%s', for os type %s", pkgRest.PkgType, pkgRest.Os), http.StatusInternalServerError)
		}
	}

	if "shared_dir" == pkgRest.PkgLocation {
		expectedPattern := `^\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\.\w+$`

		_, err := regexp.MatchString(expectedPattern, pkgRest.PkgFilePath.RefName)
		if err != nil {
			return common.Error(fmt.Sprintf("Incompatible Package File path '%s', for Package location %s. Expected File path : //192.168.1.10/Test/test.pdf", pkgRest.PkgFilePath, pkgRest.PkgLocation), http.StatusInternalServerError)
		}
	}
	return common.CustomError{}
}

func (service PackageService) GetAllPackage(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, "packages", true, "")
	var responsePage rest.ListResponseRest
	var packagePageList []model.Package
	var err error
	count := service.Repository.Count(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, "packages", false, "")
		packagePageList, err = service.Repository.GetAllPackages(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func DownloadFileBeforeCreatingPkg(pkgRest *rest.PackageRest) (common.CustomError, bool) {
	fileService := NewFileDataService()
	if pkgRest.PatchMap["pkgLocation"] != nil || pkgRest.PatchMap["pkgFilePath"] != nil {
		if "public_url" == pkgRest.PkgLocation {
			fileRefName := uuid.New().String()
			fileDataRest, customErr := fileService.DownloadFileFromURL(pkgRest.PkgFilePath.RefName, fileRefName, pkgRest.PkgType, "")
			if fileDataRest.RefName == "" {
				return customErr, false
			}
			_, err := fileService.CreateFileData(fileDataRest)
			if err != nil {
				customError := common.Error(err.Error(), http.StatusInternalServerError)
				return customError, false
			}
			pkgRest.PkgFilePath.RefName = fileDataRest.RefName
			pkgRest.PkgFilePath.RealName = fileDataRest.RealName
		} else if "shared_dir" == pkgRest.PkgLocation {
			fileRefName := uuid.New().String()
			fileDataRest, customErr := fileService.DownloadFileFromSMBPath(fileRefName, pkgRest.PkgFilePath.RefName)
			if fileDataRest.RefName == "" {
				return customErr, false
			}
			_, err := fileService.CreateFileData(fileDataRest)
			if err != nil {
				customError := common.Error(err.Error(), http.StatusInternalServerError)
				return customError, false
			}
			pkgRest.PkgFilePath.RefName = fileDataRest.RefName
			pkgRest.PkgFilePath.RealName = fileDataRest.RealName
		}
	}
	return common.CustomError{}, true
}

func DownloadFileBeforeUpdatingPkg(pkg model.Package, pkgRest rest.PackageRest) (common.CustomError, bool) {
	fileService := NewFileDataService()
	shouldDownloadPkg := false
	location := pkg.PkgLocation
	filePath := pkg.PkgFilePath
	fileType := pkg.PkgType
	if pkgRest.PatchMap["pkgLocation"] != nil {
		if pkgRest.PatchMap["pkgFilePath"] == nil {
			customError := common.Error("Error while downloading package : Incompatible file location and file path ", http.StatusInternalServerError)
			return customError, false
		}
		var pkgLocation common.PkgLocation
		pkgLocation, err := pkgLocation.ToPkgLocation(pkgRest.PkgLocation)
		if err == nil {
			location = pkgLocation
			shouldDownloadPkg = true
		}
	}
	if pkgRest.PatchMap["pkgType"] != nil {
		if pkgRest.PatchMap["pkgFilePath"] == nil {
			customError := common.Error("Error while downloading package : Incompatible file location and file path and pkg type ", http.StatusInternalServerError)
			return customError, false
		}
		var pkgType common.PkgType
		pkgType, err := pkgType.ToPkgType(pkgRest.PkgType)
		if err == nil {
			fileType = pkgType
			shouldDownloadPkg = true
		}
	}
	if pkgRest.PatchMap["pkgFilePath"] != nil {
		filePath = rest.ConvertToFileMetaData(pkgRest.PkgFilePath)
		shouldDownloadPkg = true
	}

	if shouldDownloadPkg {
		if common.PublicURL == location {
			fileRefName := uuid.New().String()
			fileDataRest, customErr := fileService.DownloadFileFromURL(filePath.RefName, fileRefName, fileType.String(), "")
			if fileDataRest.RefName == "" {
				return customErr, false
			}
			_, err := fileService.CreateFileData(fileDataRest)
			if err != nil {
				customError := common.Error("Error while downloading package : "+err.Error(), http.StatusInternalServerError)
				return customError, false
			}
			pkgRest.PkgFilePath.RefName = fileDataRest.RefName
			pkgRest.PkgFilePath.RealName = fileDataRest.RealName
		} else if common.SharedDir == location {
			fileRefName := uuid.New().String()
			fileDataRest, customErr := fileService.DownloadFileFromSMBPath(fileRefName, filePath.RefName)
			if fileDataRest.RefName == "" {
				return customErr, false
			}
			_, err := fileService.CreateFileData(fileDataRest)
			if err != nil {
				customError := common.Error("Error while downloading package : "+err.Error(), http.StatusInternalServerError)
				return customError, false
			}
			pkgRest.PkgFilePath.RefName = fileDataRest.RefName
			pkgRest.PkgFilePath.RealName = fileDataRest.RealName
		}
	}

	return common.CustomError{}, true
}

func (service PackageService) convertListToRest(packages []model.Package) []rest.PackageRest {
	var packageRestList []rest.PackageRest
	if len(packages) != 0 {
		for _, pkg := range packages {
			packageRest := service.convertToRest(pkg)
			packageRestList = append(packageRestList, packageRest)
		}
	}
	return packageRestList
}

func (service PackageService) AfterCreate(pkg *model.Package) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for pkg id : ", pkg.Id)
		}
	}()

	pkg.Name = fmt.Sprintf("SWP-%03d", pkg.Id)
	_, err := service.Repository.Update(pkg)
	if err != nil {
		logger.ServiceLogger.Error("[PackageService][AfterCreate]", err)
	}

	go service.CreateAudit(pkg)
}

func (service PackageService) CreateAudit(pkg *model.Package) {
	auditString := fmt.Sprintf("Package is created with Id : %v, Name : %s, OS : %s, Package Type : %s, Package Location : %s.",
		pkg.Name, pkg.DisplayName, pkg.Os.String(), pkg.PkgType.String(), pkg.PkgLocation.String())
	auditModel := common.PACKAGE.String()
	audit := rest.CreateAuditRest(auditString, auditModel, pkg.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[PackageService][CreateAudit]", err)
	}
}

func (service PackageService) AfterUpdate(diffMap map[string]map[string]interface{}, pkg model.Package) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after update process for pkg id : ", pkg.Id)
		}
	}()

	auditString := fmt.Sprintf("Package is Updated for Package id - %v -> ", pkg.Name)
	auditString = service.generateUpdateAuditString(auditString, diffMap)
	auditModel := common.PACKAGE.String()
	audit := rest.UpdateAuditRest(auditString, auditModel, pkg.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[PackageService][AfterUpdate]", err)
	}
}

func (service PackageService) AfterDelete(pkg model.Package, permanentDelete bool) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after delete process for pkg id : ", pkg.Id)
		}
	}()
	auditString := "Package "
	if permanentDelete {
		auditString = "is Deleted "
	} else {
		auditString = "is Archived "
	}
	auditString = fmt.Sprintf("-> Id : %v, Name : %s. ", pkg.Name, pkg.DisplayName)
	auditModel := common.PACKAGE.String()
	audit := rest.DeleteAuditRest(auditString, auditModel, pkg.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[PackageService][AfterDelete]", err)
	}
}

func (service PackageService) generateUpdateAuditString(auditString string, diffMap map[string]map[string]interface{}) string {
	if len(diffMap) > 0 {
		for key, innerMap := range diffMap {
			switch key {
			case "display_name":
				auditString += common.UpdateAuditStringWithFromTo("Display Name", innerMap)
				break
			case "description":
				auditString += common.UpdateAuditStringWithFromTo("Description", innerMap)
				break
			case "os":
				auditString += common.UpdateAuditStringWithFromTo("OS", innerMap)
				break
			case "os_arch":
				auditString += common.UpdateAuditStringWithFromTo("OS Architecture", innerMap)
				break
			case "version":
				auditString += common.UpdateAuditStringWithFromTo("Version", innerMap)
				break
			case "pkg_type":
				auditString += common.UpdateAuditStringWithFromTo("Package Type", innerMap)
				break
			case "pkg_location":
				auditString += common.UpdateAuditStringWithFromTo("Package Location", innerMap)
				break
			case "pkg_file_path":
				auditString += "Package file is updated, "
				break
			case "install_command":
				auditString += common.UpdateAuditStringWithFromTo("Install Command", innerMap)
				break
			case "uninstall_command":
				auditString += common.UpdateAuditStringWithFromTo("Uninstall Command", innerMap)
				break
			case "upgrade_command":
				auditString += common.UpdateAuditStringWithFromTo("Upgrade Command", innerMap)
				break
			case "deployment_policy_id":
				auditString += "Deployment Policy is updated, "
				break
			case "self_service_supported":
				auditString += fmt.Sprintf("SelfServiceSupported is updated as %s, ", innerMap["newvalue"])
				break
			case "use_user_defined_command":
				auditString += fmt.Sprintf("UseUserDefinedCommand is updated as %s, ", innerMap["newvalue"])
				break
			}
		}
	}
	auditString = auditString[:len(auditString)-2] + "."
	return auditString
}

func (service PackageService) CreateOOBPackages() {
	filter := rest.SearchFilter{
		Offset:          0,
		Size:            1,
		Qualification:   []rest.Qualification{},
		IncludeArchived: true,
	}
	// Use secure parameterized queries to prevent SQL injection
	queryResult := rest.PrepareSecureQueryFromSearchFilter(filter, "packages", true, "")
	count := service.Repository.Count(queryResult.Query, queryResult.Parameters)
	if count == 0 {
		var pkgRestList []rest.PackageRest

		packageRest := service._7zipPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.fileZillaPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.fireFoxPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.fireFoxMacPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.chromePkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.microsoftTeamsPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.notepadPlusPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.office365PkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.office365MacPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.slackPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.vlcPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.vlcMacPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.winRarPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		packageRest = service.zoomPkgRest()
		pkgRestList = append(pkgRestList, packageRest)

		service.BulkCreate(pkgRestList)
	}
}

func (service PackageService) slackPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("slack.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c967")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Slack Windows",
		},
		DisplayName: "Slack Windows",
		Description: "Install Slack for Window 64 bit",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "1",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://downloads.slack-edge.com/desktop-releases/windows/x64/4.37.101/SlackSetup.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "slack.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c967",
		},
		InstallCommand:        "SlackSetup.exe /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) office365MacPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("office365.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c966")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "O365 Mac",
		},
		DisplayName: "O365 Mac",
		Description: "Install Mac Office 365 Setup",
		Os:          "mac",
		OsArch:      "x64",
		Version:     "1",
		PkgType:     "application",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://officecdnmac.microsoft.com/pr/C1297A47-86C4-4C1F-97FA-950631F94777/MacAutoupdate/Microsoft_365_and_Office_16.84.24041420_Installer.pkg?culture=en-us&country=us",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "office365.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c966",
		},
	}
}

func (service PackageService) office365PkgRest() rest.PackageRest {
	service.addOOBPackageIcon("office365.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c966")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "O365 Windows",
		},
		DisplayName: "O365 Windows",
		Description: "Install Microsoft Office 365 Setup",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "1",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://c2rsetup.officeapps.live.com/c2r/download.aspx?ProductreleaseID=O365HomePremRetail&language=en-us&platform=def&version=O16GA&source=MktDownloadForWinPage",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "office365.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c966",
		},
		InstallCommand:        "\"OfficeSetup.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) winRarPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("winrar.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c911")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "WinRAR",
		},
		DisplayName: "WinRAR",
		Description: "Install winrar x64 700",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "700",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://www.win-rar.com/fileadmin/winrar-versions/winrar/winrar-x64-700.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "winrar.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c911",
		},
		InstallCommand:        "\"winrar-x64-624.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) zoomPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("zoom.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c912")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Zoom desktop client",
		},
		DisplayName: "Zoom desktop client",
		Description: "Install Zoom desktop client for Meetings x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "latest",
		PkgType:     "msi",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://zoom.us/client/latest/ZoomInstallerFull.msi?_ga=2.18235069.529033726.1713155667-2128129654.1713155667",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "zoom.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c912",
		},
		InstallCommand:        "ZoomInstallerFull.msi /qn /norestart ALLUSERS=1",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) vlcPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("vlc.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c969")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "VLC",
		},
		DisplayName: "VLC",
		Description: "Install VLC 3.0.20 x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "3.0.20",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://get.videolan.org/vlc/3.0.20/win64/vlc-3.0.20-win64.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "vlc.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c969",
		},
		InstallCommand:        "\"vlc-3.0.20-win64.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) vlcMacPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("vlc.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c969")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "VLC For Mac",
		},
		DisplayName: "VLC For Mac",
		Description: "Install VLC 3.0.20",
		Os:          "mac",
		OsArch:      "x64",
		Version:     "3.0.20",
		PkgType:     "application",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://get.videolan.org/vlc/3.0.20/macosx/vlc-3.0.20-arm64.dmg",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "vlc.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c969",
		},
	}
}

func (service PackageService) notepadPlusPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("notepad.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c965")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Notepad++",
		},
		DisplayName: "Notepad++",
		Description: "Install Notepad++ v8.6.4 x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "8.6.4",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.6.4/npp.8.6.4.Installer.x64.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "notepad.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c965",
		},
		InstallCommand:        "\"npp.8.6.4.Installer.x64.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) microsoftTeamsPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("teams.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c968")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Microsoft Teams",
		},
		DisplayName: "Microsoft Teams",
		Description: "Install Microsoft Teams x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "latest",
		PkgType:     "msi",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://teams.microsoft.com/downloads/desktopurl?env=production&plat=windows&arch=x64&managedInstaller=true&download=true",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "teams.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c968",
		},
		InstallCommand:        "Teams_windows_x64.msi /quiet /norestart ALLUSERS=1",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) chromePkgRest() rest.PackageRest {
	service.addOOBPackageIcon("chrome.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c962")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Google Chrome",
		},
		DisplayName: "Google Chrome",
		Description: "Install Google Chrome Enterprise  x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "latest",
		PkgType:     "msi",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://dl.google.com/edgedl/chrome/install/GoogleChromeStandaloneEnterprise64.msi",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "chrome.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c962",
		},
		InstallCommand:        "googlechromestandaloneenterprise64.msi /qn /norestart",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) fireFoxMacPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("firefox.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c964")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Firefox For Mac",
		},
		DisplayName: "Firefox For Mac",
		Description: "Install Firefox 125.0.1",
		Os:          "mac",
		OsArch:      "x64",
		Version:     "125.0.1",
		PkgType:     "application",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://ftp.mozilla.org/pub/firefox/releases/125.0.1/mac/en-US/Firefox 125.0.1.dmg",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "firefox.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c964",
		},
	}
}

func (service PackageService) fireFoxPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("firefox.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c964")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "Firefox",
		},
		DisplayName: "Firefox",
		Description: "Install Firefox 125.0.1  x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "125.0.1",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://ftp.mozilla.org/pub/firefox/releases/125.0.1/win64/en-US/Firefox Setup 125.0.1.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "firefox.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c964",
		},
		InstallCommand:        "\"Firefox Setup 125.0.1.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) fileZillaPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("filezilla.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c963")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "FileZilla",
		},
		DisplayName: "FileZilla",
		Description: "Install FileZilla 3.65.0  x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "3.65.0",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://download.filezilla-project.org/client/FileZilla_3.66.5_win64_sponsored2-setup.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "filezilla.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c963",
		},
		InstallCommand:        "\"FileZilla_3.65.0_win64_sponsored2-setup.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) _7zipPkgRest() rest.PackageRest {
	service.addOOBPackageIcon("7zip.png", "7ac3a31d-d890-42de-88e7-8e0f1cc9c961")
	return rest.PackageRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name: "7-Zip",
		},
		DisplayName: "7-Zip",
		Description: "Install 7.zip 23.01  x64",
		Os:          "windows",
		OsArch:      "x64",
		Version:     "23.01",
		PkgType:     "exe",
		PkgLocation: "public_url",
		PkgFilePath: rest.FileMetaDataRest{
			RealName: "",
			RefName:  "https://7-zip.org/a/7z2301-x64.exe",
		},
		IconFile: rest.FileMetaDataRest{
			RealName: "7zip.png",
			RefName:  "7ac3a31d-d890-42de-88e7-8e0f1cc9c961",
		},
		InstallCommand:        "\"7z2301-x64.exe\" /S",
		UseUserDefinedCommand: true,
	}
}

func (service PackageService) addOOBPackageIcon(realName, refName string) {
	fileDataRest := rest.FileDataRest{
		RealName: realName,
		RefName:  refName,
	}
	_, err := NewFileDataService().CreateFileData(fileDataRest)
	if err != nil {
		logger.ServiceLogger.Error("[PackageService][addOOBPackageIcon]", err)
	}
}

func (service PackageService) GetTagFilterWithCount() map[string]int64 {
	response := map[string]int64{}
	tupleQual := rest.PrepareTupleQueryFromSearchFilter([]string{"tags"}, "packages")
	pkgList, err := service.Repository.GetAllPackage(tupleQual)
	if err == nil {
		for _, pkg := range pkgList {
			if len(pkg.Tags) > 0 {
				for _, tag := range pkg.Tags {
					if val, ok := response[tag]; ok {
						response[tag] = val + 1
					} else {
						response[tag] = 1
					}
				}
			}
		}
	}
	return response
}
