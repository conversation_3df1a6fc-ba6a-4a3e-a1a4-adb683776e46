package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type WindowsPatchService struct {
	Repository *repository.WindowsPatchRepository
}

func NewWindowsPatchService() *WindowsPatchService {
	return &WindowsPatchService{Repository: repository.NewWindowsPatchRepository()}
}

func (service WindowsPatchService) convertToModel(patchRest rest.WindowsPatchRest) (*model.WindowsPatch, error) {
	patchRest.Name = patchRest.UUID
	return &model.WindowsPatch{
		BaseEntityModel:                         ConvertToBaseEntityModel(patchRest.BaseEntityRest),
		TempData:                                patchRest.TempData,
		ReplaceById:                             patchRest.ReplaceById,
		ReplaceByUuid:                           patchRest.ReplaceByUuid,
		RevisionId:                              patchRest.RevisionId,
		UUID:                                    patchRest.UUID,
		Title:                                   patchRest.Title,
		KbId:                                    patchRest.KbId,
		Description:                             patchRest.Description,
		HasReplacement:                          patchRest.HasReplacement,
		Leaf:                                    patchRest.Leaf,
		Declined:                                patchRest.Declined,
		PublishState:                            patchRest.PublishState,
		UpdateType:                              patchRest.UpdateType,
		BulletinId:                              patchRest.BulletinId,
		InstallationCanRequestUserInput:         patchRest.InstallationCanRequestUserInput,
		InstallationRequiresNetworkConnectivity: patchRest.InstallationRequiresNetworkConnectivity,
		CanUninstall:                            patchRest.CanUninstall,
		InstallationImpact:                      patchRest.InstallationImpact,
		RestartBehaviour:                        patchRest.RestartBehaviour,
		SupportedLanguage:                       patchRest.SupportedLanguage,
		LastUpdatedTime:                         patchRest.LastUpdatedTime,
		WsusLastUpdatedTime:                     patchRest.WsusLastUpdatedTime,
		FileDetails:                             patchRest.FileDetails,
		SupersedesString:                        patchRest.SupersedesString,
		Company:                                 patchRest.Company,
		ProductFamily:                           patchRest.ProductFamily,
		Products:                                patchRest.Products,
		ProductsUuid:                            patchRest.ProductsUuid,
		Classification:                          patchRest.Classification,
		CveNumber:                               patchRest.CveNumber,
		Severity:                                patchRest.Severity,
		MoreInfoUrl:                             patchRest.MoreInfoUrl,
		DownloadUrl:                             patchRest.DownloadUrl,
		ReleaseDate:                             patchRest.ReleaseDate,
		Arch:                                    patchRest.Arch,
		OsName:                                  patchRest.OsName,
		OsArch:                                  patchRest.OsArch,
		ProductId:                               patchRest.ProductId,
		MsrcPatchId:                             patchRest.MsrcPatchId,
		AffectedProduct:                         patchRest.AffectedProduct,
		Rules:                                   patchRest.Rules,
		KbIdToBeInstalled:                       patchRest.KbIdToBeInstalled,
		CabExist:                                patchRest.CabExist,
		AtLeastOneFileInstallation:              patchRest.AtLeastOneFileInstallation,
		PackageNames:                            patchRest.PackageNames,
	}, nil
}

func (service WindowsPatchService) convertToRest(patch model.WindowsPatch) rest.WindowsPatchRest {
	return rest.WindowsPatchRest{
		BaseEntityRest:                          ConvertToBaseEntityRest(patch.BaseEntityModel),
		TempData:                                patch.TempData,
		ReplaceById:                             patch.ReplaceById,
		ReplaceByUuid:                           patch.ReplaceByUuid,
		RevisionId:                              patch.RevisionId,
		UUID:                                    patch.UUID,
		Title:                                   patch.Title,
		KbId:                                    patch.KbId,
		Description:                             patch.Description,
		HasReplacement:                          patch.HasReplacement,
		Leaf:                                    patch.Leaf,
		Declined:                                patch.Declined,
		PublishState:                            patch.PublishState,
		UpdateType:                              patch.UpdateType,
		BulletinId:                              patch.BulletinId,
		InstallationCanRequestUserInput:         patch.InstallationCanRequestUserInput,
		InstallationRequiresNetworkConnectivity: patch.InstallationRequiresNetworkConnectivity,
		CanUninstall:                            patch.CanUninstall,
		InstallationImpact:                      patch.InstallationImpact,
		RestartBehaviour:                        patch.RestartBehaviour,
		SupportedLanguage:                       patch.SupportedLanguage,
		LastUpdatedTime:                         patch.LastUpdatedTime,
		WsusLastUpdatedTime:                     patch.WsusLastUpdatedTime,
		FileDetails:                             patch.FileDetails,
		SupersedesString:                        patch.SupersedesString,
		Company:                                 patch.Company,
		ProductFamily:                           patch.ProductFamily,
		Products:                                patch.Products,
		ProductsUuid:                            patch.ProductsUuid,
		Classification:                          patch.Classification,
		CveNumber:                               patch.CveNumber,
		Severity:                                patch.Severity,
		MoreInfoUrl:                             patch.MoreInfoUrl,
		DownloadUrl:                             patch.DownloadUrl,
		ReleaseDate:                             patch.ReleaseDate,
		Arch:                                    patch.Arch,
		OsName:                                  patch.OsName,
		OsArch:                                  patch.OsArch,
		ProductId:                               patch.ProductId,
		MsrcPatchId:                             patch.MsrcPatchId,
		AffectedProduct:                         patch.AffectedProduct,
		Rules:                                   patch.Rules,
		KbIdToBeInstalled:                       patch.KbIdToBeInstalled,
		CabExist:                                patch.CabExist,
		AtLeastOneFileInstallation:              patch.AtLeastOneFileInstallation,
		PackageNames:                            patch.PackageNames,
	}
}

func (service WindowsPatchService) BeforeCreate(patchRest rest.WindowsPatchRest) common.CustomError {
	patch, err := service.Repository.GetByUuid(patchRest.UUID)
	if err == nil && patch.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service WindowsPatchService) Create(patchRest rest.WindowsPatchRest) (int64, common.CustomError) {
	logger.ServiceLogger.Debug("Process started to create Windows patch : ", patchRest.UUID)
	if patchRest.CreatedTime == 0 {
		patchRest.CreatedTime = common.CurrentMillisecond()
	}
	patchRest.CreatedById = common.GetUserFromCallContext()
	windowsPatch, err := service.convertToModel(patchRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Windows patch: %s : %s", patchRest.UUID, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(windowsPatch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Windows patch: %s : %s", patchRest.UUID, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Debug("Windows patch ", patchRest.UUID, " created successfully.")
	return id, common.CustomError{}
}

func (service WindowsPatchService) Update(id int64, restModel rest.WindowsPatchRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update Windows patch with id - %v", id))
	windowsPatch, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Windows patch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	if restModel.UpdatedTime == 0 {
		windowsPatch.UpdatedTime = common.CurrentMillisecond()
	} else {
		windowsPatch.UpdatedTime = restModel.UpdatedTime
	}

	windowsPatch.UpdatedById = common.GetUserFromCallContext()

	service.PerformPartialUpdate(restModel, windowsPatch)

	_, err = service.Repository.Update(&windowsPatch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Windows patch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update Windows patch with id - %v", id))
	return true, common.CustomError{}
}

func (service WindowsPatchService) PerformPartialUpdate(restModel rest.WindowsPatchRest, windowsPatch model.WindowsPatch) {
	// Update fields if they are not empty or zero
	if restModel.UUID != "" {
		windowsPatch.UUID = restModel.UUID
	}
	if restModel.Title != "" {
		windowsPatch.Title = restModel.Title
	}
	if restModel.KbId != "" {
		windowsPatch.KbId = restModel.KbId
	}
	if restModel.Description != "" {
		windowsPatch.Description = restModel.Description
	}
	if restModel.PublishState != "" {
		windowsPatch.PublishState = restModel.PublishState
	}
	if restModel.UpdateType != "" {
		windowsPatch.UpdateType = restModel.UpdateType
	}
	if restModel.BulletinId != "" {
		windowsPatch.BulletinId = restModel.BulletinId
	}
	if restModel.InstallationImpact != "" {
		windowsPatch.InstallationImpact = restModel.InstallationImpact
	}
	if restModel.RestartBehaviour != "" {
		windowsPatch.RestartBehaviour = restModel.RestartBehaviour
	}
	if restModel.SupportedLanguage != "" {
		windowsPatch.SupportedLanguage = restModel.SupportedLanguage
	}
	if restModel.SupersedesString != "" {
		windowsPatch.SupersedesString = restModel.SupersedesString
	}
	if restModel.Company != "" {
		windowsPatch.Company = restModel.Company
	}
	if restModel.ProductFamily != "" {
		windowsPatch.ProductFamily = restModel.ProductFamily
	}
	if restModel.Products != "" {
		windowsPatch.Products = restModel.Products
	}
	if restModel.Classification != "" {
		windowsPatch.Classification = restModel.Classification
	}
	if restModel.CveNumber != "" {
		windowsPatch.CveNumber = restModel.CveNumber
	}
	if restModel.Severity != "" {
		windowsPatch.Severity = restModel.Severity
	}
	if restModel.MoreInfoUrl != "" {
		windowsPatch.MoreInfoUrl = restModel.MoreInfoUrl
	}
	if restModel.Arch != "" {
		windowsPatch.Arch = restModel.Arch
	}
	if restModel.OsName != "" {
		windowsPatch.OsName = restModel.OsName
	}
	if restModel.OsArch != "" {
		windowsPatch.OsArch = restModel.OsArch
	}
	if restModel.KbIdToBeInstalled != "" {
		windowsPatch.KbIdToBeInstalled = restModel.KbIdToBeInstalled
	}

	windowsPatch.TempData = restModel.TempData
	windowsPatch.ReplaceById = restModel.ReplaceById
	windowsPatch.ReplaceByUuid = restModel.ReplaceByUuid
	windowsPatch.RevisionId = restModel.RevisionId
	windowsPatch.HasReplacement = restModel.HasReplacement
	windowsPatch.Leaf = restModel.Leaf
	windowsPatch.Declined = restModel.Declined
	windowsPatch.InstallationCanRequestUserInput = restModel.InstallationCanRequestUserInput
	windowsPatch.InstallationRequiresNetworkConnectivity = restModel.InstallationRequiresNetworkConnectivity
	windowsPatch.CanUninstall = restModel.CanUninstall
	windowsPatch.LastUpdatedTime = restModel.LastUpdatedTime
	windowsPatch.WsusLastUpdatedTime = restModel.WsusLastUpdatedTime
	windowsPatch.FileDetails = restModel.FileDetails
	windowsPatch.ProductsUuid = restModel.ProductsUuid
	windowsPatch.DownloadUrl = restModel.DownloadUrl
	windowsPatch.ReleaseDate = restModel.ReleaseDate
	windowsPatch.ProductId = restModel.ProductId
	windowsPatch.MsrcPatchId = restModel.MsrcPatchId
	windowsPatch.AffectedProduct = restModel.AffectedProduct
	windowsPatch.Rules = restModel.Rules
	windowsPatch.CabExist = restModel.CabExist
}

func (service WindowsPatchService) GetWindowsPatch(id int64, includeArchive bool) (rest.WindowsPatchRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Windows patch for id %v", id))
	var patchRest rest.WindowsPatchRest
	patch, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Windows patch for id - %v, Error: %s", id, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get Windows patch for id %v", id))
	return service.convertToRest(patch), nil
}

func (service WindowsPatchService) GetWindowsPatchByUuid(uuid string) (rest.WindowsPatchRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Windows patch for uuid %v", uuid))
	var patchRest rest.WindowsPatchRest
	patch, err := service.Repository.GetByUuid(uuid)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Windows patch for uuid - %v, Error: %s", uuid, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to get Windows patch for uuid %v", uuid))
	return service.convertToRest(patch), nil
}

func (service WindowsPatchService) DeleteWindowsPatch(id int64, permanentDelete bool) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete Windows patch for id - %v", id))
	_, err := service.Repository.GetById(id, permanentDelete)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting Windows patch to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.PermanentDeleteById(id)

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting Windows patch for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete Windows patch for id - %v", id))
	return true, nil
}

func (service WindowsPatchService) BulkCreateOrUpdate(restList []rest.WindowsPatchRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patch, err := service.Repository.GetByUuid(patchRest.UUID)
			if err == nil && patch.Id != 0 {
				service.Update(patch.Id, patchRest)
			} else {
				patchRest.Id = 0
				service.Create(patchRest)
			}
		}
	}
}

func (service WindowsPatchService) GetAllWindowsPatches(filter rest.SearchFilter) ([]rest.WindowsPatchRest, error) {
	var windowsPatchRests []rest.WindowsPatchRest
	var windowsPatches []model.WindowsPatch
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.WINDOW_PATCH.String(), false, "")
	windowsPatches, err = service.Repository.GetAllWindowsPatches(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return windowsPatchRests, err
	}
	windowsPatchRests = service.convertListToRest(windowsPatches)
	return windowsPatchRests, nil
}

func (service WindowsPatchService) GetAllWindowsPatch(filter rest.SearchFilter) ([]map[string]interface{}, error) {
	var result []map[string]interface{}
	var err error
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.WINDOW_PATCH.String(), false, "")
	result, err = service.Repository.GetAllWindowsPatch(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return result, err
	}
	return result, nil
}

func (service WindowsPatchService) convertListToRest(patchList []model.WindowsPatch) []rest.WindowsPatchRest {
	var restList []rest.WindowsPatchRest
	for _, patch := range patchList {
		restList = append(restList, service.convertToRest(patch))
	}
	return restList
}
