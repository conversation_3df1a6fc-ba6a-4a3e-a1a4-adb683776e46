package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	pch_model "deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/knqyf263/go-deb-version"
)

type UbuntuPatchService struct {
	Repository *repository.UbuntuPatchRepository
}

func NewUbuntuPatchService() *UbuntuPatchService {
	return &UbuntuPatchService{Repository: repository.NewUbuntuPatchRepository()}
}

var dependencyPackageRegex = regexp.MustCompile(`^([a-zA-Z0-9.+-]+)(?: \((>=|<=|=|<<|>>) ([^)]+)\))?$`)

func (service UbuntuPatchService) convertToModel(patchRest rest.UbuntuPatchRest) (*pch_model.UbuntuPatch, error) {
	return &pch_model.UbuntuPatch{
		BaseEntityModel:   ConvertToBaseEntityModel(patchRest.BaseEntityRest),
		UUID:              patchRest.UUID,
		OsVersion:         patchRest.OsVersion,
		Channel:           patchRest.Channel,
		Repo:              patchRest.Repo,
		PackageName:       patchRest.PackageName,
		Arch:              patchRest.Arch,
		Version:           patchRest.Version,
		Priority:          patchRest.Priority,
		Section:           patchRest.Section,
		Origin:            patchRest.Origin,
		Depends:           patchRest.Depends,
		Breaks:            patchRest.Breaks,
		FileName:          patchRest.FileName,
		DownloadUrl:       patchRest.DownloadUrl,
		Size:              patchRest.Size,
		Sha1:              patchRest.Sha1,
		PkgAndVersion:     patchRest.PkgAndVersion,
		PkgNameWithDistro: patchRest.PkgNameWithDistro,
		Downloadable:      patchRest.Downloadable,
		ReleaseDate:       patchRest.ReleaseDate,
	}, nil
}

func (service UbuntuPatchService) convertToRest(patch pch_model.UbuntuPatch) rest.UbuntuPatchRest {
	return rest.UbuntuPatchRest{
		BaseEntityRest:    ConvertToBaseEntityRest(patch.BaseEntityModel),
		UUID:              patch.UUID,
		OsVersion:         patch.OsVersion,
		Channel:           patch.Channel,
		Repo:              patch.Repo,
		PackageName:       patch.PackageName,
		Arch:              patch.Arch,
		Version:           patch.Version,
		Priority:          patch.Priority,
		Section:           patch.Section,
		Origin:            patch.Origin,
		Depends:           patch.Depends,
		Breaks:            patch.Breaks,
		FileName:          patch.FileName,
		DownloadUrl:       patch.DownloadUrl,
		Size:              patch.Size,
		Sha1:              patch.Sha1,
		PkgAndVersion:     patch.PkgAndVersion,
		PkgNameWithDistro: patch.PkgNameWithDistro,
		Downloadable:      patch.Downloadable,
		ReleaseDate:       patch.ReleaseDate,
	}
}

func (service UbuntuPatchService) convertCPRestToRest(patch rest.CPUbuntuPatchRest) rest.UbuntuPatchRest {
	patch.Name = patch.UUID
	return rest.UbuntuPatchRest{
		BaseEntityRest:    ConvertToBaseEntityRest(patch.BaseEntityModel),
		UUID:              patch.UUID,
		OsVersion:         patch.OsVersion,
		Channel:           patch.Channel,
		Repo:              patch.Repo,
		PackageName:       patch.PackageName,
		Arch:              patch.Arch,
		Version:           patch.Version,
		Priority:          patch.Priority,
		Section:           patch.Section,
		Origin:            patch.Origin,
		Depends:           patch.Depends,
		Breaks:            patch.Breaks,
		FileName:          patch.FileName,
		DownloadUrl:       patch.DownloadUrl,
		Size:              patch.Size,
		Sha1:              patch.Sha1,
		PkgAndVersion:     patch.PkgAndVersion,
		PkgNameWithDistro: patch.PkgNameWithDistro,
		Downloadable:      patch.Downloadable,
		ReleaseDate:       patch.ReleaseDate,
	}
}

func (service UbuntuPatchService) convertListToRest(patchList []pch_model.UbuntuPatch) []rest.UbuntuPatchRest {
	var patchRestList []rest.UbuntuPatchRest
	if len(patchList) != 0 {
		for _, patch := range patchList {
			patchRest := service.convertToRest(patch)
			patchRestList = append(patchRestList, patchRest)
		}
	}
	return patchRestList
}

func (service UbuntuPatchService) BulkCreateOrUpdate(restList []rest.CPUbuntuPatchRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patch, err := service.Repository.GetByUUID(patchRest.UUID)
			if err == nil && patch.Id != 0 {
				if patch.UpdatedTime != patchRest.UpdatedTime {
					service.Update(patch.Id, service.convertCPRestToRest(patchRest))
				}
			} else {
				patchRest.Id = 0
				service.Create(service.convertCPRestToRest(patchRest))
			}
		}
	}
}

func (service UbuntuPatchService) GetPatch(id int64, includeArchive bool) (rest.UbuntuPatchRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Patch for id %v", id))
	var patchRest rest.UbuntuPatchRest
	patch, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get patch for id - %v, Error : %s ", id, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get Patch for id %v", id))
	return service.convertToRest(patch), nil
}

func (service UbuntuPatchService) Create(ubuntuPatchRest rest.UbuntuPatchRest) (int64, common.CustomError) {

	logger.ServiceLogger.Debug("Process started to create UbuntuPatch ", ubuntuPatchRest.UUID)
	if ubuntuPatchRest.CreatedTime == 0 {
		ubuntuPatchRest.CreatedTime = common.CurrentMillisecond()
	}
	ubuntuPatchRest.CreatedById = common.GetUserFromCallContext()
	patch, err := service.convertToModel(ubuntuPatchRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating UbuntuPatch: %s : %s", ubuntuPatchRest.UUID, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(patch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating UbuntuPatch: %s : %s", ubuntuPatchRest.UUID, err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Debug("Ubuntu Patch ", ubuntuPatchRest.UUID, " created successfully.")
	return id, common.CustomError{}
}

func (service UbuntuPatchService) Update(id int64, restModel rest.UbuntuPatchRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update UbuntuPatch with id - %v", id))
	patch, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuPatch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	if restModel.UpdatedTime == 0 {
		patch.UpdatedTime = common.CurrentMillisecond()
	} else {
		patch.UpdatedTime = restModel.UpdatedTime
	}
	patch.UpdatedById = common.GetUserFromCallContext()
	// Update fields as needed
	PerformPartialUpdateForBase(&patch.BaseEntityModel, restModel.BaseEntityRest)
	patch.UUID = restModel.UUID
	patch.OsVersion = restModel.OsVersion
	patch.Channel = restModel.Channel
	patch.Repo = restModel.Repo
	patch.PackageName = restModel.PackageName
	patch.Arch = restModel.Arch
	patch.Version = restModel.Version
	patch.Priority = restModel.Priority
	patch.Section = restModel.Section
	patch.Origin = restModel.Origin
	patch.Depends = restModel.Depends
	patch.Breaks = restModel.Breaks
	patch.FileName = restModel.FileName
	patch.DownloadUrl = restModel.DownloadUrl
	patch.Size = restModel.Size
	patch.Sha1 = restModel.Sha1
	patch.PkgAndVersion = restModel.PkgAndVersion
	patch.PkgNameWithDistro = restModel.PkgNameWithDistro
	patch.Downloadable = restModel.Downloadable
	patch.ReleaseDate = restModel.ReleaseDate

	_, err = service.Repository.Update(&patch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating UbuntuPatch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update UbuntuPatch with id - %v", id))
	return true, common.CustomError{}
}

func (service UbuntuPatchService) DeleteUbuntuPatch(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete UbuntuPatch for id - %v", id))
	_, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting UbuntuPatch to delete for id - %v, Error: %s", id, err.Error()))
		return false, err
	}

	success, err := service.Repository.DeleteById(id)

	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting UbuntuPatch for id - %v, Error: %s", id, err.Error()))
		return success, err
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to delete UbuntuPatch for id - %v", id))
	return true, nil
}

func (service UbuntuPatchService) GetAllUbuntuPatches(filter rest.SearchFilter) ([]pch_model.UbuntuPatch, error) {
	var patchPageList []pch_model.UbuntuPatch
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.UBUNTU_PATCH.String(), false, "")
	patchPageList, err = service.Repository.GetAllUbuntuPatchesByQuery(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return patchPageList, err
	}
	return patchPageList, nil
}

func (service UbuntuPatchService) GetAllDependentFileDetails(patchRest rest.UbuntuPatchRest, archList []string) []pch_model.PatchFileData {
	var fileDetails []pch_model.PatchFileData
	if patchRest.Depends != "" {
		dependsGroups := strings.Split(patchRest.Depends, ",")
		for _, dependsPackage := range dependsGroups {
			dependsPackage = strings.TrimSpace(dependsPackage)
			matches := dependencyPackageRegex.FindStringSubmatch(dependsPackage)
			if len(matches) > 0 {
				if len(matches[1]) > 0 && len(matches[2]) > 0 && len(matches[3]) > 0 {
					pkgName := matches[1]
					operator := matches[2]
					baseVersion, _ := version.NewVersion(matches[3])
					var qualifications []rest.Qualification
					qualifications = append(qualifications, rest.BuildQualification("package_name", "equals", pkgName, constant.AND.String()))
					qualifications = append(qualifications, rest.BuildQualification("os_version", "equals", patchRest.OsVersion, constant.AND.String()))
					if len(archList) > 0 {
						qualifications = append(qualifications, rest.BuildQualification("arch", "in", archList, constant.AND.String()))
					}
					pkgFilter := rest.SearchFilter{
						Qualification: qualifications,
					}
					var eligiblePkg pch_model.UbuntuPatch
					ubuntuPkgList, _ := service.GetAllUbuntuPatches(pkgFilter)
					if len(ubuntuPkgList) > 0 {
						for _, ubuntuPatch := range ubuntuPkgList {
							currentVersion, _ := version.NewVersion(ubuntuPatch.Version)
							result := currentVersion.Compare(baseVersion)
							switch operator {
							case ">=":
								if result >= 0 {
									eligiblePkg = ubuntuPatch
								}
							case "<=":
								if result <= 0 {
									eligiblePkg = ubuntuPatch
								}
							case "=":
								if result == 0 {
									eligiblePkg = ubuntuPatch
								}
							case "<<":
								if result < 0 {
									eligiblePkg = ubuntuPatch
								}
							case ">>":
								if result > 0 {
									eligiblePkg = ubuntuPatch
								}
							}

							if eligiblePkg.Id > 0 {
								break
							}
						}
					}

					if eligiblePkg.Id > 0 {
						fileDetails = append(fileDetails, pch_model.PatchFileData{
							FileName:     eligiblePkg.FileName,
							RefName:      "",
							Url:          eligiblePkg.DownloadUrl + "?checksum=" + eligiblePkg.Sha1,
							DownloadUrl:  eligiblePkg.DownloadUrl + "?checksum=" + eligiblePkg.Sha1,
							Size:         eligiblePkg.Size,
							ReleaseDate:  0,
							Language:     0,
							ChecksumSHA1: eligiblePkg.Sha1,
						})
					}
				}
			}
		}
	}
	return fileDetails

}
