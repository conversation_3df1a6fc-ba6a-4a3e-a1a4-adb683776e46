package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"fmt"
	"time"

	"github.com/go-co-op/gocron/v2"
)

type JobExecutorService struct {
}

var taskScheduler gocron.Scheduler

func NewJobExecutorService() *JobExecutorService {
	return new(JobExecutorService)
}

func (service JobExecutorService) Init() {
	scheduler, err := gocron.NewScheduler()
	if err != nil {
		logger.ServiceLogger.Error("failed to initialize cron scheduler", err)
		return
	}
	taskScheduler = scheduler
	_, err = taskScheduler.NewJob(gocron.DurationJob(5*time.Minute), gocron.NewTask(service.ExecuteRecurringDeploymentRescheduleJob), gocron.WithName("Recurring Deployment Reschedule Job"))
	if err != nil {
		logger.ServiceLogger.Error("failed to schedule recurring deployment reschedule job", err)
		return
	}
	_, err = taskScheduler.NewJob(gocron.DurationJob(10*time.Minute), gocron.NewTask(service.ExecuteJobToMarkPendingCommandFailed), gocron.WithName("Mark Pending Command Failed Job"))
	if err != nil {
		return
	}
	_, err = taskScheduler.NewJob(gocron.DurationJob(15*time.Minute), gocron.NewTask(service.ExecuteLicenseCheckJob), gocron.WithName("License Validation Job"))
	if err != nil {
		return
	}
	_, err = taskScheduler.NewJob(gocron.DurationJob(2*time.Hour), gocron.NewTask(service.DumpJobTiming), gocron.WithName("Job Time Dump"))
	if err != nil {
		return
	}
	/*_, err = taskScheduler.NewJob(gocron.DurationJob(30*time.Second), gocron.NewTask(dumpStatistics), gocron.WithName("Database Stats Job"))
	if err != nil {
		return
	}*/
	taskScheduler.Start()
}

func getTask(name string) *gocron.Job {
	var job *gocron.Job
	for _, j := range taskScheduler.Jobs() {
		if j.Name() == name {
			job = &j
			break
		}
	}

	return job
}

func (service JobExecutorService) AddOrRescheduleDailyJob(jobName string, time time.Time, function any) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("no job found ", jobName, " so adding new job")
		_, err := taskScheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to schedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " scheduled")
	} else {
		_, err := taskScheduler.Update((*job).ID(), gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to reschedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " rescheduled")
	}
}

func (service JobExecutorService) AddOrRescheduleIntervalJob(jobName string, hour int64, function any) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("no job found ", jobName, " so adding new job")
		_, err := taskScheduler.NewJob(gocron.DurationJob(time.Duration(hour)*time.Hour), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to schedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " scheduled")
	} else {
		_, err := taskScheduler.Update((*job).ID(), gocron.DurationJob(time.Duration(hour)*time.Hour), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to reschedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " rescheduled")
	}
}

func (service JobExecutorService) AddOrRescheduleMonthlyJob(jobName string, time time.Time, function any) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("no job found ", jobName, " so adding new job")
		_, err := taskScheduler.NewJob(gocron.MonthlyJob(1, gocron.NewDaysOfTheMonth(time.Day()), gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to schedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " scheduled")
	} else {
		_, err := taskScheduler.Update((*job).ID(), gocron.MonthlyJob(1, gocron.NewDaysOfTheMonth(time.Day()), gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to reschedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " rescheduled")
	}
}

func (service JobExecutorService) AddOrRescheduleWeeklyJob(jobName string, time time.Time, function any) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("no job found ", jobName, " so adding new job")
		_, err := taskScheduler.NewJob(gocron.WeeklyJob(1, gocron.NewWeekdays(time.Weekday()), gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to schedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " scheduled")
	} else {
		_, err := taskScheduler.Update((*job).ID(), gocron.WeeklyJob(1, gocron.NewWeekdays(time.Weekday()), gocron.NewAtTimes(gocron.NewAtTime(uint(time.Hour()), uint(time.Minute()), uint(time.Second())))), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to reschedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " rescheduled")
	}
}

func (service JobExecutorService) AddOrRescheduleOnceJob(jobName string, time time.Time, function any) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("no job found ", jobName, " so adding new job")
		_, err := taskScheduler.NewJob(gocron.OneTimeJob(gocron.OneTimeJobStartDateTime(time)), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to schedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " scheduled")
	} else {
		_, err := taskScheduler.Update((*job).ID(), gocron.OneTimeJob(gocron.OneTimeJobStartDateTime(time)), gocron.NewTask(function), gocron.WithName(jobName))
		if err != nil {
			logger.ServiceLogger.Debug("failed to reschedule ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " rescheduled")
	}
}

func (service JobExecutorService) RemoveJob(jobName string) {
	job := getTask(jobName)
	if job == nil {
		logger.ServiceLogger.Debug("failed to remove job, no job found ", jobName)
	} else {
		err := taskScheduler.RemoveJob((*job).ID())
		if err != nil {
			logger.ServiceLogger.Debug("failed to remove job ", jobName)
			return
		}
		logger.ServiceLogger.Debug(jobName, " job removed")
	}
}

func (service JobExecutorService) ExecuteJobToMarkPendingCommandFailed() {
	timeLimitExceed := time.Now().UnixMilli() - (time.Hour.Milliseconds() * 3)
	taskService := NewAgentTaskService()
	deploymentService := NewDeploymentService()
	autoPatchTestService := NewAutoPatchTestService()
	deploymentPolicyService := NewDeploymentPolicyService()
	filter := rest.SearchFilter{Qualification: []rest.Qualification{
		{
			Column:    "updated_time",
			Operator:  "<=",
			Value:     timeLimitExceed,
			Condition: "and",
		},
		{
			Column:    "task_status",
			Operator:  "not_in",
			Value:     []int64{int64(model.TaskSuccess), int64(model.TaskFailed), int64(model.TaskCancelled), int64(model.TaskRebootRequired)},
			Condition: "and",
		},
	}}
	query := rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK.String(), true, "")
	logger.PatchPoolingLogger.Debug("Job mark failed query :", query)
	taskCount := taskService.Repository.CountByQuery(query.Query, query.Parameters)
	if taskCount > 0 {
		var deploymentIds []int64
		query = rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK.String(), false, "")
		taskList, err := taskService.Repository.GetAllTaskByQuery(query.Query, query.Parameters)
		if err == nil {
			for _, task := range taskList {
				var isValidTask = true
				if !task.HasMultipleExecution && task.DeploymentId > 0 {
					deployment, _ := deploymentService.Repository.GetById(task.DeploymentId, false)
					if deployment.Id > 0 {
						policy, err := deploymentPolicyService.Repository.GetById(deployment.DeploymentPolicyId, false)
						if err == nil && policy.Type == model.Schedule && policy.InitiateDeploymentOn != model.Recurring {
							isValidTask = false
						}
					}
				}
				if isValidTask {
					logger.PatchPoolingLogger.Debug("Job mark failed for id :", task.Id)
					task.TaskStatus = model.TaskFailed
					task.TaskResult = "The system marked it as failed due to being idle for a long time."
					patchMap := map[string]interface{}{"taskStatus": model.TaskFailed.String(), "taskResult": "The system marked it as failed due to being idle for a long time."}
					_, err := taskService.UpdateAgentTask(task, patchMap)
					if err.Message != "" {
						logger.PatchPoolingLogger.Error("[ExecuteJobToMarkPendingCommandFailed]", err)
					}
					if task.DeploymentId > 0 {
						deploymentIds = common.AddIfNotExist(deploymentIds, task.DeploymentId)
					}
				}
			}
		}

		if len(deploymentIds) > 0 {
			logger.PatchPoolingLogger.Debug("Job mark failed for reoccurring deploymentIds :", deploymentIds)
			for _, deploymentId := range deploymentIds {
				filter := rest.SearchFilter{
					Offset:          0,
					Size:            0,
					IncludeArchived: false,
					SortBy:          "",
					Qualification: []rest.Qualification{{
						Column:   "deploymentId",
						Operator: "Equals",
						Value:    deploymentId,
					}},
				}
				query = rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK.String(), false, "")
				allDeploymentTasks, err := taskService.Repository.GetAllTaskByQuery(query.Query, query.Parameters)
				logger.PatchPoolingLogger.Info(fmt.Sprintf("total %d task found for deplotmane id %d", len(allDeploymentTasks), deploymentId))
				if err == nil && len(allDeploymentTasks) > 0 {
					isAllTaskCompleted := true
					hasMultipleExecution := false
					for _, agentTask := range allDeploymentTasks {
						logger.PatchPoolingLogger.Info("task status : ", agentTask.TaskStatus, "for task id :", agentTask.Id)
						if !(agentTask.TaskStatus == model.TaskSuccess || agentTask.TaskStatus == model.TaskFailed || agentTask.TaskStatus == model.TaskCancelled) {
							isAllTaskCompleted = false
						}

						hasMultipleExecution = agentTask.HasMultipleExecution
					}
					logger.PatchPoolingLogger.Info("isAllTaskCompleted -> ", isAllTaskCompleted, "for", deploymentId)
					if isAllTaskCompleted {
						deployment, _ := deploymentService.Repository.GetById(deploymentId, false)
						if hasMultipleExecution {
							deployment.DeploymentStage = model.Idle
							deployment.NextExecutionTime = time.Now().UnixMilli() + (deployment.NextExecutionTime - deployment.LastExecutionTime)
							deployment.LastExecutionTime = time.Now().UnixMilli()
						} else {
							deployment.DeploymentStage = model.Completed
							deployment.LastExecutionTime = time.Now().UnixMilli()
							if deployment.Origin == model.AUTO_PATCH_TEST {
								go autoPatchTestService.UpdatePatchTestStatus(deployment.Id)
							}
						}
						_, err = deploymentService.Repository.Update(&deployment)
						if err != nil {
							logger.ServiceLogger.Error("[ExecuteJobToMarkPendingCommandFailed]", err)
						}
						go deploymentService.PrepareDeploymentCompletedReport(deploymentId)
					}
				}
			}
		}
	}
}

func (service JobExecutorService) ExecuteRecurringDeploymentRescheduleJob() {
	policyService := NewDeploymentPolicyService()
	policyFilter := rest.SearchFilter{Qualification: []rest.Qualification{{
		Column:   "initiate_deployment_on",
		Operator: "equals",
		Value:    fmt.Sprintf("%d", model.Recurring),
	}}}
	policyQuery := rest.PrepareSecureQueryFromSearchFilter(policyFilter, common.DEPLOYMENT_POLICY.String(), false, "")
	policyList, err := policyService.Repository.GetAllPolicy(policyQuery.Query, policyQuery.Parameters)
	if err == nil && len(policyList) > 0 {
		var policyIds []int64
		var policies = make(map[int64]model.DeploymentPolicy)
		for _, policy := range policyList {
			policyIds = append(policyIds, policy.Id)
			policies[policy.Id] = policy
		}
		deploymentService := NewDeploymentService()
		deploymentFilter := rest.SearchFilter{Qualification: []rest.Qualification{
			{
				Column:    "deployment_policy_id",
				Operator:  "in",
				Value:     policyIds,
				Condition: "and",
			},
			{
				Column:    "deployment_stage",
				Operator:  "equals",
				Value:     fmt.Sprintf("%d", model.Idle),
				Condition: "and",
			}}}
		deploymentQuery := rest.PrepareSecureQueryFromSearchFilter(deploymentFilter, "deployments", false, "")
		deployments, err := deploymentService.Repository.GetAllDeployments(deploymentQuery.Query, deploymentQuery.Parameters)
		if err == nil && len(deployments) > 0 {
			var deploymentIds []int64
			for _, deployment := range deployments {
				policy := policies[deployment.DeploymentPolicyId]
				if policy.Type == model.Schedule && policy.InitiateDeploymentOn == model.Recurring {
					if (deployment.LastExecutionTime + common.ToMilliSecond(policy.AfterEveryTimeUnit, policy.AfterEveryTime)) < (time.Now().UnixMilli() + common.ToMilliSecond("minute", 5)) {
						deploymentIds = append(deploymentIds, deployment.Id)
						deployment.NextExecutionTime = deployment.LastExecutionTime + common.ToMilliSecond(policy.AfterEveryTimeUnit, policy.AfterEveryTime)
						deployment.DeploymentStage = model.Initiated
						_, err = deploymentService.Repository.Update(&deployment)
						if err != nil {
							logger.PatchPoolingLogger.Error("[ExecuteRecurringDeploymentRescheduleJob]", err)
						}
					}
				}
			}

			if len(deploymentIds) > 0 {
				agentTaskService := NewAgentTaskService()
				taskFilter := rest.SearchFilter{Qualification: []rest.Qualification{{
					Column:   "deploymentId",
					Operator: "in",
					Value:    deploymentIds,
				}}}
				taskQuery := rest.PrepareSecureQueryFromSearchFilter(taskFilter, common.AGENT_TASK.String(), false, "")
				taskList, err := agentTaskService.Repository.GetAllTaskByQuery(taskQuery.Query, taskQuery.Parameters)
				if err == nil && taskList != nil && len(taskList) > 0 {
					currentTime := common.CurrentMillisecond()
					for _, task := range taskList {
						task.TaskStatus = model.TaskReadyToDeploy
						task.UpdatedTime = currentTime
						task.TaskResult = ""
						patchMap := map[string]interface{}{"taskStatus": model.TaskReadyToDeploy.String(), "taskResult": "", "updatedTime": currentTime}
						_, err := agentTaskService.UpdateAgentTask(task, patchMap)
						if err.Message != "" {
							logger.PatchPoolingLogger.Error("[ExecuteRecurringDeploymentRescheduleJob]", err)
						}
					}
				}
			}
		}
	}
}

func (service JobExecutorService) ExecuteLicenseCheckJob() {
	NewLicenseService().ValidLicense()
}

func (service JobExecutorService) DumpJobTiming() {
	for _, job := range taskScheduler.Jobs() {
		next, err := job.NextRun()
		if err != nil {
			logger.ServiceLogger.Error("[DumpJobTiming]", err)
		} else {
			logger.ServiceLogger.Info(fmt.Printf("Job %v will run next at %v\n", job.Name(), next.Format("2006-01-02 15:04:05")))
		}

	}

}
