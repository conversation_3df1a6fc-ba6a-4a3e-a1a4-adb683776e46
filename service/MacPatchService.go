package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type MacPatchService struct {
	Repository *repository.MacPatchRepository
}

func NewMacPatchService() *MacPatchService {
	return &MacPatchService{Repository: repository.NewMacPatchRepository()}
}

func (service MacPatchService) convertToModel(patchRest rest.MacPatchRest) (*model.MacOsPatch, error) {
	return &model.MacOsPatch{
		BaseEntityModel:      ConvertToBaseEntityModel(patchRest.BaseEntityRest),
		OsVersion:            patchRest.OsVersion,
		ProductKey:           patchRest.ProductKey,
		ReleaseDate:          patchRest.ReleaseDate,
		Description:          patchRest.Description,
		Version:              patchRest.Version,
		DistributionFileName: patchRest.DistributionFileName,
		ProductType:          patchRest.ProductType,
		Packages:             patchRest.Packages,
	}, nil
}

func (service MacPatchService) convertToRest(patch model.MacOsPatch) rest.MacPatchRest {
	return rest.MacPatchRest{
		BaseEntityRest:       ConvertToBaseEntityRest(patch.BaseEntityModel),
		OsVersion:            patch.OsVersion,
		ProductKey:           patch.ProductKey,
		ReleaseDate:          patch.ReleaseDate,
		Description:          patch.Description,
		Version:              patch.Version,
		DistributionFileName: patch.DistributionFileName,
		ProductType:          patch.ProductType,
		Packages:             patch.Packages,
	}
}

func (service MacPatchService) BulkCreateOrUpdate(restList []rest.MacPatchRest) {
	if len(restList) > 0 {
		for _, patchRest := range restList {
			patch, err := service.Repository.GetByProductKey(patchRest.ProductKey)
			if err == nil && patch.Id != 0 {
				if patch.UpdatedTime != patchRest.UpdatedTime {
					service.Update(patch.Id, patchRest)
				}
			} else {
				patchRest.Id = 0
				service.Create(patchRest)
			}
		}
	}
}

func (service MacPatchService) BeforeCreate(patchRest rest.MacPatchRest) common.CustomError {
	patch, err := service.Repository.GetByProductKey(patchRest.ProductKey)
	if err == nil && patch.Id != 0 {
		return common.CustomError{Message: "Same object exist."}
	}
	return common.CustomError{}
}

func (service MacPatchService) Create(patchRest rest.MacPatchRest) (int64, common.CustomError) {
	/*exception := service.BeforeCreate(patchRest)
	if exception.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Mac patch: %s", exception.Message))
		return 0, exception
	}*/

	logger.ServiceLogger.Info("Process started to create Mac patch")
	if patchRest.CreatedTime == 0 {
		patchRest.CreatedTime = common.CurrentMillisecond()
	}

	patchRest.CreatedById = common.GetUserFromCallContext()
	macPatch, err := service.convertToModel(patchRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Mac patch: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(macPatch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating Mac patch: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create Mac patch process completed successfully")
	return id, common.CustomError{}
}

func (service MacPatchService) Update(id int64, restModel rest.MacPatchRest) (bool, common.CustomError) {

	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update Mac patch with id - %v", id))
	macPatch, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Mac patch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if restModel.UpdatedTime == 0 {
		macPatch.UpdatedTime = common.CurrentMillisecond()
	} else {
		macPatch.UpdatedTime = restModel.UpdatedTime
	}

	macPatch.UpdatedById = common.GetUserFromCallContext()
	service.PerformPartialUpdate(restModel, macPatch)
	_, err = service.Repository.Update(&macPatch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating Mac patch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Process completed to update Mac patch with id - %v", id))
	return true, common.CustomError{}
}

func (service MacPatchService) PerformPartialUpdate(restModel rest.MacPatchRest, macPatch model.MacOsPatch) {
	if restModel.ProductKey != "" {
		macPatch.ProductKey = restModel.ProductKey
	}
	if restModel.OsVersion != "" {
		macPatch.OsVersion = restModel.OsVersion
	}
	macPatch.ReleaseDate = restModel.ReleaseDate
	if restModel.Description != "" {
		macPatch.Description = restModel.Description
	}
	if restModel.Version != "" {
		macPatch.Version = restModel.Version
	}
	if restModel.DistributionFileName != "" {
		macPatch.DistributionFileName = restModel.DistributionFileName
	}
	if restModel.ProductType != "" {
		macPatch.ProductType = restModel.ProductType
	}
}

func (service MacPatchService) GetAll(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.MAC_PATCH.String(), true, "")
	var responsePage rest.ListResponseRest
	var appPageList []model.MacOsPatch
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.MAC_PATCH.String(), false, "")
		appPageList, err = service.Repository.GetAllMacPatches(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(appPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service MacPatchService) GetAllMacPatches(filter rest.SearchFilter) ([]rest.MacPatchRest, error) {
	var macPatchesRest []rest.MacPatchRest
	var macPatches []model.MacOsPatch
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.MAC_PATCH.String(), false, "")
	macPatches, err = service.Repository.GetAllMacPatches(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return macPatchesRest, err
	}
	macPatchesRest = service.convertListToRest(macPatches)
	return macPatchesRest, nil
}

func (service MacPatchService) convertListToRest(appList []model.MacOsPatch) []rest.MacPatchRest {
	var appRestList []rest.MacPatchRest
	if len(appList) != 0 {
		for _, app := range appList {
			appRest := service.convertToRest(app)
			appRestList = append(appRestList, appRest)
		}
	}
	return appRestList
}
