package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"net/http"
	"reflect"
)

type ComputerGroupService struct {
	Repository *repository.ComputerGroupRepository
}

func NewComputerGroupService() *ComputerGroupService {
	return &ComputerGroupService{
		Repository: repository.NewComputerGroupRepository(),
	}
}

func (service *ComputerGroupService) convertToModel(cgRest rest.ComputerGroupRest) *model.ComputerGroup {
	return &model.ComputerGroup{
		BaseEntityModel: ConvertToBaseEntityModel(cgRest.BaseEntityRest),
		Description:     cgRest.Description,
		AssetIds:        cgRest.AssetIds,
	}
}

func (service *ComputerGroupService) convertToRest(computerGroup model.ComputerGroup) rest.ComputerGroupRest {
	return rest.ComputerGroupRest{
		BaseEntityRest: ConvertToBaseEntityRest(computerGroup.BaseEntityModel),
		Description:    computerGroup.Description,
		AssetIds:       computerGroup.AssetIds,
	}
}

func (service *ComputerGroupService) Create(cgRest rest.ComputerGroupRest) (int64, error) {
	cgRest.CreatedTime = common.CurrentMillisecond()
	cgRest.CreatedById = common.GetUserFromCallContext()
	computerGroup := service.convertToModel(cgRest)
	id, err := service.Repository.Create(computerGroup)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service *ComputerGroupService) Update(id int64, restModel rest.ComputerGroupRest) (bool, common.CustomError) {
	computerGroup, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: "ComputerGroup not found", Code: http.StatusNotFound}
	}

	updated := false
	if _, ok := restModel.PatchMap["description"]; ok && computerGroup.Description != restModel.Description {
		computerGroup.Description = restModel.Description
		updated = true
	}
	if _, ok := restModel.PatchMap["assetIds"]; ok && !reflect.DeepEqual(computerGroup.AssetIds, restModel.AssetIds) {
		computerGroup.AssetIds = restModel.AssetIds
		updated = true
	}

	if updated {
		_, err = service.Repository.Update(&computerGroup)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		return true, common.CustomError{}
	}

	return false, common.CustomError{Message: "No changes detected", Code: http.StatusNoContent}
}

func (service *ComputerGroupService) GetById(id int64) (rest.ComputerGroupRest, error) {
	computerGroup, err := service.Repository.GetById(id)
	if err != nil {
		return rest.ComputerGroupRest{}, err
	}
	return service.convertToRest(computerGroup), nil
}

func (service *ComputerGroupService) DeleteById(id int64) (bool, error) {
	return service.Repository.DeleteById(id)
}

func (service *ComputerGroupService) GetAllComputerGroups(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	tableName := common.COMPUTER_GROUP.String()
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		computerGroupList, err := service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(computerGroupList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *ComputerGroupService) convertListToRest(cgList []model.ComputerGroup) []rest.ComputerGroupRest {
	var cgRestList []rest.ComputerGroupRest
	for _, cg := range cgList {
		cgRestList = append(cgRestList, service.convertToRest(cg))
	}
	return cgRestList
}

func (service *ComputerGroupService) BulkCreate(cgRestList []rest.ComputerGroupRest) {
	if len(cgRestList) > 0 {
		for _, cgRest := range cgRestList {
			_, err := service.Create(cgRest)
			if err != nil {
				logger.ServiceLogger.Error("[ComputerGroupService][BulkCreate]", err)
			}
		}
	}
}
