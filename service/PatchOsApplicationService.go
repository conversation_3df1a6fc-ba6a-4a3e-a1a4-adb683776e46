package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
)

type PatchOsApplicationService struct {
	Repository *repository.PatchOsApplicationRepository
}

func NewPatchOsApplicationService() *PatchOsApplicationService {
	return &PatchOsApplicationService{
		Repository: repository.NewPatchOsApplicationRepository(),
	}
}

func (service PatchOsApplicationService) GetPatchOsApplication(id int64) (rest.PatchOsApplicationRest, error) {
	application, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.PatchOsApplicationRest{}, err
	}
	return service.convertToRest(application), nil
}

func (service PatchOsApplicationService) GetGroupName(categoryName string) string {
	var groupName string
	switch categoryName {
	case "Windows Embedded Standard 7", "Windows 7":
		groupName = constant.GroupNameWindows7
	case "Windows 8", "Windows 8 Embedded":
		groupName = constant.GroupNameWindows8
	case "Windows 8.1":
		groupName = constant.GroupNameWindows81
	case "Windows 10, version 1903 and later", "Windows 10", "Microsoft Windows 10 Pro":
		groupName = constant.GroupNameWindows10
	case "Windows 10 LTSB":
		groupName = constant.GroupNameWindows10Ltsb
	case "Windows Server 2012", "Microsoft Windows Server 2012 Datacenter Evaluation":
		groupName = constant.GroupNameWindowsServer2012
	case "Windows Server 2012 R2":
		groupName = constant.GroupNameWindowsServer2012R2
	case "Windows Server 2016", "Microsoft Windows Server 2016 Datacenter Evaluation":
		groupName = constant.GroupNameWindowsServer2016
	case "Windows Server 2019":
		groupName = constant.GroupNameWindowsServer2019
	case "Windows Server, version 1903 and later":
		groupName = constant.GroupNameWindowsServer
	case "Microsoft Defender Antivirus":
		groupName = constant.GroupNameWindowsDefender
	case "Microsoft Edge":
		groupName = constant.GroupNameWindowsEdge
	case "OOBE ZDP":
		groupName = constant.GroupNameAllOs
	case "Windows 11", "Windows 11 GDR-DU", "Microsoft Windows 11 Pro N":
		groupName = constant.GroupNameWindows11
	case constant.MicrosoftServerOs24H2:
		groupName = constant.GroupNameWindows24H2
	case constant.MicrosoftServerOs21H2:
		groupName = constant.GroupNameWindows21H2
	default:
		// Handle other cases here
	}

	return groupName

}

func (service PatchOsApplicationService) GetOsAppByName(name string) (rest.PatchOsApplicationRest, error) {
	application, err := service.Repository.GetByName(name)
	if err != nil {
		return rest.PatchOsApplicationRest{}, err
	}
	return service.convertToRest(application), nil
}

func (service PatchOsApplicationService) GetOrCreatePatchOsApplication(appName string) (rest.PatchOsApplicationRest, error) {
	application, err := service.Repository.GetByName(appName)
	if err != nil {
		isStartsWithAny := common.StringStartsWithAny(appName, "Windows XP", "Windows Vista", "Windows 7", "Windows 8", "Windows 8.1", "Windows RT 8.1", "Windows 10", "Windows 2000", "Windows Server ", "Windows Embedded Standard 7", "Windows Server", "Ubuntu", "CentOS", "Red Hat", "Linux Mint", "Windows 11", "debian", "macOS")
		if isStartsWithAny {
			application.ProductType = model.OS
		} else {
			application.ProductType = model.APPLICATION
		}
		application.Platform = common.Windows
		application.Hidden = true
		application.Name = appName
		_, err = service.Repository.Create(&application)
		if err != nil {
			logger.ServiceLogger.Error("[GetOrCreatePatchOsApplication]", err)
		}
		application, err = service.Repository.GetByName(appName)
	}
	return service.convertToRest(application), nil
}

func (service PatchOsApplicationService) CreatePatchOsApplication(applicationRest rest.PatchOsApplicationRest) (int64, error) {
	application, err := service.Repository.GetByName(applicationRest.Name)
	if err == nil && application.Id > 0 {
		return application.Id, nil
	}
	application = service.convertToModel(applicationRest)
	id, err := service.Repository.Create(&application)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service PatchOsApplicationService) UpdatePatchOsApplication(id int64, applicationRest rest.PatchOsApplicationRest) (bool, error) {
	application, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, err
	}

	_, updated := service.performPartialUpdate(&application, applicationRest)
	if !updated {
		return false, nil
	}

	_, err = service.Repository.Update(&application)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (service PatchOsApplicationService) DeletePatchOsApplication(id int64) (bool, error) {
	_, err := service.Repository.DeleteById(id)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (service PatchOsApplicationService) GetAllPatchOsApplications(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_OS_APPLICATION.String(), true, "")
	var responsePage rest.ListResponseRest
	var applicationPageList []model.PatchOsApplication
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_OS_APPLICATION.String(), false, "")
		applicationPageList, err = service.Repository.GetAllApplication(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(applicationPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service PatchOsApplicationService) GetAll(filter rest.SearchFilter) ([]rest.PatchOsApplicationRest, error) {
	var patchOsApplicationRest []rest.PatchOsApplicationRest
	var patchOsApplications []model.PatchOsApplication
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_OS_APPLICATION.String(), false, "")
	patchOsApplications, err = service.Repository.GetAllApplication(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return patchOsApplicationRest, err
	}
	patchOsApplicationRest = service.convertListToRest(patchOsApplications)
	return patchOsApplicationRest, nil
}

func (service PatchOsApplicationService) convertToModel(applicationRest rest.PatchOsApplicationRest) model.PatchOsApplication {
	var productType model.PatchProductType
	var os common.OsType
	if applicationRest.Name == "" {
		applicationRest.Name = applicationRest.UUID
	}
	return model.PatchOsApplication{
		BaseEntityModel:   ConvertToBaseEntityModel(applicationRest.BaseEntityRest),
		ProductFamily:     applicationRest.ProductFamily,
		CompanyName:       applicationRest.CompanyName,
		ProductFamilyUUID: applicationRest.ProductFamilyUUID,
		UUID:              applicationRest.UUID,
		ProductType:       productType.ToPatchProductType(applicationRest.ProductType),
		Vendor:            applicationRest.Vendor,
		Platform:          os.ToOsType(applicationRest.Platform),
		CategoryType:      applicationRest.CategoryType,
		Description:       applicationRest.Description,
		Hidden:            applicationRest.Hidden,
		GroupName:         applicationRest.GroupName,
		ServicePack:       applicationRest.ServicePack,
	}
}

func (service PatchOsApplicationService) convertToRest(application model.PatchOsApplication) rest.PatchOsApplicationRest {
	return rest.PatchOsApplicationRest{
		BaseEntityRest:    ConvertToBaseEntityRest(application.BaseEntityModel),
		ProductFamily:     application.ProductFamily,
		CompanyName:       application.CompanyName,
		ProductFamilyUUID: application.ProductFamilyUUID,
		UUID:              application.UUID,
		ProductType:       application.ProductType.String(),
		Vendor:            application.Vendor,
		Platform:          application.Platform.String(),
		CategoryType:      application.CategoryType,
		Description:       application.Description,
		Hidden:            application.Hidden,
		GroupName:         application.GroupName,
		ServicePack:       application.ServicePack,
	}
}

func (service PatchOsApplicationService) convertListToRest(applications []model.PatchOsApplication) []rest.PatchOsApplicationRest {
	var applicationRestList []rest.PatchOsApplicationRest
	if len(applications) != 0 {
		for _, application := range applications {
			applicationRest := service.convertToRest(application)
			applicationRestList = append(applicationRestList, applicationRest)
		}
	}
	return applicationRestList
}

func (service PatchOsApplicationService) performPartialUpdate(domainModel *model.PatchOsApplication, restModel rest.PatchOsApplicationRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if restModel.ProductFamily != "" && domainModel.ProductFamily != restModel.ProductFamily {
		common.PrepareInDiffMap("product_family", domainModel.ProductFamily, restModel.ProductFamily, &diffMap)
		domainModel.ProductFamily = restModel.ProductFamily
	}

	if restModel.CompanyName != "" && domainModel.CompanyName != restModel.CompanyName {
		common.PrepareInDiffMap("company_name", domainModel.CompanyName, restModel.CompanyName, &diffMap)
		domainModel.CompanyName = restModel.CompanyName
	}

	if restModel.ProductFamilyUUID != "" && domainModel.ProductFamilyUUID != restModel.ProductFamilyUUID {
		common.PrepareInDiffMap("product_family_uuid", domainModel.ProductFamilyUUID, restModel.ProductFamilyUUID, &diffMap)
		domainModel.ProductFamilyUUID = restModel.ProductFamilyUUID
	}

	if restModel.UUID != "" && domainModel.UUID != restModel.UUID {
		common.PrepareInDiffMap("uuid", domainModel.UUID, restModel.UUID, &diffMap)
		domainModel.UUID = restModel.UUID
	}

	if restModel.ProductType != "" && domainModel.ProductType.String() != restModel.ProductType {
		var productType model.PatchProductType
		common.PrepareInDiffMap("product_type", domainModel.ProductType, restModel.ProductType, &diffMap)
		domainModel.ProductType = productType.ToPatchProductType(restModel.ProductType)
	}

	if restModel.Vendor != "" && domainModel.Vendor != restModel.Vendor {
		common.PrepareInDiffMap("vendor", domainModel.Vendor, restModel.Vendor, &diffMap)
		domainModel.Vendor = restModel.Vendor
	}

	if restModel.Platform != "" && domainModel.Platform.String() != restModel.Platform {
		var os common.OsType
		common.PrepareInDiffMap("platform", domainModel.Platform, restModel.Platform, &diffMap)
		domainModel.Platform = os.ToOsType(restModel.Platform)
	}

	if restModel.CategoryType != "" && domainModel.CategoryType != restModel.CategoryType {
		common.PrepareInDiffMap("category_type", domainModel.CategoryType, restModel.CategoryType, &diffMap)
		domainModel.CategoryType = restModel.CategoryType
	}

	if restModel.Description != "" && domainModel.Description != restModel.Description {
		common.PrepareInDiffMap("description", domainModel.Description, restModel.Description, &diffMap)
		domainModel.Description = restModel.Description
	}

	if domainModel.Hidden != restModel.Hidden {
		common.PrepareInDiffMap("hidden", domainModel.Hidden, restModel.Hidden, &diffMap)
		domainModel.Hidden = restModel.Hidden
	}

	if restModel.GroupName != "" && domainModel.GroupName != restModel.GroupName {
		common.PrepareInDiffMap("group_name", domainModel.GroupName, restModel.GroupName, &diffMap)
		domainModel.GroupName = restModel.GroupName
	}

	if restModel.ServicePack != "" && domainModel.ServicePack != restModel.ServicePack {
		common.PrepareInDiffMap("service_pack", domainModel.ServicePack, restModel.ServicePack, &diffMap)
		domainModel.ServicePack = restModel.ServicePack
	}

	return diffMap, len(diffMap) != 0
}
