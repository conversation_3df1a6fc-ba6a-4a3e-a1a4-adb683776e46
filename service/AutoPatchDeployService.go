package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
	"reflect"
	"strings"
)

type AutoPatchDeployService struct {
	Repository *repository.AutoPatchDeployRepository
}

func NewAutoPatchDeployService() *AutoPatchDeployService {
	return &AutoPatchDeployService{
		Repository: repository.NewAutoPatchDeployRepository(),
	}
}
func (service *AutoPatchDeployService) convertToModel(restDeploy rest.AutoPatchDeployRest) *model.AutoPatchDeploy {
	var appType model.APApplicationType
	appType = appType.ToAPApplicationType(restDeploy.ApplicationType)

	var patchCategories []model.PatchUpdateCategory
	for _, patchCategory := range restDeploy.PatchCategories {
		var pchcategory model.PatchUpdateCategory
		patchCategories = append(patchCategories, pchcategory.ToPatchCategory(strings.ToLower(patchCategory)))
	}

	var severityList []model.PatchSeverity
	for _, sev := range restDeploy.PatchSeverities {
		var severity model.PatchSeverity
		severityList = append(severityList, severity.ToPatchSeverity(strings.ToLower(sev)))
	}

	return &model.AutoPatchDeploy{
		BaseEntityModel:     ConvertToBaseEntityModel(restDeploy.BaseEntityRest),
		DisplayName:         restDeploy.DisplayName,
		Description:         restDeploy.Description,
		PatchCategories:     patchCategories,
		PatchSeverities:     severityList,
		Platform:            restDeploy.Platform,
		ApplicationType:     appType,
		ProductIds:          restDeploy.ProductIds,
		ComputerGroupFilter: ConvertToComputerGroupFilter(restDeploy.ComputerGroupFilterRest),
		DeploymentPolicyId:  restDeploy.DeploymentPolicyId,
		NotifyTo:            restDeploy.NotifyTo,
		DeploymentScheduler: rest.ToSchedulerConfig(restDeploy.DeploymentScheduler),
	}
}

func (service *AutoPatchDeployService) convertToRest(deploy model.AutoPatchDeploy) rest.AutoPatchDeployRest {
	var severityList []string
	for _, sev := range deploy.PatchSeverities {
		severityList = append(severityList, sev.String())
	}

	var categoryList []string
	for _, category := range deploy.PatchCategories {
		categoryList = append(categoryList, category.String())
	}

	return rest.AutoPatchDeployRest{
		BaseEntityRest:          ConvertToBaseEntityRest(deploy.BaseEntityModel),
		DisplayName:             deploy.DisplayName,
		Description:             deploy.Description,
		PatchCategories:         categoryList,
		PatchSeverities:         severityList,
		Platform:                deploy.Platform,
		ApplicationType:         deploy.ApplicationType.String(),
		ProductIds:              deploy.ProductIds,
		ComputerGroupFilterRest: ConvertToComputerGroupFilterRest(deploy.ComputerGroupFilter),
		DeploymentPolicyId:      deploy.DeploymentPolicyId,
		NotifyTo:                deploy.NotifyTo,
		DeploymentScheduler:     rest.ToSchedulerConfigRest(deploy.DeploymentScheduler),
	}
}

func (service *AutoPatchDeployService) Create(restDeploy rest.AutoPatchDeployRest) (int64, error) {
	restDeploy.CreatedTime = common.CurrentMillisecond()
	restDeploy.CreatedById = common.GetUserFromCallContext()
	deploy := service.convertToModel(restDeploy)
	id, err := service.Repository.Create(deploy)
	if err != nil {
		return 0, fmt.Errorf("failed to create AutoPatchDeploy: %w", err)
	}
	return id, nil
}

func (service *AutoPatchDeployService) Update(id int64, restDeploy rest.AutoPatchDeployRest) (bool, common.CustomError) {
	deploy, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Apply updates based on PatchMap
	if _, ok := restDeploy.PatchMap["displayName"]; ok && deploy.DisplayName != restDeploy.DisplayName {
		deploy.DisplayName = restDeploy.DisplayName
	}
	if _, ok := restDeploy.PatchMap["description"]; ok && deploy.Description != restDeploy.Description {
		deploy.Description = restDeploy.Description
	}
	if _, ok := restDeploy.PatchMap["patchCategories"]; ok {
		var patchCategories []model.PatchUpdateCategory
		for _, patchCategory := range restDeploy.PatchCategories {
			var category model.PatchUpdateCategory
			patchCategories = append(patchCategories, category.ToPatchCategory(strings.ToLower(patchCategory)))
		}
		if !reflect.DeepEqual(deploy.PatchCategories, patchCategories) {
			deploy.PatchCategories = patchCategories
		}
	}
	if _, ok := restDeploy.PatchMap["patchSeverities"]; ok {
		var patchSeverities []model.PatchSeverity
		for _, patchSeverity := range restDeploy.PatchSeverities {
			var severity model.PatchSeverity
			patchSeverities = append(patchSeverities, severity.ToPatchSeverity(strings.ToLower(patchSeverity)))
		}
		if !reflect.DeepEqual(deploy.PatchSeverities, patchSeverities) {
			deploy.PatchSeverities = patchSeverities
		}
	}

	if _, ok := restDeploy.PatchMap["computerGroupFilterRest"]; ok {
		var computerGroupFilter model.ComputerGroupFilter
		computerGroupFilter = ConvertToComputerGroupFilter(restDeploy.ComputerGroupFilterRest)
		if !reflect.DeepEqual(deploy.ComputerGroupFilter, computerGroupFilter) {
			deploy.ComputerGroupFilter = computerGroupFilter
		}
	}

	if _, ok := restDeploy.PatchMap["platform"]; ok && deploy.Platform != restDeploy.Platform {
		deploy.Platform = restDeploy.Platform
	}
	if _, ok := restDeploy.PatchMap["productIds"]; ok && !reflect.DeepEqual(deploy.ProductIds, restDeploy.ProductIds) {
		deploy.ProductIds = restDeploy.ProductIds
	}
	if _, ok := restDeploy.PatchMap["notifyTo"]; ok && !reflect.DeepEqual(deploy.NotifyTo, restDeploy.NotifyTo) {
		deploy.NotifyTo = restDeploy.NotifyTo
	}
	if _, ok := restDeploy.PatchMap["deploymentScheduler"]; ok && !reflect.DeepEqual(rest.ToSchedulerConfigRest(deploy.DeploymentScheduler), restDeploy.DeploymentScheduler) {
		deploy.DeploymentScheduler = rest.ToSchedulerConfig(restDeploy.DeploymentScheduler)
	}

	_, err = service.Repository.Update(&deploy)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	return true, common.CustomError{}
}

func (service *AutoPatchDeployService) GetById(id int64) (rest.AutoPatchDeployRest, error) {
	deploy, err := service.Repository.GetById(id)
	if err != nil {
		return rest.AutoPatchDeployRest{}, fmt.Errorf("failed to get AutoPatchDeploy by id %d: %w", id, err)
	}
	return service.convertToRest(deploy), nil
}

func (service *AutoPatchDeployService) DeleteById(id int64) (bool, error) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, fmt.Errorf("failed to delete AutoPatchDeploy by id %d: %w", id, err)
	}
	return success, nil
}

func (service *AutoPatchDeployService) GetAllDeploys(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var deployList []model.AutoPatchDeploy
	var err error
	tableName := common.AUTO_PATCH_DEPLOY.String()
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count, err := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if err != nil {
		return responsePage, fmt.Errorf("failed to count AutoPatchDeploys: %w", err)
	}
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		deployList, err = service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, fmt.Errorf("failed to get all AutoPatchDeploys: %w", err)
		}
	}
	responsePage.ObjectList = service.convertListToRest(deployList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *AutoPatchDeployService) convertListToRest(deploys []model.AutoPatchDeploy) []rest.AutoPatchDeployRest {
	var deployRestList []rest.AutoPatchDeployRest
	for _, deploy := range deploys {
		deployRestList = append(deployRestList, service.convertToRest(deploy))
	}
	return deployRestList
}

func (service AutoPatchDeployService) ExecuteAutoPatchDeployJob() {
	logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] executing auto patch deploy job")
	queryResult := rest.PrepareSecureQueryFromSearchFilter(rest.SearchFilter{}, common.AUTO_PATCH_DEPLOY.String(), false, "")
	autoPatchDeploys, err := service.Repository.GetAll(queryResult.Query, queryResult.Parameters)
	if err != nil {
		logger.ServiceLogger.Error("Error while ExecuteAutoPatchDeployJob", err.Error())
	}

	if len(autoPatchDeploys) > 0 {
		logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] auto patch deploy count ", len(autoPatchDeploys))
		assetPatchRelationService := NewAssetPatchRelationService()
		patchService := NewPatchService()
		for _, patchDeploy := range autoPatchDeploys {
			allAssetIds := GetAllEndpointIdsFromCGFilter(patchDeploy.ComputerGroupFilter)
			if len(allAssetIds) > 0 {
				patchList, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{
					Qualification: []rest.Qualification{
						rest.BuildQualification("asset_id", "in", allAssetIds, "and"),
						rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Missing), "and"),
					},
				})
				if err != nil {
					logger.ServiceLogger.Error("Error while retrieving patch list ExecuteAutoPatchDeployJob", err.Error())
				}
				var patchIds []int64
				var qualifiedPatches []int64
				uniqueIds := make(map[int64]struct{})      // A map to store unique IDs
				uniquePatchIds := make(map[int64]struct{}) // A map to store unique IDs
				if len(patchList) > 0 {
					for _, relationRest := range patchList {
						if _, exists := uniquePatchIds[relationRest.PatchId]; !exists {
							uniquePatchIds[relationRest.PatchId] = struct{}{}
							patchIds = append(patchIds, relationRest.PatchId)
						}
					}
					var severityList []string
					var categories []string
					thirdPartyEnabled := false
					if patchDeploy.PatchSeverities != nil && len(patchDeploy.PatchSeverities) > 0 {
						for _, severity := range patchDeploy.PatchSeverities {
							severityList = append(severityList, fmt.Sprintf("%d", severity))
						}
					}
					if patchDeploy.PatchCategories != nil && len(patchDeploy.PatchCategories) > 0 {
						for _, category := range patchDeploy.PatchCategories {
							if category == model.THIRD_PARTY {
								thirdPartyEnabled = true
							} else {
								categories = append(categories, fmt.Sprintf("%d", category))
							}
						}
					}
					var qualifications []rest.Qualification
					qualifications = append(qualifications, rest.BuildQualification("tags", "not_equals", "Third Party", "and"))
					qualifications = append(qualifications, rest.BuildQualification("id", "in", patchIds, "and"))
					qualifications = append(qualifications, rest.BuildQualification("patch_approval_status", "equals", model.APPROVED.String(), "and"))

					if patchDeploy.Platform != "" {
						qualifications = append(qualifications, rest.BuildQualification("os_platform", "equals", patchDeploy.Platform, "and"))
					}

					if len(severityList) > 0 {
						qualifications = append(qualifications, rest.BuildQualification("patch_severity", "in", severityList, "and"))
					}

					if len(categories) > 0 {
						qualifications = append(qualifications, rest.BuildQualification("patch_update_category", "in", categories, "and"))
					}

					if patchDeploy.ApplicationType != model.AllApplication {
						if patchDeploy.ApplicationType == model.IncludeApplication {
							qualifications = append(qualifications, rest.BuildQualification("affected_products", "equals", patchDeploy.ProductIds, "and"))
						} else if patchDeploy.ApplicationType == model.ExcludeApplication {
							qualifications = append(qualifications, rest.BuildQualification("affected_products", "not_equals", patchDeploy.ProductIds, "and"))
						}
					}
					patches, err := patchService.GetAllPatches(rest.SearchFilter{Qualification: qualifications})
					if err != nil {
						logger.ServiceLogger.Error("Error while retrieving patches ExecuteAutoPatchDeployJob", err.Error())
					}
					for _, patch := range patches {
						if _, exists := uniqueIds[patch.Id]; !exists {
							uniqueIds[patch.Id] = struct{}{}
							qualifiedPatches = append(qualifiedPatches, patch.Id)
						}
					}

					if thirdPartyEnabled {
						var thirdPartyQualifications []rest.Qualification
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("tags", "equals", "Third Party", "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("id", "in", patchIds, "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_approval_status", "equals", model.NOT_APPROVED.String(), "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_test_status", "equals", model.NOT_TESTED.String(), "and"))

						if patchDeploy.Platform != "" {
							thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("os_platform", "equals", patchDeploy.Platform, "and"))
						}

						if len(severityList) > 0 {
							thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_severity", "in", severityList, "and"))
						}

						thirdPartyPatches, err := patchService.GetAllPatches(rest.SearchFilter{Qualification: thirdPartyQualifications})
						if err != nil {
							logger.ServiceLogger.Error("Error while retrieving patches ExecuteAutoPatchDeployJob", err.Error())
						}
						for _, patch := range thirdPartyPatches {
							if _, exists := uniqueIds[patch.Id]; !exists {
								uniqueIds[patch.Id] = struct{}{}
								qualifiedPatches = append(qualifiedPatches, patch.Id)
							}
						}
					}
				} else {
					logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] no missing patches found for ", patchDeploy.DisplayName, " auto patch deploy ", patchDeploy.Id)
				}
				if len(qualifiedPatches) > 0 {
					deployment := rest.DeploymentRest{
						AgentScopeFilterRest: rest.AgentScopeFilterRest{
							Scope:  int64(model.SpecificAssets),
							Assets: allAssetIds,
						},
						DisplayName:        "Auto Patch Deploy - " + patchDeploy.DisplayName,
						DeploymentType:     model.Install.String(),
						DeploymentStage:    model.Initiated.String(),
						RefIds:             qualifiedPatches,
						DeploymentPolicyId: constant.DefaultInstantDeploymentPolicyId,
						Origin:             model.AUTO_PATCH_DEPLOY.String(),
					}

					deployment.RefModel = "Patch"
					deployment.RefId = patchDeploy.Id
					_, err = NewDeploymentService().Create(deployment)
					if err != nil {
						logger.ServiceLogger.Error("Error while creating deployment [ExecuteAutoPatchDeployJob]", err)
					}
				} else {
					logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] no qualified patches found for ", patchDeploy.DisplayName, " auto patch deploy ", patchDeploy.Id)
				}
			} else {
				logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] no assets found for ", patchDeploy.DisplayName, " auto patch deploy ", patchDeploy.Id)
			}
		}
	} else {
		logger.ServiceLogger.Debug("[ExecuteAutoPatchDeployJob] no auto patch deploy policies found.")
	}
}
