package service

import (
	"deployment/common"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"encoding/base64"
	"github.com/google/uuid"
)

type AgentTaskResultService struct {
	Repository *repository.AgentTaskResultRepository
}

func NewAgentTaskResultService() *AgentTaskResultService {
	return &AgentTaskResultService{
		Repository: repository.NewAgentTaskResultRepository(),
	}
}

func (service AgentTaskResultService) convertToModel(restModel rest.AgentTaskResultRest) (*model.AgentTaskResult, error) {
	var taskStatus model.AgentTaskStatus
	var err error
	taskStatus, err = taskStatus.ToTaskStatus(restModel.TaskStatus)
	if err != nil {
		return &model.AgentTaskResult{}, err
	}
	restModel.Name = uuid.New().String()
	return &model.AgentTaskResult{
		BaseEntityRefModel: ConvertToBaseEntityRefModel(restModel.BaseEntityRefModelRest),
		TaskStatus:         taskStatus,
		TaskResult:         restModel.TaskResult,
	}, nil
}

func (service AgentTaskResultService) convertToRest(entityModel model.AgentTaskResult) rest.AgentTaskResultRest {
	taskRest := rest.AgentTaskResultRest{
		BaseEntityRefModelRest: ConvertToBaseEntityRefModelRest(entityModel.BaseEntityRefModel),
		TaskStatus:             entityModel.TaskStatus.String(),
		TaskResult:             base64.StdEncoding.EncodeToString([]byte(entityModel.TaskResult)),
	}

	return taskRest
}

func (service AgentTaskResultService) Create(restModel rest.AgentTaskResultRest) (int64, error) {
	restModel.CreatedTime = common.CurrentMillisecond()
	restModel.CreatedById = common.GetUserFromCallContext()
	agentTaskResult, err := service.convertToModel(restModel)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(agentTaskResult)
	return id, err
}

func (service AgentTaskResultService) GetAllTaskResult(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK_RESULT.String(), true, "")
	var responsePage rest.ListResponseRest
	var packagePageList []model.AgentTaskResult
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.AGENT_TASK_RESULT.String(), false, "")
		packagePageList, err = service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AgentTaskResultService) convertListToRest(agentTaskResults []model.AgentTaskResult) []rest.AgentTaskResultRest {
	var restList []rest.AgentTaskResultRest
	if len(agentTaskResults) != 0 {
		for _, taskResult := range agentTaskResults {
			taskResultRest := service.convertToRest(taskResult)
			restList = append(restList, taskResultRest)
		}
	}
	return restList
}
