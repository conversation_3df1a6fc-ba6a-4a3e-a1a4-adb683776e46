package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/rest"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

type AssetPatchService struct {
}

func NewAssetPatchService() *AssetPatchService {
	return &AssetPatchService{}
}

func (service AssetPatchService) GetAllApplicabilityRuleFileList(assetId int64) (map[string]interface{}, error) {
	categoryFileMap := map[string]interface{}{}
	asset, err := NewAgentService().GetAssetMetaDataById(assetId)
	if err != nil {
		return nil, err
	}
	patchAsset, _ := NewAssetService().GetAsset(assetId)

	var sysInfo common.SysInfo
	var groupNameList []string
	groupNameList = append(groupNameList, constant.GroupNameAllOs)
	if strings.Contains(asset.Name, constant.GroupNameWindowsServer2012) || strings.Contains(asset.Name, constant.WindowsServer2022) {
		groupNameList = append(groupNameList, constant.GroupNameWindows21H2)
	}

	if strings.Contains(asset.Name, constant.WindowsServer2025) {
		groupNameList = append(groupNameList, constant.GroupNameWindows24H2)
	}

	sysInfo = sysInfo.GetSysInfo(patchAsset.SysInfo)

	patchAppFilter := rest.SearchFilter{
		Qualification: []rest.Qualification{
			rest.BuildQualification("asset_id", "equals", fmt.Sprint(assetId), constant.AND.String()),
			rest.BuildEnumQualification("platform", "equals", common.Windows.String(), constant.AND.String(), "os"),
		},
	}

	patchApplications, err := NewPatchAssetApplicationService().GetAllPatchAssetApps(patchAppFilter)
	if err == nil && len(patchApplications) > 0 {
		for _, patchAppRest := range patchApplications {
			categoryName := patchAppRest.Name
			if common.StringContainsInList([]string{constant.WindowsEdgeBetaApp, constant.WindowsEdgeStableApp, constant.WindowsEdgeCanaryApp, constant.WindowsEdgeDevApp}, strings.TrimSpace(categoryName)) {
				categoryName = constant.WindowsEdgeApp
			}
			groupName := NewPatchOsApplicationService().GetGroupName(categoryName)
			if groupName != "" {
				groupNameList = append(groupNameList, groupName)
			}
		}
	}

	if sysInfo.OsName != "" {
		groupNameList = append(groupNameList, sysInfo.OsName)
	}

	patchCategoryFilter := rest.SearchFilter{
		Qualification: []rest.Qualification{
			rest.BuildQualification("group_name", "in", groupNameList, constant.AND.String()),
		},
	}

	categoryList, err := NewPatchOsApplicationService().GetAll(patchCategoryFilter)
	if err == nil && len(categoryList) > 0 {
		for _, osApp := range categoryList {
			categoryFileMap[osApp.Name] = strings.ReplaceAll(osApp.Name, " ", "") + "_" + sysInfo.OsArch + ".7z"
		}
	}

	isDotNetAvailable := false
	isVisualStudioAvailable := false
	if len(patchAsset.InstalledSoftwareList) > 0 {
		for _, installedSoftware := range patchAsset.InstalledSoftwareList {
			softwareName := strings.ToLower(installedSoftware.Name)
			if strings.Contains(softwareName, "dotnet") || strings.Contains(softwareName, ".net") {
				isDotNetAvailable = true
			} else if strings.Contains(softwareName, "microsoft office") {
				/*if strings.Contains(softwareName, "2016") {
					categoryFileMap["Office2016"] = "Office2016_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2007") {
					categoryFileMap["Office2007"] = "Office2007_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2010") {
					categoryFileMap["Office2010"] = "Office2010_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2013") {
					categoryFileMap["Office2013"] = "Office2013_" + sysInfo.OsArch + ".7z"
				}*/
			} else if strings.Contains(softwareName, "microsoft 365 apps for business") {
				//categoryFileMap["Microsoft365"] = "Microsoft365Apps" + ".7z"
			} else if strings.Contains(softwareName, "skype for business server") {
				if strings.Contains(softwareName, "2015") {
					//categoryFileMap["SkypeforBusinessServer2015SmartSetup"] = "SkypeforBusinessServer2015,SmartSetup_" + sysInfo.OsArch + ".7z"
					//categoryFileMap["SkypeforBusinessServer2015_"] = "SkypeforBusinessServer2015_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2019") {
					//categoryFileMap["SkypeforBusinessServer2019SmartSetup"] = "SkypeforBusinessServer2019,SmartSetup_" + sysInfo.OsArch + ".7z"
				}
			} else if strings.Contains(softwareName, "microsoft visual studio") {
				isVisualStudioAvailable = true
			} else if strings.Contains(softwareName, "sql server") {
				if strings.Contains(softwareName, "2016") {
					categoryFileMap["Microsoft SQL Server 2016"] = "MicrosoftSQLServer2016_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2017") {
					categoryFileMap["Microsoft SQL Server 2017"] = "MicrosoftSQLServer2017_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2019") {
					categoryFileMap["Microsoft SQL Server 2019"] = "MicrosoftSQLServer2019_" + sysInfo.OsArch + ".7z"
				} else if strings.Contains(softwareName, "2022") {
					categoryFileMap["Microsoft SQL Server 2022"] = "MicrosoftSQLServer2022_" + sysInfo.OsArch + ".7z"
				}
			}
		}
	}
	patchPreference, err := NewPatchPreferenceService().Get()
	if err == nil && patchPreference.EnableThirdPartyPatching {
		thirdPartyPackageList, err := NewThirdPartyPackageService().GetAllThirdPartyPackages(rest.SearchFilter{Qualification: []rest.Qualification{
			{
				Column:    "os",
				Value:     common.Windows.String(), // MacOS enum value
				Operator:  "equals",
				Type:      "enum",
				Reference: "os",
			},
		}})
		if err == nil {
			for _, pkg := range thirdPartyPackageList {
				if pkg.Application != "microsoft_office" {
					categoryFileMap[pkg.Application] = pkg.Application + ".7z"
				}
			}
		}
	}

	categoryFileMap["oids"] = "oids.7z"

	if isDotNetAvailable {
		categoryFileMap["NET70"] = "NET70" + "_" + sysInfo.OsArch + ".7z"
		categoryFileMap["NET60"] = "NET60" + "_" + sysInfo.OsArch + ".7z"
		categoryFileMap["NET80"] = "NET80" + "_" + sysInfo.OsArch + ".7z"
	}

	if isVisualStudioAvailable {
		/*categoryFileMap["VisualStudio2013"] = "VisualStudio2013_" + sysInfo.OsArch + ".7z"
		categoryFileMap["VisualStudio2015"] = "VisualStudio2015_" + sysInfo.OsArch + ".7z"
		categoryFileMap["VisualStudio2017"] = "VisualStudio2017_" + sysInfo.OsArch + ".7z"
		categoryFileMap["VisualStudio2019"] = "VisualStudio2019_" + sysInfo.OsArch + ".7z"
		categoryFileMap["VisualStudio2022"] = "VisualStudio2022_" + sysInfo.OsArch + ".7z"
		categoryFileMap["VisualStudio2015Update3"] = "VisualStudio2015Update3_" + sysInfo.OsArch + ".7z"*/
	}

	for key, value := range categoryFileMap {
		fileName := value.(string)
		if !strings.HasSuffix(fileName, ".7z") {
			fileName = fileName + ".7z"
		}
		if strings.EqualFold(fileName, "MicrosoftDefenderAntivirus_x64.7z") {
			delete(categoryFileMap, key)
			continue
		}

		path := filepath.Join(common.PatchXmlPath(), fileName)
		_, err = os.Stat(path)
		if os.IsNotExist(err) {
			go NewPatchSyncService().DownloadPatchXmlByFileName(fileName)
		}
	}

	return categoryFileMap, nil
}
