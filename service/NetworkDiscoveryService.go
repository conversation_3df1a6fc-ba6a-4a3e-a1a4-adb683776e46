package service

import (
	"deployment/client"
	"deployment/common"
	"deployment/logger"
	"deployment/plugin"
	"encoding/json"
	"strings"
)

type NetworkDiscoveryService struct {
}

type NetworkDiscoveryContext struct {
	DiscoveryId        int64                    `json:"discovery_id"`
	DeviceId           int64                    `json:"device_id"`
	DiscoveryType      string                   `json:"discovery_type"`
	Port               int                      `json:"port"`
	RetryCount         int                      `json:"retry_count"`
	Timeout            int                      `json:"timeout"`
	Organization       int                      `json:"organization"`
	Department         int                      `json:"department"`
	PollingInterval    int                      `json:"polling_interval"`
	Targets            []string                 `json:"targets"`
	CredentialProfiles []map[string]interface{} `json:"credential_profiles"`
}

func NewNetworkDiscoveryService() *NetworkDiscoveryService {
	return &NetworkDiscoveryService{}
}

func (service NetworkDiscoveryService) ExecuteRequest(context map[string]interface{}) {
	var discoveryContext NetworkDiscoveryContext
	contextData, _ := json.Marshal(context)
	err := json.Unmarshal(contextData, &discoveryContext)
	if err != nil {
		logger.ServiceLogger.Error("[ExecuteRequest]", err)
	}
	for _, target := range discoveryContext.Targets {
		executeSnmpDiscovery(discoveryContext, target, service)
	}
}

func executeSnmpDiscovery(discoveryContext NetworkDiscoveryContext, target string, service NetworkDiscoveryService) {
	result := make(map[string]interface{})
	for _, CredentialProfile := range discoveryContext.CredentialProfiles {
		snmpContext := make(client.SNMPContext)
		for k, v := range CredentialProfile {
			snmpContext[k] = v
		}
		snmpContext["host"] = target
		snmpContext["port"] = discoveryContext.Port
		snmpContext["retry_count"] = discoveryContext.RetryCount
		snmpContext["timeout"] = discoveryContext.Timeout
		if strings.EqualFold(discoveryContext.DiscoveryType, "SNMP") {
			result = plugin.CollectUsingSNMP(snmpContext)
			if len(result) > 0 {
				status := result["status"].(string)
				if strings.EqualFold(status, "success") {
					result["credential-profile-id"] = CredentialProfile["id"]
					result["credential-profile-name"] = CredentialProfile["credential_profile_name"]
					break
				}
			}
		}
	}
	logger.ServiceLogger.Debug("discovery completed for " + target)
	result["host"] = target
	result["discovery_id"] = discoveryContext.DiscoveryId
	result["device_id"] = discoveryContext.DeviceId
	result["port"] = discoveryContext.Port
	result["retry_count"] = discoveryContext.RetryCount
	result["timeout"] = discoveryContext.Timeout
	result["organization"] = discoveryContext.Organization
	result["department"] = discoveryContext.Department
	result["polling_interval"] = discoveryContext.PollingInterval
	logger.ServiceLogger.Debug("sending discovery response for " + target)
	service.sendDiscoveryResponse(result)
}

func (service NetworkDiscoveryService) sendDiscoveryResponse(discoveryResponse map[string]interface{}) {
	var payload []byte
	bytes, err := json.Marshal(discoveryResponse)
	if err != nil {
		logger.DiscoveryLogger.Error("Error while Serializing JSON : %v.\n", err.Error())
	}
	url := common.MainServerUrl()
	tokenUrl := url + "/api/token"
	credential := map[string]string{"username": "zirozen", "password": "Zir@zen2@24"}
	payload, _ = json.Marshal(credential)
	logger.ServiceLogger.Debug("generating token for discovery response..")
	response, success := common.ExecutePostRequest(tokenUrl, payload, nil)
	if success && response != nil && len(response) > 0 {
		result := make(map[string]interface{})
		err := json.Unmarshal(response, &result)
		if err == nil && len(result["access-token"].(string)) > 0 {
			token := result["access-token"].(string)
			logger.ServiceLogger.Debug("token generated for discovery response now sending response..")
			apiUrl := url + "/api/zirozen/discovery"
			response, success = common.ExecutePostRequest(apiUrl, bytes, map[string]string{"Authorization": "Bearer " + token})
			if success && response != nil && len(response) > 0 {
				result = make(map[string]interface{})
				err := json.Unmarshal(response, &result)
				if err != nil {
					logger.ServiceLogger.Error("Failed to send discovery response.." + err.Error())
				}
				if status, ok := result["status"]; ok {
					if status != "success" {
						logger.ServiceLogger.Debug("Failed to send discovery response..")
					}
				}
			}
		} else {
			logger.ServiceLogger.Debug("Failed to parse generate access token response.")
		}
	} else {
		logger.ServiceLogger.Debug("Failed to generate access token to send discovery response.")
	}
}
