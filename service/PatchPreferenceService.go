package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"net/http"
	"reflect"
	"time"

	"github.com/google/uuid"
)

type PatchPreferenceService struct {
	Repository *repository.PatchPreferenceRepository
}

func NewPatchPreferenceService() *PatchPreferenceService {
	return &PatchPreferenceService{
		Repository: repository.NewPatchPreferenceRepo(),
	}
}

func (service PatchPreferenceService) convertToModel(rest rest.PatchPreferenceRest) *model.PatchPreference {
	var patchApprovalPolicy model.PatchApprovalPolicy
	patchApprovalPolicy = patchApprovalPolicy.ToRebootBehaviour(rest.PatchApprovalPolicy)

	var categoryList []model.PatchUpdateCategory
	for _, category := range rest.EnabledCategoryList {
		var pchCategory model.PatchUpdateCategory
		pchCategory = pchCategory.ToPatchCategory(category)
		categoryList = append(categoryList, pchCategory)
	}

	var osList []common.OsType
	for _, os := range rest.EnabledPatchOs {
		var pchOs common.OsType
		pchOs = pchOs.ToOsType(os)
		osList = append(osList, pchOs)
	}

	return &model.PatchPreference{
		BaseEntityModel:                  ConvertToBaseEntityModel(rest.BaseEntityRest),
		PatchApprovalPolicy:              patchApprovalPolicy,
		EnablePatching:                   rest.EnablePatching,
		EnableThirdPartyPatching:         rest.EnableThirdPartyPatching,
		EnabledCategoryList:              categoryList,
		EnabledPatchOs:                   osList,
		Schedule:                         convertToScheduleModel(rest.Schedule),
		PatchApprovalSchedule:            convertToScheduleModel(rest.PatchApprovalSchedule),
		ZeroTouchSchedule:                convertToScheduleModel(rest.ZeroTouchSchedule),
		EnableScheduling:                 rest.EnableScheduling,
		HighlyVulnerableCriticalPatch:    rest.HighlyVulnerableCriticalPatch,
		HighlyVulnerableImportantPatches: rest.HighlyVulnerableImportantPatches,
		HighlyVulnerableLowPatch:         rest.HighlyVulnerableLowPatch,
		HighlyVulnerableModeratePatch:    rest.HighlyVulnerableModeratePatch,
		VulnerableCriticalPatch:          rest.VulnerableCriticalPatch,
		VulnerableImportantPatches:       rest.VulnerableImportantPatches,
		VulnerableLowPatch:               rest.VulnerableLowPatch,
		VulnerableModeratePatch:          rest.VulnerableModeratePatch,
		OnlyApprovedPatch:                rest.OnlyApprovedPatch,
		IsPatchSyncRunning:               rest.IsPatchSyncRunning,
		LastPatchSyncTime:                rest.LastPatchSyncTime,
	}
}

func convertToScheduleModel(rest rest.PatchScheduleRest) model.PatchSchedule {
	return model.PatchSchedule{
		Type:  rest.Type,
		Value: rest.Value,
	}
}

func convertToScheduleRest(model model.PatchSchedule) rest.PatchScheduleRest {
	return rest.PatchScheduleRest{
		Type:  model.Type,
		Value: model.Value,
	}
}

func (service PatchPreferenceService) convertToRest(model model.PatchPreference) rest.PatchPreferenceRest {
	var categoryList []string
	for _, category := range model.EnabledCategoryList {
		categoryList = append(categoryList, category.String())
	}

	var osList []string
	for _, os := range model.EnabledPatchOs {
		osList = append(osList, os.String())
	}

	return rest.PatchPreferenceRest{
		BaseEntityRest:                   ConvertToBaseEntityRest(model.BaseEntityModel),
		PatchApprovalPolicy:              model.PatchApprovalPolicy.String(),
		EnablePatching:                   model.EnablePatching,
		EnableThirdPartyPatching:         model.EnableThirdPartyPatching,
		Schedule:                         convertToScheduleRest(model.Schedule),
		PatchApprovalSchedule:            convertToScheduleRest(model.PatchApprovalSchedule),
		ZeroTouchSchedule:                convertToScheduleRest(model.ZeroTouchSchedule),
		EnableScheduling:                 model.EnableScheduling,
		EnabledPatchOs:                   osList,
		EnabledCategoryList:              categoryList,
		HighlyVulnerableCriticalPatch:    model.HighlyVulnerableCriticalPatch,
		HighlyVulnerableImportantPatches: model.HighlyVulnerableImportantPatches,
		HighlyVulnerableLowPatch:         model.HighlyVulnerableLowPatch,
		HighlyVulnerableModeratePatch:    model.HighlyVulnerableModeratePatch,
		VulnerableCriticalPatch:          model.VulnerableCriticalPatch,
		VulnerableImportantPatches:       model.VulnerableImportantPatches,
		VulnerableLowPatch:               model.VulnerableLowPatch,
		VulnerableModeratePatch:          model.VulnerableModeratePatch,
		OnlyApprovedPatch:                model.OnlyApprovedPatch,
		IsPatchSyncRunning:               model.IsPatchSyncRunning,
		LastPatchSyncTime:                model.LastPatchSyncTime,
	}
}

func (service PatchPreferenceService) Create(rest rest.PatchPreferenceRest) (int64, error) {
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	patchPreference := service.convertToModel(rest)
	id, err := service.Repository.Create(patchPreference)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service PatchPreferenceService) Update(restModel rest.PatchPreferenceRest) (bool, common.CustomError) {
	patchPreference, err := service.Repository.Get()
	var rescheduleJob bool
	var reschedulePatchApprovalJob bool
	var reschedulePatchDeployJob bool
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if _, ok := restModel.PatchMap["patchApprovalPolicy"]; ok && patchPreference.PatchApprovalPolicy.String() != restModel.PatchApprovalPolicy {
		var patchApprovalPolicy model.PatchApprovalPolicy
		patchApprovalPolicy = patchApprovalPolicy.ToRebootBehaviour(restModel.PatchApprovalPolicy)
		patchPreference.PatchApprovalPolicy = patchApprovalPolicy
	}

	if _, ok := restModel.PatchMap["scheduleTime"]; ok {
		var pchSchedule model.PatchSchedule
		pchSchedule = convertToScheduleModel(restModel.Schedule)
		if !reflect.DeepEqual(patchPreference.Schedule, pchSchedule) {
			patchPreference.Schedule = pchSchedule
			rescheduleJob = true
		}
	}

	if _, ok := restModel.PatchMap["patchApprovalScheduleTime"]; ok {
		var pchSchedule model.PatchSchedule
		pchSchedule = convertToScheduleModel(restModel.PatchApprovalSchedule)
		if !reflect.DeepEqual(patchPreference.PatchApprovalSchedule, pchSchedule) {
			patchPreference.PatchApprovalSchedule = pchSchedule
			reschedulePatchApprovalJob = true
		}
	}

	if _, ok := restModel.PatchMap["zeroTouchDeploymentScheduleTime"]; ok {
		var pchSchedule model.PatchSchedule
		pchSchedule = convertToScheduleModel(restModel.ZeroTouchSchedule)
		if !reflect.DeepEqual(patchPreference.ZeroTouchSchedule, pchSchedule) {
			patchPreference.ZeroTouchSchedule = pchSchedule
			reschedulePatchDeployJob = true
		}
	}

	if _, ok := restModel.PatchMap["enabledCategoryList"]; ok {
		var categoryList []model.PatchUpdateCategory
		for _, category := range restModel.EnabledCategoryList {
			var pchCategory model.PatchUpdateCategory
			pchCategory = pchCategory.ToPatchCategory(category)
			categoryList = append(categoryList, pchCategory)
		}
		if !reflect.DeepEqual(categoryList, patchPreference.EnabledCategoryList) {
			patchPreference.EnabledCategoryList = categoryList
		}
	}

	if _, ok := restModel.PatchMap["enabledPatchOs"]; ok {
		var osList []common.OsType
		for _, os := range restModel.EnabledPatchOs {
			var pchOs common.OsType
			pchOs = pchOs.ToOsType(os)
			osList = append(osList, pchOs)
		}
		if !reflect.DeepEqual(osList, patchPreference.EnabledPatchOs) {
			patchPreference.EnabledPatchOs = osList
		}
	}

	if _, ok := restModel.PatchMap["highlyVulnerableCriticalPatch"]; ok && patchPreference.HighlyVulnerableCriticalPatch != restModel.HighlyVulnerableCriticalPatch {
		patchPreference.HighlyVulnerableCriticalPatch = restModel.HighlyVulnerableCriticalPatch
	}

	if _, ok := restModel.PatchMap["highlyVulnerableImportantPatches"]; ok && patchPreference.HighlyVulnerableImportantPatches != restModel.HighlyVulnerableImportantPatches {
		patchPreference.HighlyVulnerableImportantPatches = restModel.HighlyVulnerableImportantPatches
	}

	if _, ok := restModel.PatchMap["highlyVulnerableModeratePatch"]; ok && patchPreference.HighlyVulnerableModeratePatch != restModel.HighlyVulnerableModeratePatch {
		patchPreference.HighlyVulnerableModeratePatch = restModel.HighlyVulnerableModeratePatch
	}

	if _, ok := restModel.PatchMap["highlyVulnerableLowPatch"]; ok && patchPreference.HighlyVulnerableLowPatch != restModel.HighlyVulnerableLowPatch {
		patchPreference.HighlyVulnerableLowPatch = restModel.HighlyVulnerableLowPatch
	}

	if _, ok := restModel.PatchMap["vulnerableCriticalPatch"]; ok && patchPreference.VulnerableCriticalPatch != restModel.VulnerableCriticalPatch {
		patchPreference.VulnerableCriticalPatch = restModel.VulnerableCriticalPatch
	}

	if _, ok := restModel.PatchMap["vulnerableImportantPatches"]; ok && patchPreference.VulnerableImportantPatches != restModel.VulnerableImportantPatches {
		patchPreference.VulnerableImportantPatches = restModel.VulnerableImportantPatches
	}

	if _, ok := restModel.PatchMap["vulnerableModeratePatch"]; ok && patchPreference.VulnerableModeratePatch != restModel.VulnerableModeratePatch {
		patchPreference.VulnerableModeratePatch = restModel.VulnerableModeratePatch
	}

	if _, ok := restModel.PatchMap["vulnerableLowPatch"]; ok && patchPreference.VulnerableLowPatch != restModel.VulnerableLowPatch {
		patchPreference.VulnerableLowPatch = restModel.VulnerableLowPatch
	}

	if _, ok := restModel.PatchMap["onlyApprovedPatch"]; ok && patchPreference.OnlyApprovedPatch != restModel.OnlyApprovedPatch {
		patchPreference.OnlyApprovedPatch = restModel.OnlyApprovedPatch
	}

	if _, ok := restModel.PatchMap["enablePatching"]; ok && patchPreference.EnablePatching != restModel.EnablePatching {
		patchPreference.EnablePatching = restModel.EnablePatching
	}

	if _, ok := restModel.PatchMap["enableThirdPartyPatching"]; ok && patchPreference.EnableThirdPartyPatching != restModel.EnableThirdPartyPatching {
		patchPreference.EnableThirdPartyPatching = restModel.EnableThirdPartyPatching
	}

	if patchPreference.IsPatchSyncRunning != restModel.IsPatchSyncRunning {
		patchPreference.IsPatchSyncRunning = restModel.IsPatchSyncRunning
	}

	if patchPreference.LastPatchSyncTime != restModel.LastPatchSyncTime {
		patchPreference.LastPatchSyncTime = restModel.LastPatchSyncTime
	}

	_, err = service.Repository.Update(&patchPreference)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	jobExecutorService := NewJobExecutorService()

	if !patchPreference.EnablePatching {

		jobExecutorService.RemoveJob("patch-sync-job")

		jobExecutorService.RemoveJob("patch-approval-job")

		jobExecutorService.RemoveJob("auto-patch-deploy-job")
	}

	if patchPreference.EnablePatching && rescheduleJob {
		jobExecutorService.RemoveJob("patch-sync-job")
		if patchPreference.Schedule.Type == model.DAILY {
			jobExecutorService.AddOrRescheduleDailyJob("patch-sync-job", time.UnixMilli(patchPreference.Schedule.Value*1000), NewPatchSyncService().ExecutePatchSync)
		} else if patchPreference.Schedule.Type == model.INTERVAL {
			jobExecutorService.AddOrRescheduleIntervalJob("patch-sync-job", patchPreference.Schedule.Value, NewPatchSyncService().ExecutePatchSync)
		}
	}

	if patchPreference.EnablePatching && reschedulePatchApprovalJob {
		jobExecutorService.RemoveJob("patch-approval-job")
		if patchPreference.PatchApprovalSchedule.Type == model.DAILY {
			jobExecutorService.AddOrRescheduleDailyJob("patch-approval-job", time.UnixMilli(patchPreference.PatchApprovalSchedule.Value*1000), NewAutoPatchTestService().ExecutePatchTestJob)
		} else if patchPreference.PatchApprovalSchedule.Type == model.INTERVAL {
			jobExecutorService.AddOrRescheduleIntervalJob("patch-approval-job", patchPreference.PatchApprovalSchedule.Value, NewAutoPatchTestService().ExecutePatchTestJob)
		}
	}

	if patchPreference.EnablePatching && reschedulePatchDeployJob {
		jobExecutorService.RemoveJob("auto-patch-deploy-job")
		if patchPreference.ZeroTouchSchedule.Type == model.DAILY {
			jobExecutorService.AddOrRescheduleDailyJob("auto-patch-deploy-job", time.UnixMilli(patchPreference.ZeroTouchSchedule.Value*1000), NewAutoPatchDeployService().ExecuteAutoPatchDeployJob)
		} else if patchPreference.ZeroTouchSchedule.Type == model.INTERVAL {
			jobExecutorService.AddOrRescheduleIntervalJob("auto-patch-deploy-job", patchPreference.ZeroTouchSchedule.Value, NewAutoPatchDeployService().ExecuteAutoPatchDeployJob)
		}
	}

	return true, common.CustomError{}
}

func (service PatchPreferenceService) Get() (rest.PatchPreferenceRest, error) {
	var patchPreferenceRest rest.PatchPreferenceRest
	patchPreference, err := service.Repository.Get()
	if err != nil {
		return patchPreferenceRest, err
	}
	return service.convertToRest(patchPreference), nil
}

func (service PatchPreferenceService) CreateOOBPreference() {
	filter := rest.SearchFilter{
		Offset:          0,
		Size:            1,
		Qualification:   []rest.Qualification{},
		IncludeArchived: true,
	}
	query := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_PREFERENCE.String(), true, "")
	count := service.Repository.Count(query.Query)
	if count == 0 {
		patchPreference := model.PatchPreference{}
		patchPreference.Name = uuid.NewString()
		patchPreference.PatchApprovalPolicy = model.PRE_APPROVED
		patchPreference.HighlyVulnerableCriticalPatch = 3
		patchPreference.HighlyVulnerableImportantPatches = 3
		patchPreference.VulnerableModeratePatch = 3
		patchPreference.VulnerableLowPatch = 3
		patchPreference.EnablePatching = true
		_, err := service.Repository.Create(&patchPreference)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	patchPreference, err := service.Repository.Get()
	if err == nil {
		jobExecutorService := NewJobExecutorService()
		if patchPreference.EnablePatching {
			if patchPreference.Schedule.Type == model.DAILY {
				jobExecutorService.AddOrRescheduleDailyJob("patch-sync-job", time.UnixMilli(patchPreference.Schedule.Value*1000), NewPatchSyncService().ExecutePatchSync)
			} else if patchPreference.Schedule.Type == model.INTERVAL {
				jobExecutorService.AddOrRescheduleIntervalJob("patch-sync-job", patchPreference.Schedule.Value, NewPatchSyncService().ExecutePatchSync)
			}

			if patchPreference.PatchApprovalSchedule.Type == model.DAILY {
				jobExecutorService.AddOrRescheduleDailyJob("patch-approval-job", time.UnixMilli(patchPreference.PatchApprovalSchedule.Value*1000), NewAutoPatchTestService().ExecutePatchTestJob)
			} else if patchPreference.PatchApprovalSchedule.Type == model.INTERVAL {
				jobExecutorService.AddOrRescheduleIntervalJob("patch-approval-job", patchPreference.PatchApprovalSchedule.Value, NewAutoPatchTestService().ExecutePatchTestJob)
			}

			if patchPreference.ZeroTouchSchedule.Type == model.DAILY {
				jobExecutorService.AddOrRescheduleDailyJob("auto-patch-deploy-job", time.UnixMilli(patchPreference.ZeroTouchSchedule.Value*1000), NewAutoPatchDeployService().ExecuteAutoPatchDeployJob)
			} else if patchPreference.ZeroTouchSchedule.Type == model.INTERVAL {
				jobExecutorService.AddOrRescheduleIntervalJob("auto-patch-deploy-job", patchPreference.ZeroTouchSchedule.Value, NewAutoPatchDeployService().ExecuteAutoPatchDeployJob)
			}
		}
	}
}
