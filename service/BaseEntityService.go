package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"encoding/json"
	"fmt"
	"net/http"
)

func PerformPartialUpdateForBase(entityModel *model.BaseEntityModel, entityRest rest.BaseEntityRest) map[string]map[string]interface{} {
	diffMap := map[string]map[string]interface{}{}

	if entityRest.PatchMap["name"] != nil && entityModel.Name != entityRest.Name {
		common.PrepareInDiffMap("name", entityModel.Name, entityRest.Name, &diffMap)
		entityModel.Name = entityRest.Name
	}

	if entityRest.PatchMap["updatedById"] != nil && entityModel.UpdatedById != entityRest.UpdatedById {
		common.PrepareInDiffMap("updated_by_id", entityModel.UpdatedById, entityRest.UpdatedById, &diffMap)
		entityModel.UpdatedById = entityRest.UpdatedById
	}

	if entityRest.PatchMap["updatedTime"] != nil && entityModel.UpdatedTime != entityRest.UpdatedTime {
		common.PrepareInDiffMap("updated_time", entityModel.UpdatedTime, entityRest.UpdatedTime, &diffMap)
		entityModel.UpdatedTime = entityRest.UpdatedTime
	}

	return diffMap
}

func PerformPartialUpdateForBaseRef(entityRefModel *model.BaseEntityRefModel, refModelRest rest.BaseEntityRefModelRest) map[string]map[string]interface{} {
	diffMap := PerformPartialUpdateForBase(&entityRefModel.BaseEntityModel, refModelRest.BaseEntityRest)

	if refModelRest.PatchMap["refId"] != nil && entityRefModel.RefId != refModelRest.RefId {
		common.PrepareInDiffMap("ref_id", entityRefModel.RefId, refModelRest.RefId, &diffMap)
		entityRefModel.RefId = refModelRest.RefId
	}

	if refModelRest.PatchMap["refModel"] != nil && entityRefModel.RefModel != refModelRest.RefModel {
		common.PrepareInDiffMap("ref_model", entityRefModel.RefModel, refModelRest.RefModel, &diffMap)
		entityRefModel.RefModel = refModelRest.RefModel
	}

	return diffMap
}

func ConvertToBaseEntityModel(rest rest.BaseEntityRest) model.BaseEntityModel {
	return model.BaseEntityModel{
		Id:          rest.Id,
		Name:        rest.Name,
		CreatedById: rest.CreatedById,
		CreatedTime: rest.CreatedTime,
		UpdatedById: rest.UpdatedById,
		UpdatedTime: rest.UpdatedTime,
		OOB:         rest.OOB,
		Removed:     rest.Removed,
	}
}

func ConvertToBaseEntityRefModel(rest rest.BaseEntityRefModelRest) model.BaseEntityRefModel {
	baseModel := ConvertToBaseEntityModel(rest.BaseEntityRest)
	return model.BaseEntityRefModel{
		BaseEntityModel: baseModel,
		RefId:           rest.RefId,
		RefModel:        rest.RefModel,
	}
}

func ConvertToBaseEntityRest(entityModel model.BaseEntityModel) rest.BaseEntityRest {
	return rest.BaseEntityRest{
		Id:          entityModel.Id,
		Name:        entityModel.Name,
		CreatedById: entityModel.CreatedById,
		CreatedTime: entityModel.CreatedTime,
		UpdatedById: entityModel.UpdatedById,
		UpdatedTime: entityModel.UpdatedTime,
		OOB:         entityModel.OOB,
		Removed:     entityModel.Removed,
	}
}

func ConvertToBaseEntityRefModelRest(entityModel model.BaseEntityRefModel) rest.BaseEntityRefModelRest {
	baseRest := ConvertToBaseEntityRest(entityModel.BaseEntityModel)
	return rest.BaseEntityRefModelRest{
		BaseEntityRest: baseRest,
		RefId:          entityModel.RefId,
		RefModel:       entityModel.RefModel,
	}
}

func ConvertJsonToRequestByIdsRest(w http.ResponseWriter, r *http.Request, requestByIds rest.RequestByIdsAndModel) (rest.RequestByIdsAndModel, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &requestByIds)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error : %s", err.Error()), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ConvertJsonToRequestByIdsRest]", err)
		}
		return requestByIds, err
	}
	return requestByIds, err
}

func ConvertJsonToBulkUpdateRequest(w http.ResponseWriter, r *http.Request, bulkUpdateRequest rest.BulkUpdateRequest) (rest.BulkUpdateRequest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &bulkUpdateRequest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error : %s", err.Error()), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ConvertJsonToBulkUpdateRequest]", err)
		}
		return bulkUpdateRequest, err
	}
	return bulkUpdateRequest, err
}

func ConvertJsonToBulkDeleteRequest(w http.ResponseWriter, r *http.Request, bulkDeleteRequest rest.BulkDeleteRequest) (rest.BulkDeleteRequest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &bulkDeleteRequest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Error : %s", err.Error()), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[ConvertJsonToBulkDeleteRequest]", err)
		}
		return bulkDeleteRequest, err
	}
	return bulkDeleteRequest, err
}

func ConvertToAgentScopeFilter(filterRest rest.AgentScopeFilterRest) model.AgentScopeFilter {
	return model.AgentScopeFilter{
		Scope:            filterRest.Scope,
		Assets:           filterRest.Assets,
		PlatformVersions: filterRest.PlatformVersions,
	}
}

func ConvertToAgentScopeFilterRest(filter model.AgentScopeFilter) rest.AgentScopeFilterRest {
	return rest.AgentScopeFilterRest{
		Scope:            filter.Scope,
		Assets:           filter.Assets,
		PlatformVersions: filter.PlatformVersions,
	}
}

func ConvertToComputerGroupFilter(filter rest.ComputerGroupFilterRest) model.ComputerGroupFilter {
	var cgType model.CGFilterType
	return model.ComputerGroupFilter{
		AgentScopeFilter: ConvertToAgentScopeFilter(filter.AgentScopeFilterRest),
		CGFilterType:     cgType.ToCGFilterType(filter.CGFilterType),
		ComputerGroupIds: filter.ComputerGroupIds,
	}
}

func ConvertToComputerGroupFilterRest(filter model.ComputerGroupFilter) rest.ComputerGroupFilterRest {
	return rest.ComputerGroupFilterRest{
		AgentScopeFilterRest: ConvertToAgentScopeFilterRest(filter.AgentScopeFilter),
		CGFilterType:         filter.CGFilterType.String(),
		ComputerGroupIds:     filter.ComputerGroupIds,
	}
}
