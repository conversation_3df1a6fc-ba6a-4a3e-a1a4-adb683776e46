package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strings"
)

type SystemActionService struct {
	Repository *repository.SystemActionRepository
}

func NewSystemActionService() *SystemActionService {
	return &SystemActionService{
		Repository: repository.NewSystemActionRepository(),
	}
}

func (service SystemActionService) convertToModel(sysAction rest.SystemActionRest) *model.SystemAction {
	return &model.SystemAction{
		BaseEntityModel: ConvertToBaseEntityModel(sysAction.BaseEntityRest),
		Description:     sysAction.Description,
		Disabled:        sysAction.Disabled,
		WindowsCommand:  rest.ToSystemActionConfigList(sysAction.WindowsCommand),
		LinuxCommand:    rest.ToSystemActionConfigList(sysAction.LinuxCommand),
		MacCommand:      rest.ToSystemActionConfigList(sysAction.MacCommand),
	}
}

func (service SystemActionService) convertToRest(systemAction model.SystemAction) rest.SystemActionRest {
	return rest.SystemActionRest{
		BaseEntityRest: ConvertToBaseEntityRest(systemAction.BaseEntityModel),
		Description:    systemAction.Description,
		Disabled:       systemAction.Disabled,
		WindowsCommand: rest.ToSystemActionConfigRestList(systemAction.WindowsCommand),
		LinuxCommand:   rest.ToSystemActionConfigRestList(systemAction.LinuxCommand),
		MacCommand:     rest.ToSystemActionConfigRestList(systemAction.MacCommand),
	}
}

func (service SystemActionService) Create(rest rest.SystemActionRest) (int64, error) {
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	systemAction := service.convertToModel(rest)
	id, err := service.Repository.Create(systemAction)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service SystemActionService) Update(id int64, restModel rest.SystemActionRest) (bool, common.CustomError) {
	systemAction, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if _, ok := restModel.PatchMap["name"]; ok && systemAction.Name != restModel.Name {
		systemAction.Name = restModel.Name
	}
	if _, ok := restModel.PatchMap["description"]; ok && systemAction.Description != restModel.Description {
		systemAction.Description = restModel.Description
	}
	if _, ok := restModel.PatchMap["disabled"]; ok && systemAction.Disabled != restModel.Disabled {
		systemAction.Disabled = restModel.Disabled
	}
	if _, ok := restModel.PatchMap["windowsCommand"]; ok && !reflect.DeepEqual(systemAction.WindowsCommand, restModel.WindowsCommand) {
		systemAction.WindowsCommand = rest.ToSystemActionConfigList(restModel.WindowsCommand)
	}
	if _, ok := restModel.PatchMap["linuxCommand"]; ok && !reflect.DeepEqual(systemAction.LinuxCommand, restModel.LinuxCommand) {
		systemAction.LinuxCommand = rest.ToSystemActionConfigList(restModel.LinuxCommand)
	}
	if _, ok := restModel.PatchMap["macCommand"]; ok && !reflect.DeepEqual(systemAction.MacCommand, restModel.MacCommand) {
		systemAction.MacCommand = rest.ToSystemActionConfigList(restModel.MacCommand)
	}

	_, err = service.Repository.Update(&systemAction)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	return true, common.CustomError{}
}

func (service SystemActionService) GetById(id int64) (rest.SystemActionRest, error) {
	var systemActionRest rest.SystemActionRest
	systemAction, err := service.Repository.GetById(id)
	if err != nil {
		return systemActionRest, err
	}
	return service.convertToRest(systemAction), nil
}

func (service SystemActionService) DeleteById(id int64) (bool, error) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, err
	}
	return success, nil
}

func (service SystemActionService) GetAllSystemActions(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var systemActionList []model.SystemAction
	var err error
	tableName := common.SYSTEM_ACTION.String()
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		systemActionList, err = service.Repository.GetAllSystemAction(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.convertListToRest(systemActionList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service SystemActionService) convertListToRest(actionList []model.SystemAction) []rest.SystemActionRest {
	var actionRestList []rest.SystemActionRest
	if len(actionList) != 0 {
		for _, configuration := range actionList {
			configurationRest := service.convertToRest(configuration)
			actionRestList = append(actionRestList, configurationRest)
		}
	}
	return actionRestList
}

func (service SystemActionService) CreateSystemActionsAgentTasks(context map[string]interface{}) {
	var actionCtx rest.SystemActionContext
	contextData, _ := json.Marshal(context)
	logger.ServiceLogger.Info("CreateSystemActionsAgentTasks : ", contextData)
	err := json.Unmarshal(contextData, &actionCtx)
	if err != nil {
		logger.ServiceLogger.Error("[CreateSystemActionsAgentTasks] error while json unmarshal", err)
		return
	}

	if actionCtx.Actions != nil {
		agentTaskService := NewAgentTaskService()
		for _, actionId := range actionCtx.Actions {
			sysAction, err := service.GetById(actionId)
			if err == nil {
				agentTask := rest.AgentTaskRest{}
				agentTask.AgentId = actionCtx.AssetId
				agentTask.RefId = actionId
				agentTask.RefModel = common.SYSTEM_ACTION.String()
				agentTask.TaskStatus = model.TaskReadyToDeploy.String()
				agentTask.TaskType = model.SYSTEM_ACTION.String()
				var macCommand []rest.SystemActionConfigRest
				var linuxCommand []rest.SystemActionConfigRest
				var windowsCommand []rest.SystemActionConfigRest
				for _, ctxMap := range actionCtx.Context {
					if len(sysAction.MacCommand) > 0 {
						for _, configRest := range sysAction.MacCommand {
							commandConfig := rest.SystemActionConfigRest{}
							commandConfig.OrderId = configRest.OrderId
							commandConfig.CommandType = configRest.CommandType
							for key, val := range ctxMap {
								configRest.Command = strings.ReplaceAll(configRest.Command, "{$"+key+"}", fmt.Sprint(val))
							}
							commandConfig.Command = configRest.Command
							macCommand = append(macCommand, commandConfig)
						}
					}
					if len(sysAction.LinuxCommand) > 0 {
						for _, configRest := range sysAction.LinuxCommand {
							commandConfig := rest.SystemActionConfigRest{}
							commandConfig.OrderId = configRest.OrderId
							commandConfig.CommandType = configRest.CommandType
							var isValid = true
							if configRest.Platform != "" {
								isValid = false
								if strings.EqualFold(configRest.Platform, actionCtx.Platform) {
									isValid = true
								}
							}
							if isValid {
								for key, val := range ctxMap {
									configRest.Command = strings.ReplaceAll(configRest.Command, "{$"+key+"}", fmt.Sprint(val))
								}
								commandConfig.Command = configRest.Command
								linuxCommand = append(linuxCommand, commandConfig)
							}
						}
					}
					if len(sysAction.WindowsCommand) > 0 {
						for _, configRest := range sysAction.WindowsCommand {
							commandConfig := rest.SystemActionConfigRest{}
							commandConfig.OrderId = configRest.OrderId
							commandConfig.CommandType = configRest.CommandType
							for key, val := range ctxMap {
								configRest.Command = strings.ReplaceAll(configRest.Command, "{$"+key+"}", fmt.Sprint(val))
							}
							commandConfig.Command = configRest.Command
							windowsCommand = append(windowsCommand, commandConfig)
						}
					}
				}

				if len(windowsCommand) == 0 && len(sysAction.WindowsCommand) > 0 {
					windowsCommand = append(windowsCommand, sysAction.WindowsCommand...)
				}
				if len(linuxCommand) == 0 && len(sysAction.LinuxCommand) > 0 {
					linuxCommand = append(linuxCommand, sysAction.LinuxCommand...)
				}
				if len(macCommand) == 0 && len(sysAction.MacCommand) > 0 {
					macCommand = append(macCommand, sysAction.MacCommand...)
				}

				if "windows" == strings.ToLower(actionCtx.PlatformType) {
					agentTask.CustomTaskDetails = map[string]interface{}{
						"command": windowsCommand,
					}
				} else if "darwin" == strings.ToLower(actionCtx.PlatformType) || "mac" == strings.ToLower(actionCtx.PlatformType) {
					agentTask.CustomTaskDetails = map[string]interface{}{
						"command": macCommand,
					}
				} else {
					agentTask.CustomTaskDetails = map[string]interface{}{
						"command": linuxCommand,
					}
				}

				_, err = agentTaskService.Create(agentTask)
				if err != nil {
					logger.ServiceLogger.Error("[CreateSystemActionsAgentTasks] creating agent task ", err)
					return
				}
			}
		}
	}
}

func (service SystemActionService) BulkCreate(restList []rest.SystemActionRest) {
	if len(restList) > 0 {
		for _, packageRest := range restList {
			_, err := service.Create(packageRest)
			if err != nil {
				logger.ServiceLogger.Error("[SystemActionService][BulkCreate]", err)
			}
		}
	}
}

func (service SystemActionService) CreateOOBSystemAction() {
	filter := rest.SearchFilter{
		Offset:          0,
		Size:            1,
		Qualification:   []rest.Qualification{},
		IncludeArchived: true,
	}
	// Use secure parameterized queries to prevent SQL injection
	queryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.SYSTEM_ACTION.String(), true, "")
	count := service.Repository.Count(queryResult.Query, queryResult.Parameters)
	if count == 0 {
		killProcessAction(service)
		deleteFileFolderAction(service)
		rebootAction(service)
		powerOffAction(service)
		blockIpAddress(service)
		blockPort(service)
		deleteUser(service)
		stopServices(service)
		startServices(service)
		wakeOnLan(service)
	}
}

func deleteUser(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Delete User"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "net user {$username} /delete",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo userdel -r {$username}",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo /usr/bin/dscl . -delete \"/Users/<USER>"",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][deleteUser]", err)
	}
}

func blockPort(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Block Port"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "netsh advfirewall firewall add rule name=\"Block - {$protocol} - {$port}\" protocol={$protocol} dir=out remoteport={$port} action=block",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo iptables -A INPUT -p {$protocol} -dport {$port} -j DROP",
			Platform:    "rhel",
		},
		{
			OrderId:     1,
			CommandType: model.CMD.String(),
			Command:     "sudo service iptables save",
			Platform:    "rhel",
		},
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo ufw deny proto {$protocol} to any port {$port}",
			Platform:    "ubuntu",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][blockPort]", err)
	}
}

func blockIpAddress(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Block Ip Address"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "netsh advfirewall firewall add rule name=\"Block IP Address - {$ipAddress}\" dir=in action=block remoteip={$ipAddress}",
		},
		{
			OrderId:     1,
			CommandType: model.CMD.String(),
			Command:     " netsh advfirewall firewall add rule name=\"Block IP Address - {$ipAddress}\" dir=out action=block remoteip={$ipAddress}",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo iptables -I INPUT -s {$ipAddress} -j DROP",
			Platform:    "rhel",
		},
		{
			OrderId:     1,
			CommandType: model.CMD.String(),
			Command:     "sudo service iptables save",
			Platform:    "rhel",
		},
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "ufw deny from {$ipAddress}",
			Platform:    "ubuntu",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][blockIpAddress]", err)
	}
}

func powerOffAction(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "PowerOff"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "shutdown /s",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo shutdown -h now",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo shutdown -h now",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][powerOffAction]", err)
	}
}

func rebootAction(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Reboot"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "shutdown /r",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "init 6",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "sudo shutdown -r now",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][rebootAction]", err)
	}
}

func deleteFileFolderAction(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Delete Folders / Files"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "del /f \"{$file}\"",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "rm -rf {$file}",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "rm -rf {$file}",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][deleteFileFolderAction]", err)
	}
}

func killProcessAction(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Kill Process"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "taskkill /F /PID {$pid}",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "kill -9 {$pid}",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "kill -9 {$pid}",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][killProcessAction]", err)
	}
}

func stopServices(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Stop Services"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "net stop {$service}",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "systemctl stop {$service}",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "systemctl stop {$service}",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][stopServices]", err)
	}
}

func startServices(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Start Services"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "net start {$service}",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "systemctl start {$service}",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "systemctl start {$service}",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][startServices]", err)
	}
}

func wakeOnLan(service SystemActionService) {
	actionRest := rest.SystemActionRest{}
	actionRest.Name = "Wake On LAN"
	actionRest.WindowsCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "'C:\\Progam Files\\zirozen\\endpointops.exe' --wol --mac=\"{$mac}\"",
		},
	}
	actionRest.LinuxCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "/opt/zirozen/agent/endpointops --wol --mac=\"{$mac}\"",
		},
	}
	actionRest.MacCommand = []rest.SystemActionConfigRest{
		{
			OrderId:     0,
			CommandType: model.CMD.String(),
			Command:     "/opt/zirozen/agent/endpointops --wol --mac=\"{$mac}\"",
		},
	}
	_, err := service.Create(actionRest)
	if err != nil {
		logger.ServiceLogger.Error("[SystemActionService][wakeOnLan]", err)
	}
}
