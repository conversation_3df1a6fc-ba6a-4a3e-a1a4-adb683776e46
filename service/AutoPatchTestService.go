package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
)

type AutoPatchTestService struct {
	Repository *repository.AutoPatchTestRepository
}

func NewAutoPatchTestService() *AutoPatchTestService {
	return &AutoPatchTestService{
		Repository: repository.NewAutoPatchTestRepository(),
	}
}

func (service *AutoPatchTestService) convertToModel(restTest rest.AutoPatchTestRest) *model.AutoPatchTest {
	var appType model.APApplicationType
	appType = appType.ToAPApplicationType(restTest.ApplicationType)

	var patchCategories []model.PatchUpdateCategory
	for _, patchCategory := range restTest.PatchCategories {
		var pchcategory model.PatchUpdateCategory
		patchCategories = append(patchCategories, pchcategory.ToPatchCategory(strings.ToLower(patchCategory)))
	}

	var severityList []model.PatchSeverity
	for _, sev := range restTest.PatchSeverities {
		var severity model.PatchSeverity
		severityList = append(severityList, severity.ToPatchSeverity(strings.ToLower(sev)))
	}

	return &model.AutoPatchTest{
		BaseEntityModel:     ConvertToBaseEntityModel(restTest.BaseEntityRest),
		DisplayName:         restTest.DisplayName,
		Description:         restTest.Description,
		PatchCategories:     patchCategories,
		PatchSeverities:     severityList,
		Platform:            restTest.Platform,
		ApplicationType:     appType,
		ProductIds:          restTest.ProductIds,
		ComputerGroupFilter: ConvertToComputerGroupFilter(restTest.ComputerGroupFilterRest),
		DeploymentPolicyId:  restTest.DeploymentPolicyId,
		NotifyTo:            restTest.NotifyTo,
	}
}

func (service *AutoPatchTestService) convertToRest(test model.AutoPatchTest) rest.AutoPatchTestRest {
	var severityList []string
	for _, sev := range test.PatchSeverities {
		severityList = append(severityList, sev.String())
	}

	var categoryList []string
	for _, category := range test.PatchCategories {
		categoryList = append(categoryList, category.String())
	}

	return rest.AutoPatchTestRest{
		BaseEntityRest:          ConvertToBaseEntityRest(test.BaseEntityModel),
		DisplayName:             test.DisplayName,
		Description:             test.Description,
		PatchCategories:         categoryList,
		PatchSeverities:         severityList,
		Platform:                test.Platform,
		ApplicationType:         test.ApplicationType.String(),
		ProductIds:              test.ProductIds,
		ComputerGroupFilterRest: ConvertToComputerGroupFilterRest(test.ComputerGroupFilter),
		DeploymentPolicyId:      test.DeploymentPolicyId,
		NotifyTo:                test.NotifyTo,
	}
}

func (service *AutoPatchTestService) Create(restTest rest.AutoPatchTestRest) (int64, error) {
	restTest.CreatedTime = common.CurrentMillisecond()
	restTest.CreatedById = common.GetUserFromCallContext()
	test := service.convertToModel(restTest)
	id, err := service.Repository.Create(test)
	if err != nil {
		return 0, fmt.Errorf("failed to create AutoPatchTest: %w", err)
	}
	return id, nil
}

func (service *AutoPatchTestService) Update(id int64, restTest rest.AutoPatchTestRest) (bool, common.CustomError) {
	test, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Apply updates based on PatchMap
	if _, ok := restTest.PatchMap["displayName"]; ok && test.DisplayName != restTest.DisplayName {
		test.DisplayName = restTest.DisplayName
	}
	if _, ok := restTest.PatchMap["description"]; ok && test.Description != restTest.Description {
		test.Description = restTest.Description
	}
	if _, ok := restTest.PatchMap["patchCategories"]; ok {
		var patchCategories []model.PatchUpdateCategory
		for _, patchCategory := range restTest.PatchCategories {
			var category model.PatchUpdateCategory
			patchCategories = append(patchCategories, category.ToPatchCategory(strings.ToLower(patchCategory)))
		}
		if !reflect.DeepEqual(test.PatchCategories, patchCategories) {
			test.PatchCategories = patchCategories
		}
	}
	if _, ok := restTest.PatchMap["patchSeverities"]; ok {
		var patchSeverities []model.PatchSeverity
		for _, patchSeverity := range restTest.PatchSeverities {
			var severity model.PatchSeverity
			patchSeverities = append(patchSeverities, severity.ToPatchSeverity(strings.ToLower(patchSeverity)))
		}
		if !reflect.DeepEqual(test.PatchSeverities, patchSeverities) {
			test.PatchSeverities = patchSeverities
		}
	}

	if _, ok := restTest.PatchMap["computerGroupFilterRest"]; ok {
		var computerGroupFilter model.ComputerGroupFilter
		computerGroupFilter = ConvertToComputerGroupFilter(restTest.ComputerGroupFilterRest)
		if !reflect.DeepEqual(test.ComputerGroupFilter, computerGroupFilter) {
			test.ComputerGroupFilter = computerGroupFilter
		}
	}

	if _, ok := restTest.PatchMap["platform"]; ok && test.Platform != restTest.Platform {
		test.Platform = restTest.Platform
	}
	if _, ok := restTest.PatchMap["productIds"]; ok && !reflect.DeepEqual(test.ProductIds, restTest.ProductIds) {
		test.ProductIds = restTest.ProductIds
	}
	if _, ok := restTest.PatchMap["notifyTo"]; ok && !reflect.DeepEqual(test.NotifyTo, restTest.NotifyTo) {
		test.NotifyTo = restTest.NotifyTo
	}

	_, err = service.Repository.Update(&test)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	return true, common.CustomError{}
}

func (service *AutoPatchTestService) GetById(id int64) (rest.AutoPatchTestRest, error) {
	test, err := service.Repository.GetById(id)
	if err != nil {
		return rest.AutoPatchTestRest{}, fmt.Errorf("failed to get AutoPatchTest by id %d: %w", id, err)
	}
	return service.convertToRest(test), nil
}

func (service *AutoPatchTestService) DeleteById(id int64) (bool, error) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, fmt.Errorf("failed to delete AutoPatchTest by id %d: %w", id, err)
	}
	return success, nil
}

func (service *AutoPatchTestService) GetAllTests(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var testList []model.AutoPatchTest
	var err error
	tableName := common.AUTO_PATCH_TEST.String()
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count, err := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if err != nil {
		return responsePage, fmt.Errorf("failed to count AutoPatchTests: %w", err)
	}
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		testList, err = service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, fmt.Errorf("failed to get all AutoPatchTests: %w", err)
		}
	}
	responsePage.ObjectList = service.convertListToRest(testList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *AutoPatchTestService) convertListToRest(tests []model.AutoPatchTest) []rest.AutoPatchTestRest {
	var testRestList []rest.AutoPatchTestRest
	for _, test := range tests {
		testRestList = append(testRestList, service.convertToRest(test))
	}
	return testRestList
}

func (service AutoPatchTestService) ExecutePatchTestJob() {
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(rest.SearchFilter{}, common.AUTO_PATCH_TEST.String(), false, "")
	autoPatchTests, err := service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		logger.ServiceLogger.Error("Error while ExecutePatchTestJob", err.Error())
	}
	if len(autoPatchTests) > 0 {
		assetPatchRelationService := NewAssetPatchRelationService()
		patchService := NewPatchService()
		for _, autoPatchTest := range autoPatchTests {
			allAssetIds := GetAllEndpointIdsFromCGFilter(autoPatchTest.ComputerGroupFilter)
			if len(allAssetIds) > 0 {
				patchList, err := assetPatchRelationService.GetAllAgentPatchRelation(rest.SearchFilter{
					Qualification: []rest.Qualification{
						rest.BuildQualification("asset_id", "in", allAssetIds, "and"),
						rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Missing), "and"),
					},
				})
				if err != nil {
					logger.ServiceLogger.Error("Error while retrieving patch list ExecutePatchTestJob", err.Error())
				}
				var patchIds []int64
				var qualifiedPatches []int64
				uniqueIds := make(map[int64]struct{})      // A map to store unique IDs
				uniquePatchIds := make(map[int64]struct{}) // A map to store unique IDs
				if len(patchList) > 0 {
					for _, relationRest := range patchList {
						if _, exists := uniquePatchIds[relationRest.PatchId]; !exists {
							uniquePatchIds[relationRest.PatchId] = struct{}{}
							patchIds = append(patchIds, relationRest.PatchId)
						}
					}
					var severityList []string
					var categories []string
					thirdPartyEnabled := false
					if autoPatchTest.PatchSeverities != nil && len(autoPatchTest.PatchSeverities) > 0 {
						for _, severity := range autoPatchTest.PatchSeverities {
							severityList = append(severityList, fmt.Sprintf("%d", severity))
						}
					}
					if autoPatchTest.PatchCategories != nil && len(autoPatchTest.PatchCategories) > 0 {
						for _, category := range autoPatchTest.PatchCategories {
							if category == model.THIRD_PARTY {
								thirdPartyEnabled = true
							} else {
								categories = append(categories, fmt.Sprintf("%d", category))
							}
						}
					}

					var qualifications []rest.Qualification
					qualifications = append(qualifications, rest.BuildQualification("tags", "not_equals", "Third Party", "and"))
					qualifications = append(qualifications, rest.BuildQualification("id", "in", patchIds, "and"))
					qualifications = append(qualifications, rest.BuildQualification("patch_approval_status", "equals", model.NOT_APPROVED.String(), "and"))
					qualifications = append(qualifications, rest.BuildQualification("patch_test_status", "equals", model.NOT_TESTED.String(), "and"))

					if autoPatchTest.Platform != "" {
						qualifications = append(qualifications, rest.BuildQualification("os_platform", "equals", autoPatchTest.Platform, "and"))
					}

					if len(severityList) > 0 {
						qualifications = append(qualifications, rest.BuildQualification("patch_severity", "in", severityList, "and"))
					}

					if len(categories) > 0 {
						qualifications = append(qualifications, rest.BuildQualification("patch_update_category", "in", categories, "and"))
					}

					if autoPatchTest.ApplicationType != model.AllApplication {
						if autoPatchTest.ApplicationType == model.IncludeApplication {
							qualifications = append(qualifications, rest.BuildQualification("affected_products", "equals", autoPatchTest.ProductIds, "and"))
						} else if autoPatchTest.ApplicationType == model.ExcludeApplication {
							qualifications = append(qualifications, rest.BuildQualification("affected_products", "not_equals", autoPatchTest.ProductIds, "and"))
						}
					}
					patches, err := patchService.GetAllPatches(rest.SearchFilter{Qualification: qualifications})
					if err != nil {
						logger.ServiceLogger.Error("Error while retrieving patches ExecutePatchTestJob", err.Error())
					}
					for _, patch := range patches {
						if _, exists := uniqueIds[patch.Id]; !exists {
							uniqueIds[patch.Id] = struct{}{}
							qualifiedPatches = append(qualifiedPatches, patch.Id)
						}
					}

					if thirdPartyEnabled {
						var thirdPartyQualifications []rest.Qualification
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("tags", "equals", "Third Party", "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("id", "in", patchIds, "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_approval_status", "equals", model.NOT_APPROVED.String(), "and"))
						thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_test_status", "equals", model.NOT_TESTED.String(), "and"))

						if autoPatchTest.Platform != "" {
							thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("os_platform", "equals", autoPatchTest.Platform, "and"))
						}

						if len(severityList) > 0 {
							thirdPartyQualifications = append(thirdPartyQualifications, rest.BuildQualification("patch_severity", "in", severityList, "and"))
						}

						thirdPartyPatches, err := patchService.GetAllPatches(rest.SearchFilter{Qualification: thirdPartyQualifications})
						if err != nil {
							logger.ServiceLogger.Error("Error while retrieving patches ExecutePatchTestJob", err.Error())
						}
						for _, patch := range thirdPartyPatches {
							if _, exists := uniqueIds[patch.Id]; !exists {
								uniqueIds[patch.Id] = struct{}{}
								qualifiedPatches = append(qualifiedPatches, patch.Id)
							}
						}
					}
				}

				if len(qualifiedPatches) > 0 {
					deployment := rest.DeploymentRest{
						AgentScopeFilterRest: rest.AgentScopeFilterRest{
							Scope:  int64(model.SpecificAssets),
							Assets: allAssetIds,
						},
						DisplayName:        "Auto Patch test - " + autoPatchTest.DisplayName,
						DeploymentType:     model.Install.String(),
						DeploymentStage:    model.Initiated.String(),
						RefIds:             qualifiedPatches,
						DeploymentPolicyId: constant.DefaultInstantDeploymentPolicyId,
						Origin:             model.AUTO_PATCH_TEST.String(),
					}
					deployment.RefModel = "Patch"
					deployment.RefId = autoPatchTest.Id
					_, err = NewDeploymentService().Create(deployment)
					if err != nil {
						logger.ServiceLogger.Error("Error while creating deployment [ExecutePatchTestJob]", err)
					}
				}
			}
		}
	}
}

func (service AutoPatchTestService) UpdatePatchTestStatus(deploymentId int64) {
	agentTasks, _ := NewAgentTaskService().GetAgentTasks(rest.SearchFilter{
		Qualification: []rest.Qualification{{
			Column:   "deploymentId",
			Operator: "Equals",
			Value:    strconv.FormatInt(deploymentId, 10),
		}},
	})
	var successPatchIds []int64
	var failedPatchIds []int64
	if agentTasks != nil && len(agentTasks) > 0 {
		for _, agentTask := range agentTasks {
			if agentTask.TaskStatus == model.TaskSuccess.String() {
				successPatchIds = append(successPatchIds, agentTask.RefId)
			} else {
				failedPatchIds = append(failedPatchIds, agentTask.RefId)
			}
		}
	}

	for _, pchId := range successPatchIds {
		pch, err := NewPatchService().Repository.GetById(pchId, false)
		if err == nil {
			pch.PatchTestStatus = model.TEST_SUCCESS
			pch.PatchApprovalStatus = model.APPROVED
			_, err = NewPatchService().Repository.Update(&pch)
			if err != nil {
				logger.ServiceLogger.Error("[AutoPatchTest][UpdatePatchTestStatus]", err)
			}
		}
	}

	for _, pchId := range failedPatchIds {
		pch, err := NewPatchService().Repository.GetById(pchId, false)
		if err == nil {
			pch.PatchTestStatus = model.TEST_FAILED
			_, err = NewPatchService().Repository.Update(&pch)
			if err != nil {
				logger.ServiceLogger.Error("[AutoPatchTest][UpdatePatchTestStatus]", err)
			}
		}
	}
}
