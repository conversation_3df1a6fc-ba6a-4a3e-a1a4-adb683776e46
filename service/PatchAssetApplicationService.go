package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"

	"github.com/google/uuid"
)

type PatchAssetApplicationService struct {
	Repository *repository.PatchAssetApplicationRepository
}

func NewPatchAssetApplicationService() *PatchAssetApplicationService {
	return &PatchAssetApplicationService{
		Repository: repository.NewPatchAssetApplicationRepository(),
	}
}

func (service PatchAssetApplicationService) GetByAssetAndProductId(assetId, productId int64) (rest.PatchAssetApplicationRest, error) {
	application, err := service.Repository.GetByAssetAndProductId(assetId, productId)
	if err != nil {
		return rest.PatchAssetApplicationRest{}, err
	}
	return service.convertToRest(application), nil
}

func (service PatchAssetApplicationService) GetPatchAssetApplication(id int64) (rest.PatchAssetApplicationRest, error) {
	application, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.PatchAssetApplicationRest{}, err
	}
	return service.convertToRest(application), nil
}

func (service PatchAssetApplicationService) CreatePatchAssetApplication(applicationRest rest.PatchAssetApplicationRest) (int64, error) {
	application := service.convertToModel(applicationRest)
	id, err := service.Repository.Create(&application)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service PatchAssetApplicationService) UpdatePatchAssetApplication(id int64, applicationRest rest.PatchAssetApplicationRest) (bool, error) {
	application, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, err
	}

	_, updated := service.performPartialUpdate(&application, applicationRest)
	if !updated {
		return false, nil
	}

	_, err = service.Repository.Update(&application)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (service PatchAssetApplicationService) DeletePatchAssetApplication(id int64) (bool, error) {
	_, err := service.Repository.DeleteById(id)
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetAllPatchAssetApplications retrieves all PatchAssetApplications based on a search filter
func (service PatchAssetApplicationService) GetAllPatchAssetApplications(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_ASSET_APPLICATION.String(), true, "")
	var responsePage rest.ListResponseRest
	var applicationPageList []model.PatchAssetApplication
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_ASSET_APPLICATION.String(), false, "")
		applicationPageList, err = service.Repository.GetAllApplication(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(applicationPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service PatchAssetApplicationService) GetAllPatchAssetApps(filter rest.SearchFilter) ([]model.PatchAssetApplication, error) {
	var applications []model.PatchAssetApplication
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH_ASSET_APPLICATION.String(), false, "")
	applications, err = service.Repository.GetAllApplication(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return applications, err
	}
	return applications, nil
}

func (service PatchAssetApplicationService) convertToModel(applicationRest rest.PatchAssetApplicationRest) model.PatchAssetApplication {
	var os common.OsType
	if applicationRest.Name == "" {
		applicationRest.Name = uuid.New().String()
	}
	return model.PatchAssetApplication{
		BaseEntityModel: ConvertToBaseEntityModel(applicationRest.BaseEntityRest),
		AssetId:         applicationRest.AssetId,
		ProductId:       applicationRest.ProductId,
		Platform:        os.ToOsType(applicationRest.Platform),
	}
}

func (service PatchAssetApplicationService) convertToRest(application model.PatchAssetApplication) rest.PatchAssetApplicationRest {
	return rest.PatchAssetApplicationRest{
		BaseEntityRest: ConvertToBaseEntityRest(application.BaseEntityModel),
		Platform:       application.Platform.String(),
		AssetId:        application.AssetId,
		ProductId:      application.ProductId,
	}
}

func (service PatchAssetApplicationService) convertListToRest(applications []model.PatchAssetApplication) []rest.PatchAssetApplicationRest {
	var applicationRestList []rest.PatchAssetApplicationRest
	if len(applications) != 0 {
		for _, application := range applications {
			applicationRest := service.convertToRest(application)
			applicationRestList = append(applicationRestList, applicationRest)
		}
	}
	return applicationRestList
}

func (service PatchAssetApplicationService) performPartialUpdate(domainModel *model.PatchAssetApplication, restModel rest.PatchAssetApplicationRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	if _, ok := restModel.PatchMap["assetId"]; ok && domainModel.AssetId != restModel.AssetId {
		common.PrepareInDiffMap("asset_id", domainModel.AssetId, restModel.AssetId, &diffMap)
		domainModel.AssetId = restModel.AssetId
	}

	if _, ok := restModel.PatchMap["productId"]; ok && domainModel.ProductId != restModel.ProductId {
		common.PrepareInDiffMap("product_id", domainModel.ProductId, restModel.ProductId, &diffMap)
		domainModel.ProductId = restModel.ProductId
	}

	if _, ok := restModel.PatchMap["platform"]; ok && domainModel.Platform.String() != restModel.Platform {
		var os common.OsType
		common.PrepareInDiffMap("platform", domainModel.Platform, restModel.Platform, &diffMap)
		domainModel.Platform = os.ToOsType(restModel.Platform)
	}

	return diffMap, len(diffMap) != 0
}

func (service PatchAssetApplicationService) CreateOrDeleteApplicationAndAssetRelation(assetId int64, appName string, isCreated bool) {
	if isCreated {
		if appName != "" {
			osApplication, _ := NewPatchOsApplicationService().GetOrCreatePatchOsApplication(appName)
			patchAssetApp, err := service.GetByAssetAndProductId(assetId, osApplication.Id)
			if err != nil || patchAssetApp.Id == 0 {
				_, err = service.CreatePatchAssetApplication(rest.PatchAssetApplicationRest{
					BaseEntityRest: rest.BaseEntityRest{Name: appName},
					AssetId:        assetId,
					ProductId:      osApplication.Id,
					Platform:       common.Windows.String(),
				})
				if err != nil {
					logger.ServiceLogger.Error("[CreateOrDeleteApplicationAndAssetRelation]", err)
				}
			}
		}
	} else {
		osApplication, _ := NewPatchOsApplicationService().GetOrCreatePatchOsApplication(appName)
		patchAssetApp, err := service.GetByAssetAndProductId(assetId, osApplication.Id)
		if err == nil || patchAssetApp.Id != 0 {
			_, err = service.Repository.PermanentDeleteById(patchAssetApp.Id)
			if err != nil {
				logger.ServiceLogger.Error("[CreateOrDeleteApplicationAndAssetRelation]", err)
			}
		}
	}
}
