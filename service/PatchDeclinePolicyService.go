package service

import (
	"deployment/common"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"net/http"
	"reflect"
)

type PatchDeclinePolicyService struct {
	Repository *repository.PatchDeclinePolicyRepository
}

func NewPatchDeclinePolicyService() *PatchDeclinePolicyService {
	return &PatchDeclinePolicyService{
		Repository: repository.NewPatchDeclinePolicyRepository(),
	}
}

func (service *PatchDeclinePolicyService) convertToModel(restPolicy rest.PatchDeclinePolicyRest) *model.PatchDeclinePolicy {
	var platform common.OsType
	return &model.PatchDeclinePolicy{
		BaseEntityModel:         ConvertToBaseEntityModel(restPolicy.BaseEntityRest),
		ComputerGroupFilter:     ConvertToComputerGroupFilter(restPolicy.ComputerGroupFilterRest),
		Description:             restPolicy.Description,
		Platform:                platform.ToOsType(restPolicy.Platform),
		ApplicationPatchSetting: rest.ToApplicationPatchSettingList(restPolicy.ApplicationPatchSetting),
	}
}

func (service *PatchDeclinePolicyService) convertToRest(policy model.PatchDeclinePolicy) rest.PatchDeclinePolicyRest {
	return rest.PatchDeclinePolicyRest{
		BaseEntityRest:          ConvertToBaseEntityRest(policy.BaseEntityModel),
		ComputerGroupFilterRest: ConvertToComputerGroupFilterRest(policy.ComputerGroupFilter),
		Description:             policy.Description,
		Platform:                policy.Platform.String(),
		ApplicationPatchSetting: rest.ToApplicationPatchSettingRestList(policy.ApplicationPatchSetting),
	}
}

func (service *PatchDeclinePolicyService) Create(restPolicy rest.PatchDeclinePolicyRest) (int64, error) {
	restPolicy.CreatedTime = common.CurrentMillisecond()
	restPolicy.CreatedById = common.GetUserFromCallContext()
	policy := service.convertToModel(restPolicy)
	id, err := service.Repository.Create(policy)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (service *PatchDeclinePolicyService) Update(id int64, restPolicy rest.PatchDeclinePolicyRest) (bool, common.CustomError) {
	policy, err := service.Repository.GetById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Apply updates based on PatchMap
	if _, ok := restPolicy.PatchMap["description"]; ok && policy.Description != restPolicy.Description {
		policy.Description = restPolicy.Description
	}
	if _, ok := restPolicy.PatchMap["platform"]; ok && policy.Platform.String() != restPolicy.Platform {
		var platform common.OsType
		policy.Platform = platform.ToOsType(restPolicy.Platform)
	}
	if _, ok := restPolicy.PatchMap["applicationPatchSetting"]; ok && !reflect.DeepEqual(rest.ToApplicationPatchSettingRestList(policy.ApplicationPatchSetting), restPolicy.ApplicationPatchSetting) {
		policy.ApplicationPatchSetting = rest.ToApplicationPatchSettingList(restPolicy.ApplicationPatchSetting)
	}

	_, err = service.Repository.Update(&policy)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}
	return true, common.CustomError{}
}

func (service *PatchDeclinePolicyService) GetById(id int64) (rest.PatchDeclinePolicyRest, error) {
	policy, err := service.Repository.GetById(id)
	if err != nil {
		return rest.PatchDeclinePolicyRest{}, err
	}
	return service.convertToRest(policy), nil
}

func (service *PatchDeclinePolicyService) DeleteById(id int64) (bool, error) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, err
	}
	return success, nil
}

func (service *PatchDeclinePolicyService) GetAllPolicies(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var policyList []model.PatchDeclinePolicy
	var err error
	tableName := common.PATCH_DECLINE_POLICY.String()
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, "")
	count, err := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if err != nil {
		return responsePage, err
	}
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
		policyList, err = service.Repository.GetAll(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	}
	responsePage.ObjectList = service.convertListToRest(policyList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service *PatchDeclinePolicyService) convertListToRest(policies []model.PatchDeclinePolicy) []rest.PatchDeclinePolicyRest {
	var policyRestList []rest.PatchDeclinePolicyRest
	for _, policy := range policies {
		policyRestList = append(policyRestList, service.convertToRest(policy))
	}
	return policyRestList
}
