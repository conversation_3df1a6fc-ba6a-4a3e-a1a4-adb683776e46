{
  "patchData": {
    "defender_info": {
      "0bc0eab08d712fbb0a1a2ffb5f9dc0db": {
        "b6b44c195c792c1c193e757b12d34b61": "[1, 0, 0, 0, 62, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0]",
        "default": "HK<PERSON>\SOFTWARE\Microsoft\Windows Defender\Reporting"
      },
      "2ae926047ba615f1e80b4b866055f2f4": {
        "19448dbb8f366c0c75afb2e4aabd9192": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Real-Time Protection"
      },
      "300cf8291bede2317edaeae1df07db03": {
        "3714b0e32c39910c9f396179802e93f9": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 207, 3, 175, 97, 14, 197, 8, 95, 173, 80, 112, 127, 138, 109, 131, 150]",
        "50a17387eeff46053036c6b30b643abb": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 119, 158, 157, 95, 5, 190, 25, 156, 76, 83, 241, 232, 140, 123, 206, 47]",
        "8fa66284dfb96b5e5ac319dcff4f5d62": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 213, 218, 166, 154, 5, 41, 23, 177, 145, 36, 52, 14, 168, 117, 240, 242]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\CoreService\CpuSensor"
      },
      "37e149a2d699dbdb5d88842f102031bb": {
        "02948b89d1c2d5bb09945f01b808b64e": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 222, 92, 9, 162, 102, 235, 60, 162, 168, 178, 188, 120, 192, 198, 70, 83]",
        "3659b50b8e4d3daa0a2a278a6e1c3e14": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "503a4dd7f52e04594cc63eb11e738f5e": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 25, 0, 0, 0]",
        "6ef443ba2e2dd59e65d4482fb4f3e20d": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "73500c9ee232c1089ae2f99fdf9106b1": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "9fc2535cac93b54373a9de0b0ebfb651": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 226, 147, 4, 0]",
        "a2f9c682f4eb9baa49694505e760c0e2": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "a88efcb57281bda46c7b4ef0f59ea15a": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 60, 0, 0, 0]",
        "c4f18d7dc7670ccfe44e72c94d4822ed": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 222, 3, 251, 223, 91, 167, 183, 207, 200, 231, 42, 68, 103, 253, 213, 95]",
        "c9a998d55d5d04f606904c36b895c2ac": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 224, 147, 4, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\CoreService",
        "e6da8ac6c0ea655b1593aacacaf13478": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 136, 25, 196, 127]"
      },
      "3bb76a1e1bb6aa21c89b71d2c3b4759d": {
        "8db492fd94e777dc419188744b26b5b3": "[1, 0, 0, 0, 72, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 77, 0, 112, 0, 83, 0, 105, 0, 103, 0, 83, 0, 116, 0, 117, 0, 98, 0, 46, 0, 101, 0, 120, 0, 101, 0, 0, 0, 0, 0, 0, 0]",
        "ddf0b44fa528e58e6c7e0ab0343927cc": "[1, 0, 0, 0, 72, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 77, 0, 112, 0, 83, 0, 105, 0, 103, 0, 83, 0, 116, 0, 117, 0, 98, 0, 46, 0, 101, 0, 120, 0, 101, 0, 0, 0, 0, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Miscellaneous Configuration"
      },
      "43a5c7a9577d9fb23758aeb591160fb9": {
        "2018d477907eef9ff75422b239f1c506": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "b4ca58ec725fd711bab4fed06fae2ea8": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 5, 0, 242, 93, 1, 0, 1, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Diagnostics",
        "e280834d83fa795772ba8f5323c9fa03": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 45, 119, 128, 105, 79, 203, 30, 44, 245, 83, 230, 123, 168, 233, 222, 210]"
      },
      "708be932231a0f8425e742d1a59fba8a": {
        "4432db889d5aacbc80b199c36f42da40": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0]",
        "5aa7ff9b2424376a6107312123e582e7": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 255, 1, 0, 0, 0, 0, 0, 0]",
        "5b5530a6c986c5414807965ce786501f": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 5, 0, 0, 0]",
        "951b1b76c98877ba2fd1c9647c145aba": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "c83b23ddfea0a42c3ca83f07b57daf13": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Features",
        "ebc375b7c1a9d9b8c09ef224e16edb5f": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 73, 80, 170, 206, 224, 8, 60, 215, 102, 192, 98, 139, 139, 51, 106, 39]"
      },
      "7c8aac06d6fa69a5bbc83785c602379a": {
        "b8dd2fae0fec0ef3324fb4bcf2c29405": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "c298d8968276cef56372e230923828a1": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 41, 78, 212, 110, 83, 64, 181, 213, 239, 212, 39, 211, 56, 236, 173, 71]",
        "c7f0e95852f564d7fd4278a74c94c2fb": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\."
      },
      "7da6a4cae7a6c8687894bf0b2f75656e": {
        "33d2e95d03041617acb153047f6207eb": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 30, 0, 0, 0]",
        "c821f92d1d3558901dbaa7179e7088ef": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 169, 87, 135, 131, 127, 194, 218, 1]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Scan",
        "dfa50e33cc89d8ef92a012bfed4c62ea": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 23, 0, 0, 0]"
      },
      "86d14d81dfff9994cd7f94f0da89e132": {
        "3714b0e32c39910c9f396179802e93f9": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 5, 20, 139, 9, 146, 16, 115, 179, 5, 91, 91, 42, 231, 231, 52, 254]",
        "50a17387eeff46053036c6b30b643abb": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 125, 205, 245, 1, 127, 156, 122, 224, 244, 68, 247, 75, 200, 36, 32, 73]",
        "8fa66284dfb96b5e5ac319dcff4f5d62": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 213, 218, 166, 154, 5, 41, 23, 177, 145, 36, 52, 14, 168, 117, 240, 242]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\CoreService\MemorySensor"
      },
      "8fd89e6b99c949a45b0929102c82ccb9": {
        "2705dca4dd5e425da9dd6d1d8059eea0": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "2f3e69513e4b70888fdc2ed8d3a386be": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "cec46c459e4f2cbfbf8e10b680132c1c": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Features\SCC",
        "e960cd9fa59cc47ec66bdc025240ae9f": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]"
      },
      "Controls": {
        "10": "1",
        "13": "1",
        "15": "1",
        "21": "1",
        "22": "62",
        "31": "2405",
        "32": "17000",
        "48": "1",
        "50": "0",
        "54": "1",
        "69": "1",
        "7": "1",
        "89": "1",
        "9": "1",
        "_13": "1",
        "_4": "1",
        "_7": "1",
        "_9": "1"
      },
      "CoreService": {
        "disablecoreservice1dstelemetry": "0",
        "disablecoreserviceecsintegration": "0",
        "mdalertmininterval": "60",
        "mdalertmonitorwindow": "25",
        "mddisablerescontroller": "0",
        "mdecssettingstimestamp": "[171, 219, 238, 143, 132, 196, 218, 1]",
        "mdenabledailysensorchecks": "0",
        "mdlastheartbeat": "[83, 20, 208, 144, 135, 196, 218, 1]",
        "mdtrustedrootcertthumbprints": "CB3CCBB76031E5E0138F8DD39A23F9DE47FFC35E43C1144CEA27D46A5AB1CB5F|4348A0E9444C78CB265E058D5E8944B4D84F9662BD26DB257F8934A443C70161",
        "mdtrustedsubjectorgs": "Microsoft Corporation|DigiCert Inc",
        "wdconfighash": "2080512403",
        "wdtimerinitaldelay": "300002",
        "wdtimermonitorinterval": "300000"
      },
      "CpuSensor": {
        "highthresholds": "45|90|90",
        "lowthresholds": "10|10|10",
        "monitoredtargets": "mpdefendercoreservice|msmpeng|nissrv"
      },
      "CrashSensor": {
        "monitoredtargets": "mpdefendercoreservice|msmpeng|nissrv"
      },
      "Diagnostics": {
        "cleanupcomponentprogress": "CleanupCompleted",
        "cloudbadlistversion": "[5, 0, 0, 0, 0, 0, 0, 0]",
        "initializingcomponentprogress": "ServiceStartedSuccessfully",
        "lastknowngoodenginecandidate": "[5, 0, 242, 93, 1, 0, 1, 0]",
        "lastsignatureupdateresult": "0",
        "latestengineversionondevice": "[5, 0, 242, 93, 1, 0, 1, 0]",
        "latestplatformversionondevice": "[7, 0, 242, 93, 18, 0, 4, 0]"
      },
      "DiskSensor": {
        "highthresholds": "",
        "lowthresholds": "",
        "monitoredtargets": "",
        "thresholds": ""
      },
      "ETag": {
        "default": ""
        Ggvsnyh13
        +
        kOtg4ob3Rh4TtsIXIkYsJUtIAFGm0DQV0=
        ""
      },
      "EcsConfigs": {
        "mpdisableresourcemonitoring": "0"
      },
      "Features": {
        "enablecacs": "0",
        "mpcapability": "[255, 1, 0, 0, 0, 0, 0, 0]",
        "mpplatformkillbitsexfromengine": "[32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]",
        "mpplatformkillbitsfromengine": "[0, 0, 0, 4, 0, 0, 0, 0]",
        "tamperprotection": "1",
        "tamperprotectionsource": "5",
        "tpexclusions": "0"
      },
      "MemorySensor": {
        "highthresholds": "30|1024|500",
        "lowthresholds": "20|600|128",
        "monitoredtargets": "mpdefendercoreservice|msmpeng|nissrv"
      },
      "Miscellaneous Configuration": {
        "bddupdatefailure": "0",
        "deltaupdatefailure": "0"
      },
      "Network Protection": {
        "enablenetworkprotection": "0"
      },
      "Platform": {
        "4.18.2303.123": "[123, 0, 255, 8, 18, 0, 4, 0]",
        "4.18.23060.1004": "[236, 3, 20, 90, 18, 0, 4, 0]"
      },
      "Quarantine": {
        "purgeitemsafterdelay": "90"
      },
      "Real-Time Protection": {
        "dpadisabled": "0"
      },
      "Reporting": {
        "lastheartbeatreporttime": "[28, 248, 91, 204, 37, 155, 220, 1]",
        "lastreboottime": "[30, 28, 38, 20, 70, 153, 218, 1]",
        "lastrecaptime": "[161, 113, 100, 211, 37, 155, 220, 1]",
        "lastrtpandscanconfigscollectedinheartbeattime": "[99, 36, 112, 207, 37, 155, 220, 1]",
        "scanssincelastrecap": "6",
        "sigupdatetimestampssincelasthb": ""
      },
      "SCC": {
        "sccaltsource": "0",
        "sccaltstate": "0",
        "sccsource": "0",
        "sccstate": "0"
      },
      "Scan": {
        "aa3551ee-eaf7-4f15-8d9e-8787871d8f00": "C:\ProgramData\Microsoft\Windows Defender\Scans\History\CacheManager\AA3551EE-EAF7-4F15-8D9E-8787871D8F00-1.bin",
        "aggressivecatchupquickscanreattemptelapsed": "23",
        "cachefile": "C:\ProgramData\Microsoft\Windows Defender\Scans\History\CacheManager\AA3551EE-EAF7-4F15-8D9E-8787871D8F00-1.bin",
        "daysuntilaggressivecatchupquickscan": "30",
        "lastaggressivecheck": "[128, 207, 213, 49, 133, 196, 218, 1]",
        "lastquickscanid": "{23744687-95E2-4D7A-85DB-358A3F1E0BBB}",
        "lastquickscanresourcecount": "[116, 204, 5, 0, 0, 0, 0, 0]",
        "lastscanrun": "[234, 187, 127, 79, 129, 194, 218, 1]",
        "lastscantype": "1",
        "schedulequickscantime": "0",
        "sfcstate": "7"
      },
      "Signature Updates": {
        "assignatureapplied": "[0, 103, 62, 16, 74, 196, 218, 1]",
        "assignaturebaseversion": "1.413.0.0",
        "assignatureversion": "1.413.445.0",
        "avsignatureapplied": "[0, 58, 13, 15, 74, 196, 218, 1]",
        "avsignaturebaseversion": "1.413.0.0",
        "avsignatureversion": "1.413.445.0",
        "disabledefaultsigs": "0",
        "enableupdateresiliency": "0",
        "engineversion": "1.1.24050.5",
        "lastfallbacktime": "[247, 129, 148, 249, 134, 196, 218, 1]",
        "mocampupdatestarted": "[3, 23, 90, 142, 127, 194, 218, 1]",
        "signaturecategoryid": "8c3fcc84-7410-4a95-8b89-a166a0190486",
        "signaturelocation": "C:\ProgramData\Microsoft\Windows Defender\Definition Updates\{469988BE-8672-4618-AA3F-099CBA4F7039}",
        "signatureslastupdated": "[203, 218, 208, 3, 133, 196, 218, 1]",
        "signaturetype": "0",
        "signatureupdatecount": "22",
        "signatureupdatelastattempted": "[141, 220, 132, 249, 134, 196, 218, 1]",
        "signatureupdatepending": "0"
      },
      "Spynet": {
        "lastmapsfailuretime": "[130, 240, 103, 69, 124, 194, 218, 1]",
        "lastmapsfailuretimestring": "2024-06-19T19:09:55Z",
        "lastmapssuccesstime": "[204, 143, 202, 66, 133, 196, 218, 1]",
        "mapsconcurrency": "1",
        "mapsconcurrencydss": "10",
        "spynetreporting": "2",
        "spynetreportinglocation": "SOAP:https://wdcp.microsoft.com/WdCpSrvc.asmx\nSOAP:https://wdcpalt.microsoft.com/WdCpSrvc.asmx\nREST:https://wdcp.microsoft.com/wdcp.svc/submitReport\nREST:https://wdcpalt.microsoft.com/wdcp.svc/submitReport\nBOND:https://wdcp.microsoft.com/wdcp.svc/bond/submitreport\nBOND:https://wdcpalt.microsoft.com/wdcp.svc/bond/submitreport",
        "ssloptions": "3",
        "submitsamplesconsent": "1"
      },
      "UX Configuration": {
        "uilockdown": "0"
      },
      "UpdateControl": {
        "lastheartbeatsystime": "[11, 5, 192, 252, 242, 175, 218, 1]"
      },
      "Windows Defender": {
        "backuplocation": "C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.24040.4-0",
        "disableantispyware": "0",
        "disableantivirus": "0",
        "hybridmodeenabled": "0",
        "installlocation": "C:\ProgramData\Microsoft\Windows Defender\Platform\4.18.24050.7-0",
        "installtime": "[236, 108, 75, 188, 69, 153, 218, 1]",
        "isservicerunning": "1",
        "manageddefenderproducttype": "0",
        "oobeinstalltime": "[230, 217, 200, 180, 70, 153, 218, 1]",
        "productappdatapath": "C:\ProgramData\Microsoft\Windows Defender",
        "producticon": "@%ProgramFiles%\Windows Defender\EppManifest.dll,-100",
        "productlocalizedname": "@%ProgramFiles%\Windows Defender\EppManifest.dll,-1000",
        "productstatus": "0",
        "producttype": "2",
        "remediationexe": "windowsdefender://",
        "uupflags": "0",
        "verifiedandreputabletrustmodeenabled": "0"
      },
      "a3d48adcae854ec91138e049ab8f1667": {
        "04c0f06db4b397b61dd8a3e8d1cbe9c5": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "0ea57cc6e0902846f55c154a1eadb16b": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "1e6f0b1f64f9991b39d26c423314a160": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "24f6225c3779b7a5ef942048e74ef96e": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "67ae81acd95388b4876c74c7725f1a3b": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "71f6794efc68c56fbee873fa5fb5503a": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "819fc00a81e0cb69b94db914cdac97b9": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 104, 66, 0, 0]",
        "8c7be82f091dfc5d82b9cf5f1d6ba3a0": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "92e4d2da3d1528bc9f6668bbc26d633e": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "a734c94d6e046c4667fea57758c5b6f6": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "ad6cb1c028fd26ebe489506b11cb2798": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "b43e28d1b129619d9a1e8186df0d2e18": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "c5940e567348894860e9e45434682f5f": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 62, 0, 0, 0]",
        "c7f64044bdea24b81008d4b3a1c09274": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 101, 9, 0, 0]",
        "cea784396811723f7a4c0a1cd85f5930": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "d16c79acbe2b6a29285f345c0ea21b9a": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "d2c607c69ece7f39a2180a380b6e0e11": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Features\Controls",
        "f002cc454bc1424951daad577259abfd": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]"
      },
      "b01784f708f6053209ad4d15d6c83069": {
        "077aff94cd68ac9005e0acd0e798e8cf": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 61, 224, 160, 131, 127, 194, 218, 1]",
        "154cd9ed8dbf3abe19afee82acf42764": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 247, 21, 219, 70, 194, 218, 1]",
        "214564454a345e80a2a0d243f4d8d83d": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 96, 62, 58, 132, 35, 71, 91, 201, 205, 11, 117, 218, 82, 7, 119, 73]",
        "26560fa15cbedd1dc6d79280c53311fa": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 79, 37, 79, 143, 211, 8, 135, 120, 153, 139, 188, 139, 100, 94, 97, 62]",
        "34d376f2f99a9376e8cdf5e0fd925bd5": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 128, 141, 174, 219, 70, 194, 218, 1]",
        "3b8769df560e8a0188939607b535af16": "[1, 0, 0, 0, 74, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 77, 0, 112, 0, 67, 0, 109, 0, 100, 0, 82, 0, 117, 0, 110, 0, 46, 0, 101, 0, 120, 0, 101, 0, 0, 0, 7, 201, 175, 131, 127, 194, 218, 1]",
        "3f2d0b0e74592ed7271369d717d84bc3": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 102, 125, 66, 168, 30, 168, 218, 1]",
        "56ba5db6eb6bd71d6d4113376e409e49": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 196, 197, 178, 115, 154, 60, 222, 209, 176, 218, 180, 129, 236, 45, 215, 81]",
        "63ec360384d8974f18d38f03a55e0235": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 196, 197, 178, 115, 154, 60, 222, 209, 176, 218, 180, 129, 236, 45, 215, 81]",
        "65f49e2055944fd5d882b860d49bb066": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "a0b5b24b973e8541e47b99cdfeca1041": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 79, 37, 79, 143, 211, 8, 135, 120, 153, 139, 188, 139, 100, 94, 97, 62]",
        "a19da1d6fc94f54f77dfece65dd4201e": "[1, 0, 0, 0, 70, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 77, 0, 112, 0, 67, 0, 109, 0, 100, 0, 82, 0, 117, 0, 110, 0, 46, 0, 101, 0, 120, 0, 101, 0, 0, 0, 0, 0, 0, 0]",
        "b239264027f95968a6b47b47560f8b18": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0, 0, 0]",
        "b875c169455d17b8a5450463f8b08670": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 13, 0, 0, 0]",
        "d2ce8ce1a0455eaf4f1740961c48675f": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 79, 153, 240, 185, 50, 228, 177, 28, 111, 209, 34, 37, 189, 186, 254, 162]",
        "d314cf694cd0ba04b6a919a6efbb5ace": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 3, 23, 90, 142, 127, 194, 218, 1]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\Signature Updates"
      },
      "d22fd38d8f71a17a15b4923e07898fe8": {
        "8fa66284dfb96b5e5ac319dcff4f5d62": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 213, 218, 166, 154, 5, 41, 23, 177, 145, 36, 52, 14, 168, 117, 240, 242]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\CoreService\CrashSensor"
      },
      "dc7a2a1a6eb373b337c916375e2efa8d": {
        "3714b0e32c39910c9f396179802e93f9": "[1, 0, 0, 0, 62, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0]",
        "50a17387eeff46053036c6b30b643abb": "[1, 0, 0, 0, 62, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0]",
        "8fa66284dfb96b5e5ac319dcff4f5d62": "[1, 0, 0, 0, 62, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\CoreService\DiskSensor"
      },
      "dcc01295ef0bcc029b7ad4a1c226fb02": {
        "1190cee5c4be25923f83e09ede091de8": "[1, 0, 0, 0, 76, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 161, 68, 131, 67, 79, 201, 253, 85, 33, 114, 169, 68, 225, 189, 191, 234]",
        "39758309e682752a436dd47440392899": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 3, 0, 0, 0]",
        "437a6942ab87c0bfa3471c53a81dd601": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 10, 0, 0, 0]",
        "ae14dd1f5302faaaba819af97977de15": "[1, 0, 0, 0, 64, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 1, 0, 0, 0]",
        "default": "HKLM\SOFTWARE\Microsoft\Windows Defender\SpyNet",
        "e9c9945556abbed97eb6c7a543383803": "[1, 0, 0, 0, 68, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 124, 0, 77, 0, 112, 0, 67, 0, 108, 0, 105, 0, 101, 0, 110, 0, 116, 0, 0, 0, 254, 110, 33, 161, 126, 194, 218, 1]"
      }
    },
    "defender_policy_info": {},
    "edge_info_32": {},
    "edge_info_32_hkcu": {
      "on-logon-autolaunch": {
        "enabled": "0"
      }
    },
    "edge_info_64": {
      "edge-vbs-disable": {
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        125.0.2535.67
        \
        Installer
        \
        setup.exe
        " --edge-vbs-disable --system-level --verbose-logging --msedge --channel=stable"
      },
      "edge-vbs-enable": {
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        125.0.2535.67
        \
        Installer
        \
        setup.exe
        " --edge-vbs-enable --system-level --verbose-logging --msedge --channel=stable"
      },
      "on-interval-telemetry-collection": {
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        msedge.exe
        " --no-startup-window --edge-reporting-task",
        "runoninterval": "1"
      },
      "on-logon-autolaunch": {
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        msedge.exe
        " --launcher=on_logon_windows"
      },
      "on-logon-startup-boost": {
        "autorunonlogon": "1",
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        msedge.exe
        " --no-startup-window"
      },
      "on-os-upgrade": {
        "autorunonosupgrade": "1",
        "commandline": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        Edge
        \
        Application
        \
        125.0.2535.67
        \
        Installer
        \
        setup.exe
        " --on-os-upgrade --msedge --channel=stable --system-level --verbose-logging"
      },
      "{1FAB8CFE-9860-415C-A6CA-AA7D12021940}": {
        "name": "Microsoft Bing Service Update",
        "pv": "2.0.0.34\u0000NDOWS"
      },
      "{56EB18F8-B008-4CBD-B6D2-8C97FE7E9062}": {
        "channel": "stable",
        "location": "C:\Program Files (x86)\Microsoft\Edge\Application",
        "name": "Microsoft Edge",
        "pv": "125.0.2535.67"
      },
      "{********-FE2A-4295-8BDF-00C3A9A7E4C5}": {
        "location": "C:\Program Files (x86)\Microsoft\EdgeWebView\Application",
        "name": "Microsoft Edge WebView2 Runtime",
        "pv": "125.0.2535.67",
        "silentuninstall": ""
        C:
        \
        Program
        Files
        (x86)
        \
        Microsoft
        \
        EdgeWebView
        \
        Application
        \
        125.0.2535.67
        \
        Installer
        \
        setup.exe
        " --force-uninstall --uninstall --msedgewebview --system-level --verbose-logging"
      },
      "{F3C4FE00-EFD5-403B-9569-398A20F1BA4A}": {
        "name": "Microsoft Edge Update",
        "pv": "1.3.187.41"
      }
    },
    "edge_info_64_hkcu": {},
    "mpcomputerstatus_info": "\r\n\r\nAMEngineVersion                  : 1.1.24050.5\r\nAMProductVersion                 : 4.18.24050.7\r\nAMRunningMode                    : Normal\r\nAMServiceEnabled                 : True\r\nAMServiceVersion                 : 4.18.24050.7\r\nAntispywareEnabled               : True\r\nAntispywareSignatureAge          : 0\r\nAntispywareSignatureLastUpdated  : 6/21/2024 7:15:34 PM\r\nAntispywareSignatureVersion      : 1.413.445.0\r\nAntivirusEnabled                 : True\r\nAntivirusSignatureAge            : 0\r\nAntivirusSignatureLastUpdated    : 6/21/2024 7:15:32 PM\r\nAntivirusSignatureVersion        : 1.413.445.0\r\nBehaviorMonitorEnabled           : True\r\nComputerID                       : 25F71F87-97ED-4930-9BBB-405C96301424\r\nComputerState                    : 0\r\nDefenderSignaturesOutOfDate      : False\r\nDeviceControlDefaultEnforcement  : \r\nDeviceControlPoliciesLastUpdated : 12/31/1600 4:00:00 PM\r\nDeviceControlState               : Disabled\r\nFullScanAge                      : 4294967295\r\nFullScanEndTime                  : \r\nFullScanOverdue                  : False\r\nFullScanRequired                 : False\r\nFullScanSignatureVersion         : \r\nFullScanStartTime                : \r\nInitializationProgress           : ServiceStartedSuccessfully\r\nIoavProtectionEnabled            : True\r\nIsTamperProtected                : True\r\nIsVirtualMachine                 : False\r\nLastFullScanSource               : 0\r\nLastQuickScanSource              : 2\r\nNISEnabled                       : True\r\nNISEngineVersion                 : 1.1.24050.5\r\nNISSignatureAge                  : 0\r\nNISSignatureLastUpdated          : 6/21/2024 7:15:32 PM\r\nNISSignatureVersion              : 1.413.445.0\r\nOnAccessProtectionEnabled        : True\r\nProductStatus                    : 524288\r\nQuickScanAge                     : 2\r\nQuickScanEndTime                 : 6/19/2024 12:49:00 PM\r\nQuickScanOverdue                 : False\r\nQuickScanSignatureVersion        : 1.413.396.0\r\nQuickScanStartTime               : 6/19/2024 12:46:00 PM\r\nRealTimeProtectionEnabled        : True\r\nRealTimeScanDirection            : 0\r\nRebootRequired                   : False\r\nSmartAppControlExpiration        : \r\nSmartAppControlState             : Off\r\nTamperProtectionSource           : Signatures\r\nTDTCapable                       : Supported\r\nTDTMode                          : cm\r\nTDTSiloType                      : E\r\nTDTStatus                        : Enabled\r\nTDTTelemetry                     : Disabled\r\nTroubleShootingDailyMaxQuota     : \r\nTroubleShootingDailyQuotaLeft    : \r\nTroubleShootingEndTime           : \r\nTroubleShootingExpirationLeft    : \r\nTroubleShootingMode              : \r\nTroubleShootingModeSource        : \r\nTroubleShootingQuotaResetTime    : \r\nTroubleShootingStartTime         : \r\nPSComputerName                   : \r\n\r\n\r\n\r\n",
    "mrtversion_info": "\r\r\n\r\r\n\r\r\n",
    "reg_patches": {
      "0280F74C424635EAB6FAD2148E928C55": [],
      "05A13396A8095470FCFC483163C0B569": [],
      "083F7097DFC3C742D9CF2AC3DE02218F": [],
      "098DD738EC413BE98E1E9FA6E75EFC5E": [],
      "0A23B3DA17F598ED5BA5E9586914688D": [],
      "0A7543C0ECD333A4EB0FB925C8557717": [],
      "0AD86C7C1058DD61E1A6C643AA2AF812": [],
      "11BBE2D2D48437F8AF52F3AFCB3E170F": [],
      "120D6B1379CB5C28C92461BA683C2751": [],
      "1E430FBFE365D9F1CD5494B18BE8B9E9": [],
      "1E59D7E6A2ADDED48A6CF3AB7141BD26": [],
      "21696FF119A55653E73E50930AE4B3D8": [],
      "23C5175E0B43E8F6188B31BF911B6B28": [],
      "244E4D185C6FB4FDED8F675EA1CC658F": [],
      "2AD3FAC76F97BD0CC66542268C4C19C4": [],
      "2CB55C1DA4D642345AC5AC948A283470": [],
      "2E27D73E60E6E6F4695C598C0F5BE1E6": [],
      "3022FAEFCA2AC080D955F99026F3833C": [],
      "31CC30034CB91D84596D412BFA0E86B3": [],
      "3421B1F621C789355F5715200956966A": [],
      "35DB928A5EB42F324C8A20C65B4DE7FC": [],
      "3E7F21B0AADE29FA02BB8845F0FE45AB": [],
      "3FFE93274C36B47478B4A537462E34EE": [],
      "41700165BC9412B42960D41DFADD5DF6": [],
      "486E5E45E2313D32BC6F6912222E5C86": [],
      "4D91B99A78F75E304B8A0824E0DCF735": [],
      "52B507412CA5CA28BD973A259110A6BB": [],
      "548EAA09EC2421CE0414A8FDB77E567E": [],
      "5586A2FD18BD74090333D8A66C50A5FE": [],
      "568BA3E883E9F5E1B7948C3E7AC03630": [],
      "57B285AFC9D77172E546A1C85C4EA6DB": [],
      "57C15CFC80F88691CB68073A693D359D": [],
      "580E122673EF86016E7DD980BA3DA22E": [],
      "5A8C703D5337E650E6462E584476E4DF": [],
      "5ED4D1F53FFBB3050DF504DE9672ED71": [],
      "6372D2F0634F01F3BF03D972F98503B5": [],
      "66EE2A9B9B1E58DE7EB540A143E84BD6": [],
      "6A29E3282757D4716017595A75589F19": [],
      "6BF4952050999BBC018EFEFC7B21D2C7": [],
      "706754A126269493364D1E8FA5D359DF": [],
      "75711F8653C86BEBA2DEF6C7C65C8B0C": [],
      "75ECCD667F31CA341BE684AEC59808D6": [],
      "7F42B45DED34FB7CA3C5389F8E0E07D0": [],
      "87CD1CE856A0CA6B66D4FD3BCAE87963": [],
      "884CB26DA4C471FB0AEC124BFBF8B903": [],
      "899C6AE5CA5D9DE4983CF9521BC7DCD3": [],
      "8AD737F37B5C0478B670AB375B6EC2FD": [],
      "90BE3A13622E5E0BAF07FFB4C3DAE2FC": [],
      "918687DB08AA81B721D5AA30AA2F57B9": [],
      "982F9F28880693F891814A3551EF9FA9": [],
      "9A894A59E6E5977551327826424FF149": [],
      "A072C632ED9EEBD409E1F84AFFB66C3D": [],
      "A22686DFF43694BFE6492EA154A1B3A5": [],
      "A4BB3B8BD01A15F4197B6AF4AF3CE17A": [],
      "A634AD1A4AF22CDFB9A0BF4BC00F94D9": [],
      "AA5C5D89C39917317CFAF8A8E0C8FA68": [],
      "ACE833AEF36C6DB46BF67244337CA594": [],
      "AF7D9F39DF2FD7385EF37D797670E144": [],
      "B10C46B589726F2A983CCA3009F67888": [],
      "B162A4F1C88534A41B0F9463A54C037C": [],
      "B1B93CA0CFA448642BC22685841EC629": [],
      "B501EDE8A588371D058F5FF0935CC19F": [],
      "BA65C3B4E36984F4797450926738BDB3": [],
      "C75F9434C9C0B61CA9047E4B604C152E": [],
      "C883F8A37F0D66A0E0ED4D771B1B2B4F": [],
      "D6118034367AD441CD182634EBB6A2FD": [],
      "D71D38FC07BF1A12634E73EEE71C5B78": [],
      "D753EBD72AA286B762E78FADBE81D2F6": [],
      "D7C7798C586F2828C38726670EE3B323": [],
      "D7D7E5F09698B1CD02D520F45BA44071": [],
      "D7F323E738939D14D9F79F165DE37B8B": [],
      "D8C13928CC38E2FC4FAFA90F28B9AC22": [],
      "E064158136E071145BAB522A0F5408B1": [],
      "E0A2900305D166D85EBE106FAAC8F1EF": [],
      "E2062FCA13DB5EB4CA30C9F8BD36A8AD": [],
      "EF21260A02C37C138FA86476E37FE238": [],
      "F104BC0BFE1FC36E1819A944F72CC485": [],
      "F388ABC26A15D7D3BD9B50723D4933BC": [],
      "F7C14951D018FD14C8A5D8400972A7BF": [],
      "F84DEC95EFBEC084A883CF70C9B2CEF0": [],
      "F88235646BBEBAA4B96D3E74140F5B0D": [],
      "FAE09B0A1CF82B947A6DF7B9F1FEDD34": [],
      "FDF29A682A50E6B4EA8EFE6BE8F76F5B": []
    },
    "sysInfo": "\r\nHost Name:                 DESKTOP-4I5D1EM\r\nOS Name:                   Microsoft Windows 11 Pro\r\nOS Version:                10.0.22000 N/A Build 22000\r\nOS Manufacturer:           Microsoft Corporation\r\nOS Configuration:          Standalone Workstation\r\nOS Build Type:             Multiprocessor Free\r\nRegistered Owner:          sagar\r\nRegistered Organization:   N/A\r\nProduct ID:                00330-80000-00000-AA151\r\nOriginal Install Date:     4/28/2024, 1:29:29 AM\r\nSystem Boot Time:          6/22/2024, 2:20:15 AM\r\nSystem Manufacturer:       LENOVO\r\nSystem Model:              81A8\r\nSystem Type:               x64-based PC\r\nProcessor(s):              1 Processor(s) Installed.\r\n                           [01]: Intel64 Family 6 Model 142 Stepping 9 GenuineIntel ~2703 Mhz\r\nBIOS Version:              LENOVO 5SCN38WW, 2/23/2018\r\nWindows Directory:         C:\WINDOWS\r\nSystem Directory:          C:\WINDOWS\system32\r\nBoot Device:               \Device\HarddiskVolume1\r\nSystem Locale:             en-us;English (United States)\r\nInput Locale:              en-us;English (United States)\r\nTime Zone:                 (UTC-08:00) Pacific Time (US \u0026 Canada)\r\nTotal Physical Memory:     8,035 MB\r\nAvailable Physical Memory: 2,576 MB\r\nVirtual Memory: Max Size:  10,083 MB\r\nVirtual Memory: Available: 3,786 MB\r\nVirtual Memory: In Use:    6,297 MB\r\nPage File Location(s):     C:\pagefile.sys\r\nDomain:                    WORKGROUP\r\nLogon Server:              \DESKTOP-4I5D1EM\r\nHotfix(s):                 6 Hotfix(s) Installed.\r\n                           [01]: KB5030650\r\n                           [02]: KB5030842\r\n                           [03]: KB5004567\r\n                           [04]: KB5008295\r\n                           [05]: KB5011048\r\n                           [06]: KB5031591\r\nNetwork Card(s):           2 NIC(s) Installed.\r\n                           [01]: Qualcomm Atheros QCA61x4A Wireless Network Adapter\r\n                                 Connection Name: Wi-Fi\r\n                                 DHCP Enabled:    Yes\r\n                                 DHCP Server:     ***********\r\n                                 IP address(es)\r\n                                 [01]: ***********7\r\n                                 [02]: fe80::4d9d:49f6:93c4:5baf\r\n                                 [03]: 2401:4900:1f3f:d58f:9420:562:3c94:2651\r\n                                 [04]: 2401:4900:1f3f:d58f:334f:b4e4:5e52:17a4\r\n                           [02]: Bluetooth Device (Personal Area Network)\r\n                                 Connection Name: Bluetooth Network Connection\r\n                                 Status:          Media disconnected\r\nHyper-V Requirements:      VM Monitor Mode Extensions: Yes\r\n                           Virtualization Enabled In Firmware: Yes\r\n                           Second Level Address Translation: Yes\r\n                           Data Execution Prevention Available: Yes\r\n"
  },
  "installedPatchList": null,
  "missingPatchList": null
}
