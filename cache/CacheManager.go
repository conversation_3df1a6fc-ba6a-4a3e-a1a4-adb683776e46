package cache

import "sync/atomic"

var validLicense atomic.Bool // thread-safe global boolean

var AssetCache = Cache[string, map[string]interface{}]{}

var DepartmentAssetCache = Cache[string, []string]{}

var UserCache = Cache[int64, interface{}]{}

var UserPermissionCache = Cache[int64, string]{}

var AgentTaskCache = Cache[int64, *Cache[string, interface{}]]{}

var ProductCache = Cache[string, string]{}

func AddAgentTask(agentId int64, taskId string, task interface{}) {
	innerCache, exists := AgentTaskCache.Get(agentId)
	if !exists {
		innerCache = &Cache[string, interface{}]{}
	}
	innerCache.Set(taskId, task)
	AgentTaskCache.Set(agentId, innerCache)
}

func RemoveAgentTask(agentId int64, taskId string) {
	innerCache, exists := AgentTaskCache.Get(agentId)
	if exists {
		innerCache.Delete(taskId)
		if len(innerCache.GetAll()) == 0 {
			AgentTaskCache.Delete(agentId)
		}
	}
}

func GetAllTasksForAgent(agentId int64) map[string]interface{} {
	innerCache, exists := AgentTaskCache.Get(agentId)
	if !exists {
		return nil
	}
	return innerCache.GetAll()
}

func SetLicenseValid(valid bool) {
	validLicense.Store(valid)
}

func IsValidLicense() bool {
	return validLicense.Load()
}
