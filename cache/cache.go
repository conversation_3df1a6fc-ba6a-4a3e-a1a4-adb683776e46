package cache

import "sync"

// Cache is a thread-safe generic in-memory cache.
type Cache[K comparable, V any] struct {
	data sync.Map
}

// Set adds a key-value pair to the cache.
func (c *Cache[K, V]) Set(key K, value V) {
	c.data.Store(key, value)
}

// Get retrieves a value by key.
func (c *Cache[K, V]) Get(key K) (V, bool) {
	val, ok := c.data.Load(key)
	if !ok {
		var zero V
		return zero, false
	}
	return val.(V), true
}

// Delete removes a key from the cache.
func (c *Cache[K, V]) Delete(key K) {
	c.data.Delete(key)
}

// Range iterates over all items in the cache.
func (c *Cache[K, V]) Range(f func(K, V) bool) {
	c.data.Range(func(key, value any) bool {
		return f(key.(K), value.(V))
	})
}

func (c *Cache[K, V]) GetAll() map[K]V {
	result := make(map[K]V)
	c.data.Range(func(key, value any) bool {
		result[key.(K)] = value.(V)
		return true
	})
	return result
}

func (c *Cache[K, V]) CleanAll() {
	c.data.Range(func(key, _ any) bool {
		c.data.Delete(key)
		return true
	})
}
