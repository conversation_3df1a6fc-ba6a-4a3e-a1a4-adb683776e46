package client

import (
	"deployment/constant"
	"deployment/logger"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/gosnmp/gosnmp"
	g "github.com/gosnmp/gosnmp"
)

const (
	DiscoveryOID                           = "discover.oid"
	SNMPCommunity                          = "community"
	SNMPVersion                            = "version"
	SNMPVersion2c                          = "v2c"
	SNMPVersion3                           = "v3"
	SNMPSecurityLevel                      = "snmp_security_level"
	SNMPSecurityLevelAuthPriv              = "auth_privacy"
	SNMPSecurityLevelAuthNoPriv            = "auth_no_privacy"
	NoPrivateAuthentication                = "no_private_authentication"
	SNMPSecurityLevelPrivateAuthentication = "private_authentication"
	SNMPSecurityUserName                   = "snmp_security_username"
	SNMPAuthProtocol                       = "snmp_authentication_protocol"
	SNMPAuthPassword                       = "snmp_authentication_password"
	SNMPPrivacyProtocol                    = "snmp_privacy_protocol"
	SNMPPrivatePassword                    = "snmp_private_password"
	SystemOID                              = ".*******.*******.0"
)

var (
	authProtocols = map[string]g.SnmpV3AuthProtocol{
		"MD5":    g.MD5,
		"SHA":    g.SHA,
		"SHA224": g.SHA224,
		"SHA256": g.SHA256,
		"SHA384": g.SHA384,
		"SHA512": g.SHA512,
	}

	privProtocols = map[string]g.SnmpV3PrivProtocol{
		"DES":     g.DES,
		"AES":     g.AES,
		"AES192":  g.AES192,
		"AES256":  g.AES256,
		"AES192C": g.AES192C,
		"AES256C": g.AES256C,
	}
)

type (
	SNMPContext map[string]interface{}
)

type Result struct {
	SystemDescription string                   `json:"SystemDescription,omitempty"`
	EntPhysicalTables map[int]EntPhysicalTable `json:"entPhysicalTables,omitempty"`
}

// EntPhysicalTable ...
type EntPhysicalTable struct {
	EntPhysicalMfgName     string `json:"entPhysicalMfgName,omitempty"`
	EntPhysicalName        string `json:"entPhysicalName,omitempty"`
	EntPhysicalSoftwareRev string `json:"entPhysicalSoftwareRev,omitempty"`
}

func (context SNMPContext) Contains(key string) (result bool) {
	if _, found := context[key]; found {
		return true
	}
	return
}

func (context SNMPContext) GetStringValue(key string) (result string) {
	if context.Contains(key) {
		value := context[key]
		if reflect.TypeOf(value).Name() == "string" {
			result = value.(string)
		} else if reflect.TypeOf(value).String() == "*string" {
			result = *value.(*string)
		}
	}
	return
}

type SNMPClient struct {
	target       string
	port         int
	timeout      int
	retryCount   int
	goSNMP       *g.GoSNMP
	discoveryOID string
	errors       []map[string]string
	bulkWalk     bool
	connected    bool
}

func (snmpClient *SNMPClient) Destroy() {
	if snmpClient.goSNMP.Conn != nil {
		err := snmpClient.goSNMP.Conn.Close()
		if err != nil {
			return
		}
	}
}

func (snmpClient *SNMPClient) SetBulkWalk(value bool) {
	snmpClient.bulkWalk = value
}

func (snmpClient *SNMPClient) GetErrors() []map[string]string {

	return snmpClient.errors
}

func (snmpClient *SNMPClient) ResetErrors(index int) {
	snmpClient.errors = append(snmpClient.errors[:index], snmpClient.errors[index+1:]...)
}

func (snmpClient *SNMPClient) setTarget(context SNMPContext) *SNMPClient {
	if context.Contains(constant.Host) {
		snmpClient.target = context[constant.Host].(string)
	}
	return snmpClient
}

func (snmpClient *SNMPClient) setPort(context SNMPContext) *SNMPClient {
	if context.Contains(constant.Port) {
		snmpClient.port = context[constant.Port].(int)
	} else {
		snmpClient.port = 161
	}
	return snmpClient
}

func (snmpClient *SNMPClient) setRetryCount(context SNMPContext) *SNMPClient {
	if context.Contains(constant.RetryCount) {
		snmpClient.retryCount = context[constant.RetryCount].(int)
	} else {
		snmpClient.retryCount = 1
	}
	return snmpClient
}

func (snmpClient *SNMPClient) setDiscoveryOID(context SNMPContext) *SNMPClient {
	if context.Contains(DiscoveryOID) {
		snmpClient.discoveryOID = context[DiscoveryOID].(string)
	} else {
		snmpClient.discoveryOID = ".*******.*******.0"
	}
	return snmpClient
}

func (snmpClient *SNMPClient) setTimeout(context SNMPContext) *SNMPClient {
	if context.Contains(constant.Timeout) {
		snmpClient.timeout = context[constant.Timeout].(int)
	} else {
		snmpClient.timeout = 60
	}
	return snmpClient
}

func getAuthenticationProtocol(authenticationProtocol string) g.SnmpV3AuthProtocol {
	if strings.Contains(authenticationProtocol, "MD5") {
		return g.MD5
	} else if strings.Contains(authenticationProtocol, "SHA224") {
		return g.SHA224
	} else if strings.Contains(authenticationProtocol, "SHA256") {
		return g.SHA256
	} else if strings.Contains(authenticationProtocol, "SHA384") {
		return g.SHA384
	} else if strings.Contains(authenticationProtocol, "SHA512") {
		return g.SHA512
	} else if strings.Contains(authenticationProtocol, "SHA") {
		return g.SHA
	}
	return g.NoAuth
}

func getPrivacyProtocol(authenticationProtocol string) g.SnmpV3PrivProtocol {
	privProtocol := g.NoPriv
	if strings.Contains(authenticationProtocol, "AES256C") {
		privProtocol = g.AES256C
	} else if strings.Contains(authenticationProtocol, "AES192C") {
		privProtocol = g.AES192C
	} else if strings.Contains(authenticationProtocol, "AES256") {
		privProtocol = g.AES256
	} else if strings.Contains(authenticationProtocol, "AES192") {
		privProtocol = g.AES192
	} else if strings.Contains(authenticationProtocol, "AES128") || strings.Contains(authenticationProtocol, "AES") {
		privProtocol = g.AES
	} else if strings.Contains(authenticationProtocol, "DES") {
		privProtocol = g.DES
	}
	return privProtocol
}

func (snmpClient *SNMPClient) setParamsWithSNMP3(context SNMPContext) *SNMPClient {
	if context.Contains(constant.SnmpVer) && context[constant.SnmpVer] != nil && context.GetStringValue(constant.SnmpVer) == constant.SnmpVerV3 {
		logger.DiscoveryLogger.Debug("setting v3 parameter for " + snmpClient.target)
		params := &g.GoSNMP{
			Target:             snmpClient.target,
			Port:               uint16(snmpClient.port),
			Timeout:            time.Duration(snmpClient.timeout) * time.Second,
			Retries:            snmpClient.retryCount,
			ExponentialTimeout: true,
		}
		params.Version = g.Version3
		params.SecurityModel = g.UserSecurityModel
		level := strings.ToLower(context.GetStringValue(SNMPSecurityLevel))
		if strings.EqualFold(level, SNMPSecurityLevelAuthNoPriv) || strings.EqualFold(level, NoPrivateAuthentication) {
			logger.DiscoveryLogger.Debug("setting v3 parameter auth_no_privacy for " + snmpClient.target)
			params.MsgFlags = g.AuthNoPriv
			params.SecurityParameters = &g.UsmSecurityParameters{
				UserName:                 context.GetStringValue(SNMPSecurityUserName),
				AuthenticationProtocol:   getAuthenticationProtocol(strings.ToUpper(context.GetStringValue(SNMPAuthProtocol))),
				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),
			}
		} else if strings.EqualFold(level, SNMPSecurityLevelAuthPriv) || strings.EqualFold(level, SNMPSecurityLevelPrivateAuthentication) {
			logger.DiscoveryLogger.Debug("setting v3 parameter auth_privacy for " + snmpClient.target)
			params.MsgFlags = g.AuthPriv
			params.SecurityParameters = &g.UsmSecurityParameters{
				UserName:                 context.GetStringValue(SNMPSecurityUserName),
				AuthenticationProtocol:   getAuthenticationProtocol(strings.ToUpper(context.GetStringValue(SNMPAuthProtocol))),
				PrivacyProtocol:          getPrivacyProtocol(strings.ToUpper(context.GetStringValue(SNMPPrivacyProtocol))),
				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),
				PrivacyPassphrase:        context.GetStringValue(SNMPPrivatePassword),
			}
		} else {
			logger.DiscoveryLogger.Debug("setting v3 parameter NoAuthNoPriv for " + snmpClient.target)
			params.MsgFlags = g.NoAuthNoPriv
			params.SecurityParameters = &g.UsmSecurityParameters{UserName: context.GetStringValue(SNMPSecurityUserName)}
		}
		snmpClient.goSNMP = params

	} else if context.Contains(constant.CommunityString) && context[constant.CommunityString] != nil && len(context.GetStringValue(constant.CommunityString)) > 0 { //temporary added to check community string
		logger.DiscoveryLogger.Debug("setting v2 parameter for " + snmpClient.target)
		params := &g.GoSNMP{
			Target:             snmpClient.target,
			Port:               uint16(snmpClient.port),
			Timeout:            time.Duration(snmpClient.timeout) * time.Second,
			Retries:            snmpClient.retryCount,
			ExponentialTimeout: true,
		}
		params.Version = g.Version2c
		params.Community = context.GetStringValue(constant.CommunityString)
		snmpClient.goSNMP = params
		logger.DiscoveryLogger.Debug("connecting to snmp device using community string for " + snmpClient.target)
	} else {
		logger.DiscoveryLogger.Debug("connecting to snmp device using default setting for " + snmpClient.target)
		g.Default.Target = snmpClient.target
		g.Default.Port = uint16(snmpClient.port)
		g.Default.Retries = snmpClient.retryCount
		g.Default.Timeout = time.Duration(snmpClient.timeout) * time.Second
		snmpClient.goSNMP = g.Default
	}
	return snmpClient
}
func (snmpClient *SNMPClient) setParams(context SNMPContext) *SNMPClient {
	usm := &gosnmp.UsmSecurityParameters{
		AuthenticationProtocol: gosnmp.SHA,
		PrivacyProtocol:        gosnmp.DES,
	}
	params := &g.GoSNMP{
		Target:             snmpClient.target,
		Port:               uint16(snmpClient.port),
		Timeout:            time.Duration(snmpClient.timeout) * time.Second,
		Retries:            snmpClient.retryCount,
		ExponentialTimeout: true,
		SecurityParameters: usm,
	}
	params.SecurityModel = gosnmp.UserSecurityModel
	params.MsgFlags = gosnmp.NoAuthNoPriv
	switch context[SNMPVersion] {
	case SNMPVersion2c:
		params.Version = g.Version2c
		params.Community = context.GetStringValue(SNMPCommunity)
		break
	case SNMPVersion3:
		if context.Contains(constant.Context) {
			params.ContextName = context.GetStringValue(constant.Context)
		}
		params.Version = g.Version3
		params.SecurityModel = g.UserSecurityModel
		switch context[SNMPSecurityLevel] {
		case SNMPSecurityLevelAuthNoPriv:
			params.MsgFlags = g.AuthNoPriv
			params.SecurityParameters = &g.UsmSecurityParameters{
				UserName:                 context.GetStringValue(SNMPSecurityUserName),
				AuthenticationProtocol:   authProtocols[context.GetStringValue(SNMPAuthProtocol)],
				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),
			}
			break
		case SNMPSecurityLevelAuthPriv:
			params.MsgFlags = g.AuthPriv
			params.SecurityParameters = &g.UsmSecurityParameters{
				UserName:                 context.GetStringValue(SNMPSecurityUserName),
				AuthenticationProtocol:   authProtocols[context.GetStringValue(SNMPAuthProtocol)],
				AuthenticationPassphrase: context.GetStringValue(SNMPAuthPassword),
				PrivacyProtocol:          privProtocols[context.GetStringValue(SNMPPrivacyProtocol)],
				PrivacyPassphrase:        context.GetStringValue(SNMPPrivatePassword),
			}
			break
		default:
			params.MsgFlags = g.NoAuthNoPriv
			params.SecurityParameters = &g.UsmSecurityParameters{UserName: context.GetStringValue(SNMPSecurityUserName)}
		}
		break
	default:
		params.Version = g.Version1
		params.Community = context.GetStringValue(SNMPCommunity)
	}
	snmpClient.goSNMP = params
	return snmpClient
}

func (snmpClient *SNMPClient) SetSNMPParameters(context SNMPContext) *SNMPClient {
	return snmpClient.setTarget(context).setPort(context).setTimeout(context).setRetryCount(context).setDiscoveryOID(context).setParamsWithSNMP3(context)
}

func (snmpClient *SNMPClient) Init() bool {
	err := snmpClient.goSNMP.Connect()
	if err != nil {
		logger.DiscoveryLogger.Error("error while connecting snmp target " + snmpClient.target + " " + err.Error())
		snmpClient.errors = append(snmpClient.errors, map[string]string{
			constant.Error:   err.Error(),
			constant.Message: fmt.Sprintf(constant.ErrorMessageConnectionFailed, "SNMP", snmpClient.target, snmpClient.port),
		})
	} else {
		logger.DiscoveryLogger.Debug("init snmp connection completed now getting sysOid for " + snmpClient.target)
		oid := make(map[string]string)
		if len(snmpClient.discoveryOID) > 0 {
			oid[SystemOID] = DiscoveryOID
		} else {
			oid[snmpClient.discoveryOID] = DiscoveryOID
		}
		metrics, _ := snmpClient.GetScalarMetrics(oid)
		if len(metrics) > 0 {
			snmpClient.connected = true
		} else {
			logger.DiscoveryLogger.Debug("failed to get sysoid for " + snmpClient.target)
		}
	}
	return snmpClient.connected
}

func (snmpClient *SNMPClient) GetScalarMetrics(oids map[string]string) (metrics SNMPContext, invalidMetrics []string) {
	metrics = make(SNMPContext)
	var validOIDs []string
	if oids != nil && len(oids) > 0 {
		for oid := range oids {
			validOIDs = append(validOIDs, strings.TrimSpace(oid))
		}
	}

	result, err := snmpClient.goSNMP.Get(validOIDs)
	if err == nil {
		for _, variable := range result.Variables {
			if variable.Value != nil {
				oid := oids[variable.Name]
				switch variable.Type {
				case g.OctetString:
					if reflect.ValueOf(variable.Value).Kind().String() == "slice" {
						if isASCII(string(variable.Value.([]uint8))) &&
							isValidCharacter(string(variable.Value.([]uint8))) {
							metrics[oid] = string(variable.Value.([]uint8))
						} else if len(variable.Value.([]uint8)) == 6 {
							metrics[oid] = stringSliceToMacAddress(variable.Value)
						}
					} else {
						metrics[oid] = string(variable.Value.([]byte))
					}
				default:
					metrics[oid] = variable.Value
				}
			} else {
				invalidMetrics = append(invalidMetrics, variable.Name)
			}
		}
	} else {
		logger.DiscoveryLogger.Debug("failed to get GetScalarMetrics for " + snmpClient.target + " " + err.Error())
	}
	return
}

func (snmpClient *SNMPClient) GetSystemInformationResult() (Result, error) {
	result := Result{SystemDescription: "", EntPhysicalTables: map[int]EntPhysicalTable{}}
	for _, oid := range []string{"*******.*******.0", "*******.********.*******.1", "*******.********.********.1", "*******.********.*******.1", "*******.********.********.1"} {
		response, err := snmpClient.goSNMP.Get([]string{oid})
		if err != nil {
			logger.DiscoveryLogger.Error("Error for " + snmpClient.target + " " + oid + " " + err.Error())
			return Result{}, err

		}
		for _, variable := range response.Variables {
			switch {
			case variable.Name == ".*******.*******.0":
				if variable.Type == gosnmp.OctetString {
					result.SystemDescription = string(variable.Value.([]byte))
				}
			case strings.HasPrefix(variable.Name, ".*******.********.********."):
				i, err := strconv.Atoi(strings.TrimPrefix(variable.Name, ".*******.********.********."))
				if err != nil {
					return Result{}, err
				}
				if variable.Type == gosnmp.OctetString {
					physicalTable := result.EntPhysicalTables[i]
					physicalTable.EntPhysicalMfgName = string(variable.Value.([]byte))
					result.EntPhysicalTables[i] = physicalTable
				}
			case strings.HasPrefix(variable.Name, ".*******.********.*******."):
				i, err := strconv.Atoi(strings.TrimPrefix(variable.Name, ".*******.********.*******."))
				if err != nil {
					return Result{}, err
				}
				if variable.Type == gosnmp.OctetString {
					physicalTable := result.EntPhysicalTables[i]
					physicalTable.EntPhysicalMfgName = string(variable.Value.([]byte))
					result.EntPhysicalTables[i] = physicalTable
				}
			case strings.HasPrefix(variable.Name, ".*******.********.*******."):
				index, err := strconv.Atoi(strings.TrimPrefix(variable.Name, ".*******.********.*******."))
				if err != nil {
					return Result{}, err
				}
				if variable.Type == gosnmp.OctetString {
					physicalTable := result.EntPhysicalTables[index]
					physicalTable.EntPhysicalName = string(variable.Value.([]byte))
					result.EntPhysicalTables[index] = physicalTable
				}
			case strings.HasPrefix(variable.Name, ".*******.********.********."):
				index, err := strconv.Atoi(strings.TrimPrefix(variable.Name, ".*******.********.********."))
				if err != nil {
					return Result{}, err
				}
				if variable.Type == gosnmp.OctetString {
					physicalTable := result.EntPhysicalTables[index]
					physicalTable.EntPhysicalSoftwareRev = string(variable.Value.([]byte))
					result.EntPhysicalTables[index] = physicalTable
				}
			}
		}
	}

	return result, nil
}

func isASCII(value string) bool {
	for _, char := range value {
		if char > unicode.MaxASCII {

			return false
		}
	}
	return true
}

func isValidCharacter(value string) bool {
	for _, char := range value {
		if !unicode.IsPrint(char) && char != 13 && char != 10 {
			return false
		}
	}
	return true
}

func stringSliceToMacAddress(tokens interface{}) (macAddress string) {
	macAddress = ""
	if tokens != nil {
		for index, token := range tokens.([]uint8) {
			hex := fmt.Sprintf("%X", token)
			if len(hex) == 1 {
				hex = "0" + hex
			}
			if index == len(tokens.([]uint8))-1 {
				macAddress = macAddress + hex
			} else {
				macAddress = macAddress + hex + ":"
			}
		}
	}
	return
}

func (snmpClient *SNMPClient) GetInterfaceData(oids map[string]string) map[string][]interface{} {
	resultMap := make(map[string][]interface{})
	if oids != nil && len(oids) > 0 {
		for oid := range oids {
			tableOIDs := make(map[string]interface{})
			key := oids[oid]
			tableOIDs[oid] = oids[oid]
			rows := snmpClient.getTable(tableOIDs, false, false, false)
			if rows != nil && len(rows) > 0 {
				rowData := make([]interface{}, 0)
				for _, data := range rows {
					if _, found := data[key]; found {
						if key == "interface-address" {
							rowData = append(rowData, stringSliceToMacAddress(data[key]))
						} else {
							rowData = append(rowData, data[key])
						}
					} else {
						rowData = append(rowData, 0)
					}
				}
				resultMap[oids[oid]] = rowData
			}
		}
	}
	return resultMap
}

func (snmpClient *SNMPClient) getTable(oids map[string]interface{}, appendIndex bool, appendOID bool, manipulate bool) (table []map[string]interface{}) {
	rows := make(map[int]map[string]interface{})
	var variableBindings []g.SnmpPDU
	var err error
	for oid := range oids {
		if snmpClient.bulkWalk == true {
			variableBindings, err = snmpClient.goSNMP.BulkWalkAll(strings.TrimSpace(oid))
		} else {
			variableBindings, err = snmpClient.goSNMP.WalkAll(strings.TrimSpace(oid))
		}

		if err == nil && variableBindings == nil {
			snmpClient.errors = append(snmpClient.errors, map[string]string{
				constant.Error:   "Unknown Error",
				constant.Message: "Unknown Error",
				"oid":            oid,
			})
			logger.DiscoveryLogger.Debug("unknown error or variable data is empty for " + snmpClient.target)
		} else /*for v2c/v3 */ if err != nil {
			snmpClient.errors = append(snmpClient.errors, map[string]string{
				constant.Error:   err.Error(),
				constant.Message: err.Error(),
				"oid":            oid,
			})
			logger.DiscoveryLogger.Debug("error while getting table data :- %v", err.Error()+" for "+snmpClient.target)
		}

		if variableBindings != nil && len(variableBindings) > 0 {
			for index := range variableBindings {
				if _, found := rows[index]; !found {
					rows[index] = make(map[string]interface{})
				}
			}
			rowIndex := -1
			for ind := range variableBindings {
				rowIndex++
				if variableBindings[ind].Value != nil {
					oidIndex := oids[oid]
					strOidIndex := fmt.Sprintf("%v", oidIndex)
					switch variableBindings[ind].Type {
					case g.OctetString:
						if reflect.ValueOf(variableBindings[ind].Value).Kind().String() == "string" {
							rows[rowIndex][strOidIndex] = variableBindings[ind].Value
						} else if reflect.ValueOf(variableBindings[ind].Value).Kind().String() == "slice" {
							if manipulate {
								if isASCII(string(variableBindings[ind].Value.([]uint8))) {
									rows[rowIndex][strOidIndex] = string(variableBindings[ind].Value.([]uint8))
								} else {
									rows[rowIndex][strOidIndex] = stringSliceToMacAddress(variableBindings[ind].Value)
								}
							} else {
								rows[rowIndex][strOidIndex] = variableBindings[ind].Value.([]uint8)
							}
						} else {
							rows[rowIndex][strOidIndex] = string(variableBindings[ind].Value.([]byte))
						}
					default:
						rows[rowIndex][strOidIndex] = variableBindings[ind].Value
					}

					if appendIndex {
						indexes := strings.Split(variableBindings[ind].Name, ".")
						rows[rowIndex][strOidIndex+".index"] = indexes[len(indexes)-1]
					}
					if appendOID {
						rows[rowIndex][strOidIndex+".oid"] = variableBindings[ind].Name
					}
				} else {
					snmpClient.errors = append(snmpClient.errors, map[string]string{
						constant.Error:   "Invalid SNMP OID Values",
						"oid":            variableBindings[ind].Name,
						constant.Message: "Invalid SNMP OID Values",
					})
					logger.DiscoveryLogger.Debug("invalid SNMP OID Values for :- %v", variableBindings[ind].Name+" for "+snmpClient.target)
				}
			}
		}
	}
	lenOfRows := len(rows)
	row := 0
	for row < lenOfRows {
		table = append(table, rows[row])
		row++
	}
	return
}
