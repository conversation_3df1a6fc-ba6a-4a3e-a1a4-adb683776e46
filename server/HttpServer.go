package server

import (
	"deployment/common"
	"deployment/controller"
	"deployment/logger"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"golang.org/x/net/http2"
)

type HttpServer struct {
	httpServer *http.Server
}

func NewServer() *HttpServer {
	address := common.GetEnv("HTTP_SERVER_HOST", "0.0.0.0")
	port := common.GetEnv("HTTP_SERVER_PORT", "8088")
	logger.ServiceLogger.Info(fmt.Sprintf("starting https server at %s:%s", address, port))
	router := mux.NewRouter()
	controller.Routes(router)
	httpServer := &http.Server{
		Addr:        address + ":" + port,
		Handler:     router,
		IdleTimeout: 5 * time.Minute,
	}
	return &HttpServer{httpServer: httpServer}
}

func (server *HttpServer) ListenAndServe() error {
	secureServer := common.GetEnv("SECURE_HTTP_SERVER", "true")
	err := http2.ConfigureServer(server.httpServer, &http2.Server{})
	if err != nil {
		panic(err)
	}

	go func() {
		secServer, _ := strconv.ParseBool(secureServer)
		if secServer {
			workingDir := common.CurrentWorkingDir()
			certFile := filepath.Join(workingDir, "server.pem")
			keyFile := filepath.Join(workingDir, "server.key")
			logger.ServiceLogger.Info("https server started...")
			err := server.httpServer.ListenAndServeTLS(certFile, keyFile)
			if err != nil {
				panic(err)
			}
		} else {
			err := server.httpServer.ListenAndServe()
			if err != nil {
				panic(err)
			}
		}

	}()
	return nil
}

func (server *HttpServer) Shutdown() error {
	logger.ServiceLogger.Info("shutting down https server")
	if server.httpServer != nil {
		err := server.httpServer.Close()
		server.httpServer = nil
		if err != nil {
			return err
		}
	}
	return nil
}
