package rest

import (
	"deployment/model"
	"strings"
)

type PatchDeclinePolicyRest struct {
	BaseEntityRest
	ComputerGroupFilterRest
	Description             string                        `json:"description"`
	Platform                string                        `json:"platform"`
	ApplicationPatchSetting []ApplicationPatchSettingRest `json:"applicationPatchSetting"`
}

type ApplicationPatchSettingRest struct {
	ProductId    int64    `json:"productId"`
	SeverityList []string `json:"severityList"`
}

func ToApplicationPatchSettingRestList(pchSettingList []model.ApplicationPatchSetting) []ApplicationPatchSettingRest {
	var pchSettingRestList []ApplicationPatchSettingRest
	for _, pchSetting := range pchSettingList {
		var severityList []string
		for _, sev := range pchSetting.SeverityList {
			severityList = append(severityList, sev.String())
		}
		pchSettingRest := ApplicationPatchSettingRest{
			ProductId:    pchSetting.ProductId,
			SeverityList: severityList,
		}
		pchSettingRestList = append(pchSettingRestList, pchSettingRest)
	}
	return pchSettingRestList
}

func ToApplicationPatchSettingList(pchSettingList []ApplicationPatchSettingRest) []model.ApplicationPatchSetting {
	var pchSettingRestList []model.ApplicationPatchSetting
	for _, pchSetting := range pchSettingList {
		var severityList []model.PatchSeverity
		for _, sev := range pchSetting.SeverityList {
			var severity model.PatchSeverity
			severityList = append(severityList, severity.ToPatchSeverity(strings.ToLower(sev)))
		}
		pchSettingRest := model.ApplicationPatchSetting{
			ProductId:    pchSetting.ProductId,
			SeverityList: severityList,
		}
		pchSettingRestList = append(pchSettingRestList, pchSettingRest)
	}
	return pchSettingRestList
}
