package rest

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"fmt"
	"strings"
)

// QueryResult holds the parameterized query and its parameters
type QueryResult struct {
	Query      string
	Parameters []interface{}
}

// ParameterizedQueryBuilder builds parameterized SQL queries to prevent SQL injection
type ParameterizedQueryBuilder struct {
	query      strings.Builder
	parameters []interface{}
	paramIndex int
}

// NewParameterizedQueryBuilder creates a new query builder
func NewParameterizedQueryBuilder() *ParameterizedQueryBuilder {
	return &ParameterizedQueryBuilder{
		parameters: make([]interface{}, 0),
		paramIndex: 1,
	}
}

// AddParameter adds a parameter and returns the placeholder
func (qb *ParameterizedQueryBuilder) AddParameter(value interface{}) string {
	qb.parameters = append(qb.parameters, value)
	placeholder := "?"
	qb.paramIndex++
	return placeholder
}

// WriteString writes a string to the query
func (qb *ParameterizedQueryBuilder) WriteString(s string) {
	qb.query.WriteString(s)
}

// Build returns the final query and parameters
func (qb *ParameterizedQueryBuilder) Build() QueryResult {
	return QueryResult{
		Query:      qb.query.String(),
		Parameters: qb.parameters,
	}
}

// PrepareParameterizedQueryFromSearchFilter creates a parameterized query from SearchFilter with JOIN support
func PrepareParameterizedQueryFromSearchFilter(filter SearchFilter, tableName string, isCountQual bool, queryCondition string) QueryResult {
	queryBuilder := NewParameterizedQueryBuilder()
	qualifications := filter.Qualification

	// Build column selection with DISTINCT support
	columnSelection := buildColumnSelection(filter, isCountQual)

	// Add table alias for main table if there are joins
	queryBuilder.WriteString(fmt.Sprintf("SELECT %s FROM deployment.%s", columnSelection, common.SanitizeSQLIdentifier(tableName)))

	mainTableAlias := ""
	if len(filter.Joins) > 0 {
		mainTableAlias = "main"
		queryBuilder.WriteString(fmt.Sprintf(" AS %s", mainTableAlias))
	}

	// Add JOIN clauses
	for _, join := range filter.Joins {
		joinSQL := buildJoinClause(join)
		if joinSQL != "" {
			queryBuilder.WriteString(" ")
			queryBuilder.WriteString(joinSQL)
		}
	}

	// Handle archived filter
	hasWhereClause := false
	if !filter.IncludeArchived {
		removedColumn := "removed"
		if mainTableAlias != "" {
			removedColumn = fmt.Sprintf("%s.removed", mainTableAlias)
		}
		queryBuilder.WriteString(fmt.Sprintf(" WHERE %s = ", removedColumn))
		queryBuilder.WriteString(queryBuilder.AddParameter(false))
		hasWhereClause = true
	}

	var conditions []string
	var orConditions []string

	for _, qualification := range qualifications {
		// Handle column name with potential table alias
		column := sanitizeColumnWithAlias(qualification.Column)

		// Validate operator
		if !common.ValidateSQLOperator(qualification.Operator) {
			logger.ServiceLogger.Error("Invalid operator: " + qualification.Operator)
			continue
		}

		qualificationValue := qualification.Value
		condition := qualification.Condition
		operator := qualification.Operator

		// Convert all value types to string for unified handling
		value := ""
		if boolean, ok := qualificationValue.(bool); ok {
			value = fmt.Sprint(boolean)
		} else if intVal, ok := qualificationValue.(int64); ok {
			value = fmt.Sprintf("%d", intVal)
		} else if floatVal, ok := qualificationValue.(float64); ok {
			value = fmt.Sprintf("%.0f", floatVal)
		} else if strVal, ok := qualificationValue.(string); ok {
			value = strVal
		} else {
			// Handle list/array values
			condStr := queryBuilder.buildListCondition(column, operator, qualificationValue, qualification)
			if condStr != "" {
				if strings.EqualFold(condition, "OR") || condition == "" {
					orConditions = append(orConditions, condStr)
				} else {
					// Flush existing OR conditions before adding a new AND condition
					if len(orConditions) > 0 {
						conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(orConditions, " OR ")))
						orConditions = []string{}
					}
					conditions = append(conditions, condStr)
				}
			}
			continue
		}

		// Sanitize string value
		value = common.SanitizeStringValue(value)

		// Build condition using unified string-based approach
		condStr := queryBuilder.buildUnifiedCondition(column, operator, value, qualification)

		if condStr != "" {
			if strings.EqualFold(condition, "OR") || condition == "" {
				orConditions = append(orConditions, condStr)
			} else {
				// Flush existing OR conditions before adding a new AND condition
				if len(orConditions) > 0 {
					conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(orConditions, " OR ")))
					orConditions = []string{}
				}
				conditions = append(conditions, condStr)
			}
		}
	}

	// Add remaining OR conditions
	if len(orConditions) > 0 {
		conditions = append(conditions, fmt.Sprintf("(%s)", strings.Join(orConditions, " OR ")))
	}

	// Add conditions to query
	if len(conditions) > 0 {
		if hasWhereClause {
			queryBuilder.WriteString(" AND ")
		} else {
			queryBuilder.WriteString(" WHERE ")
			hasWhereClause = true
		}
		queryBuilder.WriteString(strings.Join(conditions, " AND "))
	}

	// Add additional query condition
	if queryCondition != "" {
		if hasWhereClause {
			queryBuilder.WriteString(" AND ")
		} else {
			queryBuilder.WriteString(" WHERE ")
		}
		queryBuilder.WriteString(queryCondition)
	}

	// Add GROUP BY
	if filter.GroupBy != "" {
		queryBuilder.WriteString(" GROUP BY ")
		queryBuilder.WriteString(sanitizeMultipleColumns(filter.GroupBy))
	}

	// Add ORDER BY and LIMIT for non-count queries
	if !isCountQual {
		if filter.SortBy != "" {
			by := filter.SortBy
			direction := "DESC"
			if strings.Contains(by, "-") {
				direction = "ASC"
				by = strings.ReplaceAll(by, "-", "")
			}
			by = sanitizeColumnWithAlias(by)
			queryBuilder.WriteString(fmt.Sprintf(" ORDER BY %s %s", by, direction))
		} else {
			queryBuilder.WriteString(" ORDER BY \"id\" DESC")
		}

		if filter.Size > 0 {
			queryBuilder.WriteString(" OFFSET ")
			queryBuilder.WriteString(queryBuilder.AddParameter(filter.Offset))
			queryBuilder.WriteString(" LIMIT ")
			queryBuilder.WriteString(queryBuilder.AddParameter(filter.Size))
		}
	}

	result := queryBuilder.Build()
	logger.ServiceLogger.Trace("Parameterized query: " + result.Query)
	logger.ServiceLogger.Trace("Parameters: %v", result.Parameters)

	return result
}

// buildUnifiedCondition builds SQL conditions for all value types using string-based approach
func (qb *ParameterizedQueryBuilder) buildUnifiedCondition(column, operator, value string, qualification Qualification) string {
	// Handle special column cases first
	// Extract just the column name for special case checking (remove table alias)
	columnForChecking := column
	if strings.Contains(column, ".") {
		parts := strings.Split(column, ".")
		if len(parts) == 2 {
			columnForChecking = parts[1]
		}
	}
	columnLower := strings.ToLower(columnForChecking)
	operatorLower := strings.ToLower(operator)

	// Handle OS/Platform columns
	if columnLower == "os" || columnLower == "platform" || columnLower == "os_platform" {
		return qb.buildOSCondition(column, operator, value)
	}

	// Handle JSONB columns (tags, affected_products)
	if columnLower == "tags" || columnLower == "affected_products" {
		return qb.buildJSONBCondition(column, operator, value)
	}

	// Handle enum types based on qualification type
	if strings.ToLower(qualification.Type) == "enum" {
		return qb.buildEnumCondition(column, operator, value, qualification)
	}

	// Handle enum columns
	if qb.isEnumColumn(columnLower) {
		return qb.buildEnumColumnCondition(column, operator, value)
	}

	// Handle numeric columns that need casting for ILIKE operations
	if qb.needsCasting(column) {
		return qb.buildCastCondition(column, value)
	}

	// Handle field_list_contains operator
	if operatorLower == "field_list_contains" {
		return qb.buildFieldListContainsCondition(qualification.ColumnList, value)
	}

	// Handle contains_with_in operator
	if operatorLower == "contains_with_in" {
		return qb.buildContainsWithInStringCondition(column, value)
	}

	// Handle standard operators
	// Use formatColumnName to handle both simple columns and table.column format
	formattedColumn := formatColumnName(column)

	switch operatorLower {
	case "contains":
		placeholder := qb.AddParameter("%" + value + "%")
		return fmt.Sprintf("%s ILIKE %s", formattedColumn, placeholder)
	case "not_contains":
		placeholder := qb.AddParameter("%" + value + "%")
		return fmt.Sprintf("%s NOT ILIKE %s", formattedColumn, placeholder)
	case "equals", "=":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
	case "not_equals", "!=":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s != %s", formattedColumn, placeholder)
	case ">":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s > %s", formattedColumn, placeholder)
	case "<":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s < %s", formattedColumn, placeholder)
	case ">=":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s >= %s", formattedColumn, placeholder)
	case "<=":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s <= %s", formattedColumn, placeholder)
	case "like":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s LIKE %s", formattedColumn, placeholder)
	case "ilike":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s ILIKE %s", formattedColumn, placeholder)
	case "not_like", "not like":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s NOT LIKE %s", formattedColumn, placeholder)
	case "not_ilike", "not ilike":
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s NOT ILIKE %s", formattedColumn, placeholder)
	case "is_null", "is null":
		return fmt.Sprintf("%s IS NULL", formattedColumn)
	case "is_not_null", "is not null":
		return fmt.Sprintf("%s IS NOT NULL", formattedColumn)
	case "in":
		// Handle IN condition - use buildInCondition for proper string handling
		return qb.buildInCondition(column, value, false)
	case "not_in", "not in":
		// Handle NOT IN condition - use buildInCondition for proper string handling
		return qb.buildInCondition(column, value, true)
	default:
		// Default to equals
		placeholder := qb.AddParameter(value)
		return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
	}
}

// buildOSCondition builds conditions for OS/Platform columns
func (qb *ParameterizedQueryBuilder) buildOSCondition(column, operator, value string) string {
	operatorLower := strings.ToLower(operator)

	if operatorLower == "in" {
		return qb.buildInCondition(column, value, false)
	}

	// Try to convert to OS enum value
	formattedColumn := formatColumnName(column)
	var osType common.OsType
	os := osType.ToOsType(strings.ToLower(value))
	if os.String() != "" {
		placeholder := qb.AddParameter(int(os))
		switch operatorLower {
		case "equals", "=":
			return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
		case "not_equals", "!=":
			return fmt.Sprintf("%s != %s", formattedColumn, placeholder)
		default:
			return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
		}
	}

	// Fallback to text search
	placeholder := qb.AddParameter("%" + value + "%")
	return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
}

// buildContainsWithInStringCondition builds ILIKE condition for string values
func (qb *ParameterizedQueryBuilder) buildContainsWithInStringCondition(column, value string) string {
	formattedColumn := formatColumnName(column)
	placeholder := qb.AddParameter("%" + value + "%")
	return fmt.Sprintf("%s ILIKE %s", formattedColumn, placeholder)
}

// isEnumColumn checks if a column is an enum type that needs special handling
func (qb *ParameterizedQueryBuilder) isEnumColumn(columnLower string) bool {
	enumColumns := map[string]bool{
		"patchstate":            true,
		"taskType":              true,
		"impact":                true,
		"patch_approval_status": true,
		"patch_test_status":     true,
		"rule_type":             true,
	}
	return enumColumns[columnLower]
}

// buildEnumColumnCondition builds conditions for all enum columns in a unified way
func (qb *ParameterizedQueryBuilder) buildEnumColumnCondition(column, operator, value string) string {
	columnLower := strings.ToLower(column)
	operatorLower := strings.ToLower(operator)

	if strings.Contains(columnLower, ".") {
		parts := strings.Split(columnLower, ".")
		if len(parts) == 2 {
			columnLower = parts[1]
		}
	}
	var enumValue int
	var isValidEnum bool

	// Convert string value to appropriate enum integer based on column type
	switch columnLower {
	case "patch_state":
		var patchState model.PatchState
		state := patchState.ToPatchState(strings.ToLower(value))
		if state.String() != "" {
			enumValue = int(state)
			isValidEnum = true
		}
	case "task_type":
		var taskType model.AgentTaskType
		task := taskType.ToTaskType(strings.ToLower(value))
		if task.String() != "" {
			enumValue = int(task)
			isValidEnum = true
		}
	case "impact":
		var impact model.ComplianceImpact
		complianceImpact := impact.ToComplianceImpact(strings.ToLower(value))
		if complianceImpact.String() != "" {
			enumValue = int(complianceImpact)
			isValidEnum = true
		}
	case "patch_approval_status":
		var approvalStatus model.PatchApprovalStatus
		patchApprovedStatus := approvalStatus.ToPatchApprovedStatus(strings.ToLower(value))
		if patchApprovedStatus.String() != "" {
			enumValue = int(patchApprovedStatus)
			isValidEnum = true
		}
	case "patch_test_status":
		var testStatus model.PatchTestStatus
		patchTestStatus := testStatus.ToPatchTestStatus(strings.ToLower(value))
		if patchTestStatus.String() != "" {
			enumValue = int(patchTestStatus)
			isValidEnum = true
		}
	case "rule_type":
		var ruleType model.ComplianceRuleType
		complianceRuleType := ruleType.ToComplianceRuleType(strings.ToLower(value))
		if complianceRuleType.String() != "" {
			enumValue = int(complianceRuleType)
			isValidEnum = true
		}
	}

	formattedColumn := formatColumnName(column)

	// If we have a valid enum value, use it with parameterized query
	if isValidEnum {
		placeholder := qb.AddParameter(enumValue)
		switch operatorLower {
		case "equals", "=":
			return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
		case "not_equals", "!=":
			return fmt.Sprintf("%s != %s", formattedColumn, placeholder)
		case "in":
			return fmt.Sprintf("%s IN (%s)", formattedColumn, placeholder)
		case "not_in", "not in":
			return fmt.Sprintf("%s NOT IN (%s)", column, placeholder)
		case ">":
			return fmt.Sprintf("%s > %s", formattedColumn, placeholder)
		case "<":
			return fmt.Sprintf("%s < %s", formattedColumn, placeholder)
		case ">=":
			return fmt.Sprintf("%s >= %s", formattedColumn, placeholder)
		case "<=":
			return fmt.Sprintf("%s <= %s", formattedColumn, placeholder)
		default:
			return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
		}
	}

	// Fallback to text search for invalid enum values
	placeholder := qb.AddParameter("%" + value + "%")
	return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
}

// buildJSONBCondition builds conditions for JSONB columns like tags and affected_products
func (qb *ParameterizedQueryBuilder) buildJSONBCondition(column, operator, value string) string {
	items := strings.Split(value, ",")
	var conditions []string
	formatedColumn := formatColumnName(column)
	for _, item := range items {
		item = strings.TrimSpace(item)
		placeholder := qb.AddParameter(item)
		if strings.EqualFold("not_equals", operator) {
			conditions = append(conditions, fmt.Sprintf("NOT %s @> jsonb_build_array(%s)", formatedColumn, placeholder))
		} else {
			conditions = append(conditions, fmt.Sprintf("%s @> jsonb_build_array(%s)", formatedColumn, placeholder))
		}
	}

	return strings.Join(conditions, " OR ")
}

// buildEnumCondition builds conditions for enum types based on reference
func (qb *ParameterizedQueryBuilder) buildEnumCondition(column, operator, value string, qualification Qualification) string {
	operatorLower := strings.ToLower(operator)
	referenceLower := strings.ToLower(qualification.Reference)
	formattedColumn := formatColumnName(column)

	// Handle OS type specially since it's used in multiple places
	if referenceLower == "os" {
		return qb.buildOSCondition(column, operator, value)
	}

	if referenceLower == "" {
		referenceLower = strings.ToLower(qualification.Column)
	}

	// For other reference-based enums, map to column name and use unified handler
	var columnToCheck string

	switch referenceLower {
	case "patchstate":
		columnToCheck = "patchstate"
	case "tasktype":
		columnToCheck = "taskType"
	default:
		// For unknown references, use standard string handling
		switch operatorLower {
		case "equals", "=":
			placeholder := qb.AddParameter(value)
			return fmt.Sprintf("%s = %s", formattedColumn, placeholder)
		case "not_equals", "!=":
			placeholder := qb.AddParameter(value)
			return fmt.Sprintf("%s != %s", formattedColumn, placeholder)
		case "in":
			placeholder := qb.AddParameter(value)
			return fmt.Sprintf("%s IN (%s)", formattedColumn, placeholder)
		case "not_in", "not in":
			placeholder := qb.AddParameter(value)
			return fmt.Sprintf("%s NOT IN (%s)", formattedColumn, placeholder)
		case "contains":
			placeholder := qb.AddParameter("%" + value + "%")
			return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
		default:
			// Fallback to ILIKE search
			placeholder := qb.AddParameter("%" + value + "%")
			return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
		}
	}

	// If we have a valid column mapping, use the unified enum handler
	if qb.isEnumColumn(columnToCheck) {
		// We'll use the original column name but the mapped enum type
		return qb.buildEnumColumnCondition(column, operator, value)
	}

	// Fallback to standard string handling
	placeholder := qb.AddParameter("%" + value + "%")
	return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
}

// buildCastCondition builds conditions for columns that need casting
func (qb *ParameterizedQueryBuilder) buildCastCondition(column, value string) string {
	// Default to ILIKE for text search
	formattedColumn := formatColumnName(column)
	placeholder := qb.AddParameter("%" + value + "%")
	return fmt.Sprintf("CAST(%s AS TEXT) ILIKE %s", formattedColumn, placeholder)
}

// needsCasting checks if a column needs to be cast to TEXT for ILIKE operations
func (qb *ParameterizedQueryBuilder) needsCasting(column string) bool {
	castColumns := map[string]bool{
		"score":             true,
		"epss_probability":  true,
		"cvss3_base_score":  true,
		"cvss2_base_score":  true,
		"cvss40_base_score": true,
	}
	return castColumns[strings.ToLower(column)]
}

// buildListCondition builds condition for list/array values
func (qb *ParameterizedQueryBuilder) buildListCondition(column, operator string, value interface{}, qualification Qualification) string {
	operatorLower := strings.ToLower(operator)

	switch operatorLower {
	case "in":
		return qb.buildInCondition(column, value, false)
	case "not_in":
		return qb.buildInCondition(column, value, true)
	case "contains_with_in":
		return qb.buildContainsWithInCondition(column, value)
	case "field_list_contains":
		return qb.buildFieldListContainsCondition(qualification.ColumnList, value)
	default:
		return ""
	}
}

// buildContainsWithInCondition builds ILIKE ANY condition
func (qb *ParameterizedQueryBuilder) buildContainsWithInCondition(column string, value interface{}) string {
	var placeholders []string
	formattedColumn := formatColumnName(column)
	if listVal, ok := value.([]string); ok {
		// Handle []string
		for _, v := range listVal {
			placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(v)))
		}
	} else if listVal, ok := value.([]interface{}); ok {
		// Handle []interface{}
		for _, v := range listVal {
			if strVal, ok := v.(string); ok {
				placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(strVal)))
			}
		}
	} else if strVal, ok := value.(string); ok {
		// Handle single string value - key addition for string support
		// Check if it's a comma-separated list or single value
		if strings.Contains(strVal, ",") {
			// Split comma-separated values
			values := strings.Split(strVal, ",")
			for _, v := range values {
				trimmedVal := strings.TrimSpace(v)
				if trimmedVal != "" {
					placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(trimmedVal)))
				}
			}
		} else {
			// Single string value
			placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(strVal)))
		}
	}

	if len(placeholders) > 0 {
		return fmt.Sprintf("%s ILIKE ANY (ARRAY[%s])", formattedColumn, strings.Join(placeholders, ", "))
	}

	return ""
}

// buildFieldListContainsCondition builds condition for multiple columns with OR
func (qb *ParameterizedQueryBuilder) buildFieldListContainsCondition(columnList []string, value interface{}) string {
	strVal := ""
	if val, ok := value.(string); ok {
		strVal = val
	} else if val, ok := value.(int64); ok {
		strVal = fmt.Sprintf("%d", val)
	} else if val, ok := value.(float64); ok {
		strVal = fmt.Sprintf("%.0f", val)
	} else if val, ok := value.(bool); ok {
		strVal = fmt.Sprint(val)
	} else {
		return ""
	}

	var conditions []string
	for _, col := range columnList {
		placeholder := qb.AddParameter("%" + common.SanitizeStringValue(strVal) + "%")
		sanitizedCol := common.SanitizeSQLIdentifier(common.ToSnakeCase(col))
		formattedColumn := formatColumnName(sanitizedCol)
		conditions = append(conditions, fmt.Sprintf("%s ILIKE %s", formattedColumn, placeholder))
	}

	return fmt.Sprintf("(%s)", strings.Join(conditions, " OR "))
}

// buildInCondition builds IN/NOT IN conditions with parameterized values
func (qb *ParameterizedQueryBuilder) buildInCondition(column string, value interface{}, isNotIn bool) string {
	var placeholders []string

	// Handle different value types
	if listVal, ok := value.([]interface{}); ok {
		// Handle []interface{} - convert each element
		for _, v := range listVal {
			if strVal, ok := v.(string); ok {
				placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(strVal)))
			} else if intVal, ok := v.(float64); ok {
				// JSON numbers come as float64
				placeholders = append(placeholders, qb.AddParameter(int64(intVal)))
			} else {
				placeholders = append(placeholders, qb.AddParameter(v))
			}
		}
	} else if intList, ok := value.([]int64); ok {
		// Handle []int64
		for _, v := range intList {
			placeholders = append(placeholders, qb.AddParameter(v))
		}
	} else if strList, ok := value.([]string); ok {
		// Handle []string
		for _, v := range strList {
			placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(v)))
		}
	} else if strVal, ok := value.(string); ok {
		// Handle single string value - this is the key addition for string support
		// Check if it's a comma-separated list or single value
		if strings.Contains(strVal, ",") {
			// Split comma-separated values
			values := strings.Split(strVal, ",")
			for _, v := range values {
				trimmedVal := strings.TrimSpace(v)
				if trimmedVal != "" {
					placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(trimmedVal)))
				}
			}
		} else {
			// Single string value
			placeholders = append(placeholders, qb.AddParameter(common.SanitizeStringValue(strVal)))
		}
	} else if intVal, ok := value.(int64); ok {
		// Handle single int64 value
		placeholders = append(placeholders, qb.AddParameter(intVal))
	} else if floatVal, ok := value.(float64); ok {
		// Handle single float64 value (JSON numbers)
		placeholders = append(placeholders, qb.AddParameter(int64(floatVal)))
	} else if boolVal, ok := value.(bool); ok {
		// Handle single boolean value
		placeholders = append(placeholders, qb.AddParameter(boolVal))
	}

	if len(placeholders) == 0 {
		return ""
	}

	operator := "IN"
	if isNotIn {
		operator = "NOT IN"
	}

	formattedColumn := formatColumnName(column)

	return fmt.Sprintf("%s %s (%s)", formattedColumn, operator, strings.Join(placeholders, ", "))
}

// sanitizeMultipleColumns sanitizes multiple column names for SELECT, GROUP BY, etc.
// Handles simple columns, SQL functions, and aliases properly
func sanitizeMultipleColumns(columns string) string {
	if columns == "" {
		return ""
	}

	// Split by comma, but be careful with commas inside functions
	columnList := smartSplitColumns(columns)
	var sanitizedColumns []string

	for _, col := range columnList {
		trimmedCol := strings.TrimSpace(col)
		if trimmedCol != "" {
			sanitizedCol := sanitizeSingleColumnExpression(trimmedCol)
			if sanitizedCol != "" {
				sanitizedColumns = append(sanitizedColumns, sanitizedCol)
			}
		}
	}

	return strings.Join(sanitizedColumns, ", ")
}

// smartSplitColumns splits columns by comma but respects parentheses in SQL functions
func smartSplitColumns(columns string) []string {
	var result []string
	var current strings.Builder
	parenCount := 0

	for _, char := range columns {
		switch char {
		case '(':
			parenCount++
			current.WriteRune(char)
		case ')':
			parenCount--
			current.WriteRune(char)
		case ',':
			if parenCount == 0 {
				// We're not inside parentheses, so this comma is a separator
				result = append(result, current.String())
				current.Reset()
			} else {
				// We're inside parentheses, so this comma is part of the function
				current.WriteRune(char)
			}
		default:
			current.WriteRune(char)
		}
	}

	// Add the last column
	if current.Len() > 0 {
		result = append(result, current.String())
	}

	return result
}

// sanitizeSingleColumnExpression sanitizes a single column expression
// Handles: column_name, function(column), function(column) as alias
func sanitizeSingleColumnExpression(expr string) string {
	expr = strings.TrimSpace(expr)
	if expr == "" {
		return ""
	}

	// Check if this is an expression with AS alias
	asIndex := findCaseInsensitiveAS(expr)
	if asIndex != -1 {
		// Split into expression and alias
		expression := strings.TrimSpace(expr[:asIndex])
		alias := strings.TrimSpace(expr[asIndex+3:]) // +2 to skip "AS"

		sanitizedExpr := sanitizeColumnOrFunction(expression)
		sanitizedAlias := sanitizeSimpleIdentifier(alias)

		return fmt.Sprintf("%s AS %s", sanitizedExpr, sanitizedAlias)
	}

	// No alias, just sanitize the expression
	return sanitizeColumnOrFunction(expr)
}

// findCaseInsensitiveAS finds " AS " in a case-insensitive way
func findCaseInsensitiveAS(expr string) int {
	upperExpr := strings.ToUpper(expr)
	return strings.Index(upperExpr, " AS ")
}

// sanitizeColumnOrFunction sanitizes a column name or SQL function
func sanitizeColumnOrFunction(expr string) string {
	expr = strings.TrimSpace(expr)

	// Check if this looks like a SQL function (contains parentheses)
	if strings.Contains(expr, "(") && strings.Contains(expr, ")") {
		return sanitizeSQLFunction(expr)
	}

	// Simple column name
	return sanitizeSimpleIdentifier(expr)
}

// sanitizeSQLFunction sanitizes SQL functions like COUNT(column_name) or COUNT(DISTINCT column)
func sanitizeSQLFunction(expr string) string {
	// Find the function name and parameters
	openParen := strings.Index(expr, "(")
	if openParen == -1 {
		return sanitizeSimpleIdentifier(expr)
	}

	functionName := strings.TrimSpace(expr[:openParen])
	remainder := expr[openParen:]

	// Validate function name (should be alphanumeric and underscores only)
	if !isValidSQLFunction(functionName) {
		// If invalid function name, treat as simple identifier
		return sanitizeSimpleIdentifier(expr)
	}

	// For now, return the function as-is since it's a known SQL function
	// In a more sophisticated implementation, we could parse and sanitize the parameters
	return strings.ToUpper(functionName) + remainder
}

// isValidSQLFunction checks if the function name is a known SQL function
func isValidSQLFunction(name string) bool {
	validFunctions := map[string]bool{
		"COUNT":             true,
		"SUM":               true,
		"AVG":               true,
		"MIN":               true,
		"MAX":               true,
		"DISTINCT":          true,
		"UPPER":             true,
		"LOWER":             true,
		"LENGTH":            true,
		"SUBSTRING":         true,
		"CONCAT":            true,
		"COALESCE":          true,
		"CASE":              true,
		"CAST":              true,
		"EXTRACT":           true,
		"DATE":              true,
		"NOW":               true,
		"CURRENT_TIMESTAMP": true,
	}

	return validFunctions[strings.ToUpper(name)]
}

// sanitizeSimpleIdentifier sanitizes a simple column name or alias
func sanitizeSimpleIdentifier(identifier string) string {
	identifier = strings.TrimSpace(identifier)
	if identifier == "" {
		return ""
	}

	// Convert to snake_case and sanitize
	snakeCaseCol := common.ToSnakeCase(identifier)
	sanitizedCol := common.SanitizeSQLIdentifier(snakeCaseCol)

	// Add quotes around the identifier for safety
	return fmt.Sprintf("\"%s\"", strings.Trim(sanitizedCol, "\""))
}

// buildJoinClause builds a JOIN clause from JoinClause struct
func buildJoinClause(join JoinClause) string {
	if join.Table == "" || join.Condition == "" {
		return ""
	}

	// Validate JOIN type
	joinType := strings.ToUpper(strings.TrimSpace(join.Type))
	validJoinTypes := map[string]bool{
		"INNER": true,
		"LEFT":  true,
		"RIGHT": true,
		"FULL":  true,
	}

	if !validJoinTypes[joinType] {
		joinType = "INNER" // Default to INNER JOIN
	}

	// Sanitize table name
	tableName := common.SanitizeSQLIdentifier(join.Table)

	// Build JOIN clause
	joinClause := fmt.Sprintf("%s JOIN deployment.%s", joinType, tableName)

	// Add alias if provided
	if join.Alias != "" {
		alias := common.SanitizeSQLIdentifier(join.Alias)
		joinClause += fmt.Sprintf(" AS %s", alias)
	}

	// Add ON condition (basic validation - should be enhanced for production)
	joinClause += fmt.Sprintf(" ON %s", join.Condition)

	return joinClause
}

// sanitizeColumnWithAlias handles column names that may include table aliases
func sanitizeColumnWithAlias(columnName string) string {
	// Check if column contains table alias (e.g., "main.column_name" or "apr.patch_state")
	if strings.Contains(columnName, ".") {
		parts := strings.Split(columnName, ".")
		if len(parts) == 2 {
			tableAlias := common.SanitizeSQLIdentifier(parts[0])
			column := common.SanitizeSQLIdentifier(common.ToSnakeCase(parts[1]))
			return fmt.Sprintf("%s.%s", tableAlias, column)
		}
	}

	// No table alias, just sanitize the column name
	return common.SanitizeSQLIdentifier(common.ToSnakeCase(columnName))
}

// formatColumnName formats column name for SQL queries, handling table aliases
func formatColumnName(column string) string {
	if strings.Contains(column, ".") {
		// Already contains table alias, just quote the column part
		parts := strings.Split(column, ".")
		if len(parts) == 2 {
			return fmt.Sprintf("%s.\"%s\"", parts[0], parts[1])
		}
	}

	// Simple column name, add quotes
	return fmt.Sprintf("\"%s\"", column)
}

// buildColumnSelection builds the SELECT clause with DISTINCT support
func buildColumnSelection(filter SearchFilter, isCountQual bool) string {
	hasJoins := len(filter.Joins) > 0

	if isCountQual {
		if filter.DistinctColumns != "" {
			cols := sanitizeColumns(filter.DistinctColumns, hasJoins)
			return fmt.Sprintf("COUNT(DISTINCT %s)", cols)
		}
		if filter.Distinct {
			if hasJoins {
				return "COUNT(DISTINCT main.id)"
			} else {
				return "COUNT(DISTINCT id)"
			}
		}
		return "COUNT(1)"
	}

	// Build column list
	var columns string
	if filter.Columns != "" {
		columns = sanitizeColumns(filter.Columns, hasJoins)
	} else {
		columns = "*"
		if hasJoins {
			columns = "main.*"
		}
	}

	// Add DISTINCT if needed
	if filter.Distinct {
		return fmt.Sprintf("DISTINCT %s", columns)
	}
	if filter.DistinctColumns != "" {
		distinctCols := sanitizeColumns(filter.DistinctColumns, hasJoins)
		return fmt.Sprintf("DISTINCT ON (%s) %s", distinctCols, columns)
	}

	return columns
}

// sanitizeColumns handles column sanitization with simple alias support
func sanitizeColumns(columns string, hasJoins bool) string {
	if columns == "" {
		return ""
	}

	// Use smart split to handle commas inside functions
	columnList := smartSplitColumns(columns)
	var result []string

	for _, col := range columnList {
		col = strings.TrimSpace(col)
		if col == "" {
			continue
		}

		// Check if this is a function (contains parentheses)
		if strings.Contains(col, "(") && strings.Contains(col, ")") {
			result = append(result, sanitizeSQLFunction(col))
		} else if strings.Contains(col, ".") {
			// Column with table alias
			result = append(result, sanitizeColumnWithAlias(col))
		} else {
			// Simple column - add main. prefix if we have joins and it's ambiguous
			if hasJoins && isAmbiguousColumn(col) {
				sanitized := common.SanitizeSQLIdentifier(common.ToSnakeCase(col))
				result = append(result, fmt.Sprintf("main.\"%s\"", strings.Trim(sanitized, "\"")))
			} else {
				sanitized := common.SanitizeSQLIdentifier(common.ToSnakeCase(col))
				result = append(result, fmt.Sprintf("\"%s\"", strings.Trim(sanitized, "\"")))
			}
		}
	}

	return strings.Join(result, ", ")
}

// isAmbiguousColumn checks if a column name is commonly ambiguous in JOINs
func isAmbiguousColumn(col string) bool {
	ambiguous := map[string]bool{
		"id": true, "created_time": true, "updated_time": true, "removed": true,
	}
	return ambiguous[strings.ToLower(col)]
}
