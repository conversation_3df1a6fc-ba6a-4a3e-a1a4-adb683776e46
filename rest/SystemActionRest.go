package rest

import "deployment/model"

type SystemActionRest struct {
	BaseEntityRest
	Description    string                   `json:"description"`
	Disabled       bool                     `json:"disabled"`
	WindowsCommand []SystemActionConfigRest `json:"windowsCommand"`
	LinuxCommand   []SystemActionConfigRest `json:"linuxCommand"`
	MacCommand     []SystemActionConfigRest `json:"macCommand"`
}

type SystemActionConfigRest struct {
	OrderId     int    `json:"orderId"`
	CommandType string `json:"commandType"`
	Command     string `json:"command"`
	Platform    string `json:"platform"`
}

func ToSystemActionConfigList(restModelList []SystemActionConfigRest) []model.SystemActionConfig {
	var systemActionConfigList []model.SystemActionConfig
	for _, restModel := range restModelList {
		var commandType model.CommandType
		commandType, _ = commandType.ToCommandType(restModel.CommandType)
		systemActionConfigList = append(systemActionConfigList, model.SystemActionConfig{
			OrderId:     restModel.OrderId,
			CommandType: commandType,
			Command:     restModel.Command,
			Platform:    restModel.Platform,
		})
	}
	return systemActionConfigList
}

func ToSystemActionConfigRestList(domainModelList []model.SystemActionConfig) []SystemActionConfigRest {
	var systemActionConfigRestList []SystemActionConfigRest
	for _, domainModel := range domainModelList {
		systemActionConfigRestList = append(systemActionConfigRestList, SystemActionConfigRest{
			OrderId:     domainModel.OrderId,
			CommandType: domainModel.CommandType.String(),
			Command:     domainModel.Command,
			Platform:    domainModel.Platform,
		})
	}
	return systemActionConfigRestList
}

type SystemActionContext struct {
	AssetId      int64                    `json:"asset_id"`
	PlatformType string                   `json:"platform_type"`
	Platform     string                   `json:"platform"`
	Actions      []int64                  `json:"actions"`
	Context      []map[string]interface{} `json:"remediation_action_context"`
}
