package rest

import (
	"deployment/common"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type LanguageRest struct {
	BaseEntityRest
	Code        int64  `json:"code"`
	EnglishName string `json:"englishName"`
}

func ConvertJsonToLanguageRest(w http.ResponseWriter, r *http.Request, restModel LanguageRest) (LanguageRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &restModel)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid JSON format", http.StatusInternalServerError))
		w.Write<PERSON>eader(http.StatusInternalServerError)
		fmt.Fprintf(w, jsonData)
		return restModel, err
	}

	v := validator.New()
	err = v.Struct(restModel)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprintf(w, jsonData)
			break
		}
	}

	var patchMap map[string]interface{}
	json.Unmarshal(body, &patchMap)
	restModel.PatchMap = patchMap

	return restModel, err
}
