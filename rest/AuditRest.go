package rest

import "deployment/model"

type AuditRest struct {
	Id              int64  `json:"id"`
	DisplayName     string `json:"displayName"`
	CreatedById     int64  `json:"createdById"`
	CreatedTime     int64  `json:"createdTime"`
	RefId           int64  `json:"refId"`
	PerformerId     int64  `json:"performerId"`
	AuditString     string `json:"auditString"`
	AuditEventType  string `json:"auditEventType"`
	AuditEventModel string `json:"auditEventModel"`
}

func CreateAuditRest(auditString, auditType string, refId, createdTime int64) AuditRest {
	return AuditRest{
		CreatedTime:     createdTime,
		RefId:           refId,
		AuditString:     auditString,
		AuditEventModel: model.CreateAudit.String(),
		AuditEventType:  auditType,
	}
}

func UpdateAuditRest(auditString, auditType string, refId, createdTime int64) AuditRest {
	return AuditRest{
		CreatedTime:     createdTime,
		RefId:           refId,
		AuditString:     auditString,
		AuditEventModel: model.CreateAudit.String(),
		AuditEventType:  auditType,
	}
}

func DeleteAuditRest(auditString, auditType string, refId, createdTime int64) AuditRest {
	return AuditRest{
		CreatedTime:     createdTime,
		RefId:           refId,
		AuditString:     auditString,
		AuditEventModel: model.DeleteAudit.String(),
		AuditEventType:  auditType,
	}
}
