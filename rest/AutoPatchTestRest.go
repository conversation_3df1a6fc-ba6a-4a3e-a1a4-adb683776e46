package rest

type AutoPatchTestRest struct {
	BaseEntityRest
	DisplayName             string                  `json:"displayName"`
	Description             string                  `json:"description"`
	PatchCategories         []string                `json:"patchCategories"`
	PatchSeverities         []string                `json:"patchSeverities"`
	Platform                string                  `json:"platform"`
	ApplicationType         string                  `json:"applicationType"`
	ProductIds              []int64                 `json:"productIds"`
	ComputerGroupFilterRest ComputerGroupFilterRest `json:"computerGroupFilterRest"`
	DeploymentPolicyId      int64                   `json:"deploymentPolicyId"`
	NotifyTo                []string                `json:"notifyTo"`
}
