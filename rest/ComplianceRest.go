package rest

import (
	"deployment/constant"
	"deployment/model"
)

type ComplianceRest struct {
	BaseEntityRest
	DisplayName       string               `json:"displayName"`
	Bindings          string               `json:"bindings"`
	RuleType          string               `json:"ruleType" validate:"omitempty,oneof=aaa configuration networking patching security"`
	Description       string               `json:"description"`
	Audit             string               `json:"audit"`
	Remediation       string               `json:"remediation"`
	Impact            string               `json:"impact" validate:"omitempty,oneof=high medium low"`
	Scope             int64                `json:"scope"`
	Rules             []ComplianceRuleRest `json:"rules"`
	Tags              []string             `json:"tags"`
	Disable           bool                 `json:"disable"`
	ExecutionType     string               `json:"executionType" validate:"omitempty,oneof=command script"`
	RuleExecutionType string               `json:"ruleExecutionType"`
}

type ComplianceRuleRest struct {
	OrderId                  string                         `json:"orderId"`
	CommandType              string                         `json:"commandType"`
	Command                  string                         `json:"command"`
	Key                      string                         `json:"key"`
	ComplianceRuleConditions []ComplianceRuleConditionsRest `json:"complianceRuleConditions"`
	Condition                string                         `json:"condition" validate:"omitempty,oneof=and or"`
	ScriptFile               FileMetaDataRest               `json:"scriptFile"`
}

type ComplianceRuleConditionsRest struct {
	ConditionValue string `json:"conditionValue"`
	RuleCondition  string `json:"ruleCondition"`
	Condition      string `json:"condition" validate:"omitempty,oneof=and or"`
}

func ToComplianceRule(restModel ComplianceRuleRest) model.ComplianceRule {
	var commandType model.CommandType
	var condition constant.Condition
	commandType, _ = commandType.ToCommandType(restModel.CommandType)
	condition = condition.ToCondition(restModel.Condition)
	return model.ComplianceRule{
		CommandType:              commandType,
		Command:                  restModel.Command,
		Key:                      restModel.Key,
		ComplianceRuleConditions: ToComplianceRuleConditionsRestList(restModel.ComplianceRuleConditions),
		Condition:                condition,
		ScriptFile:               ConvertToFileMetaData(restModel.ScriptFile),
	}
}

func ToComplianceRuleConditions(restModel ComplianceRuleConditionsRest) model.ComplianceRuleConditions {
	var condition constant.Condition
	var ruleCondition model.RuleCondition
	condition = condition.ToCondition(restModel.Condition)
	ruleCondition = ruleCondition.ToRuleCondition(restModel.RuleCondition)
	return model.ComplianceRuleConditions{
		RuleCondition:  ruleCondition,
		ConditionValue: restModel.ConditionValue,
		Condition:      condition,
	}
}

func ToComplianceRuleConditionsRest(restModel model.ComplianceRuleConditions) ComplianceRuleConditionsRest {
	return ComplianceRuleConditionsRest{
		ConditionValue: restModel.ConditionValue,
		Condition:      restModel.Condition.String(),
		RuleCondition:  restModel.RuleCondition.String(),
	}
}

func ToComplianceRuleRest(domainModel model.ComplianceRule) ComplianceRuleRest {
	return ComplianceRuleRest{
		CommandType:              domainModel.CommandType.String(),
		Command:                  domainModel.Command,
		Key:                      domainModel.Key,
		ComplianceRuleConditions: ToComplianceRuleConditionsList(domainModel.ComplianceRuleConditions),
		Condition:                domainModel.Condition.String(),
		ScriptFile:               ConvertToFileMetaDataRest(domainModel.ScriptFile),
	}
}

func ToComplianceRuleList(restModelList []ComplianceRuleRest) []model.ComplianceRule {
	var complianceRuleList []model.ComplianceRule
	for _, restModel := range restModelList {
		complianceRuleList = append(complianceRuleList, ToComplianceRule(restModel))
	}
	return complianceRuleList
}

func ToComplianceRuleRestList(domainModelList []model.ComplianceRule) []ComplianceRuleRest {
	var complianceRuleList []ComplianceRuleRest
	for _, domainModel := range domainModelList {
		complianceRuleList = append(complianceRuleList, ToComplianceRuleRest(domainModel))
	}
	return complianceRuleList
}

func ToComplianceRuleConditionsRestList(restModelList []ComplianceRuleConditionsRest) []model.ComplianceRuleConditions {
	var complianceRuleList []model.ComplianceRuleConditions
	for _, restModel := range restModelList {
		complianceRuleList = append(complianceRuleList, ToComplianceRuleConditions(restModel))
	}
	return complianceRuleList
}

func ToComplianceRuleConditionsList(domainModelList []model.ComplianceRuleConditions) []ComplianceRuleConditionsRest {
	var complianceRuleList []ComplianceRuleConditionsRest
	for _, domainModel := range domainModelList {
		complianceRuleList = append(complianceRuleList, ToComplianceRuleConditionsRest(domainModel))
	}
	return complianceRuleList
}
