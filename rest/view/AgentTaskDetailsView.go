package view

import "github.com/uptrace/bun"

// AgentTaskDetailsView represents the combined data from multiple tables
type AgentTaskDetailsView struct {
	bun.BaseModel `bun:"deployment.agent_task_details_view"`

	// Fields from agent_tasks table
	AgentTaskId              int64                  `bun:"id" json:"id"`
	AgentTaskName            string                 `bun:"name" json:"name"`
	AgentTaskCreatedBy       int64                  `bun:"created_by" json:"createdBy"`
	AgentTaskCreatedTime     int64                  `bun:"created_time" json:"createdTime"`
	AgentTaskUpdatedBy       int64                  `bun:"updated_by" json:"updatedBy"`
	AgentTaskUpdatedTime     int64                  `bun:"updated_time" json:"updatedTime"`
	AgentTaskOOB             bool                   `bun:"oob" json:"oob"`
	AgentTaskRemoved         bool                   `bun:"removed" json:"removed"`
	AgentTaskRefId           int64                  `bun:"ref_id" json:"refId"`
	AgentTaskRefModel        string                 `bun:"ref_model" json:"refModel"`
	AgentTaskAgentId         int64                  `bun:"agent_id" json:"agentId"`
	AgentTaskDeploymentId    int64                  `bun:"deployment_id" json:"deploymentId"`
	AgentTaskType            string                 `bun:"task_type" json:"task_type"`
	AgentTaskCustomDetails   map[string]interface{} `bun:"custom_task_details" json:"customTaskDetails"`
	AgentTaskStatus          string                 `bun:"status" json:"status"`
	AgentTaskResult          string                 `bun:"task_result" json:"task_result"`
	AgentTaskHasMultipleExec bool                   `bun:"has_multiple_execution" json:"hasMultipleExecution"`

	// Fields from patch table
	PatchId             int64  `bun:"patch_id" json:"patchId"`
	PatchName           string `bun:"patch_name" json:"patchName"`
	PatchTitle          string `bun:"patch_title" json:"patchTitle"`
	PatchDescription    string `bun:"patch_description" json:"patchDescription"`
	PatchDownloadStatus int64  `bun:"patch_download_status" json:"patchDownloadStatus"`

	// Fields from packages table
	PackageId          int64  `bun:"package_id" json:"packageId"`
	PackageName        string `bun:"package_name" json:"packageName"`
	PackageDisplayName string `bun:"package_display_name" json:"packageDisplayName"`
	PackageDescription string `bun:"package_description" json:"packageDescription"`
	PackageVersion     string `bun:"package_version" json:"packageVersion"`

	// Fields from configurations table
	ConfigurationId          int64  `bun:"configuration_id" json:"configurationId"`
	ConfigurationDisplayName string `bun:"configuration_display_name" json:"configurationDisplayName"`
	ConfigurationDescription string `bun:"configuration_description" json:"configurationDescription"`
	ConfigurationType        int64  `bun:"configuration_type" json:"configurationType"`

	// Fields from system_actions table
	SystemActionId          int64                  `bun:"system_action_id" json:"systemActionId"`
	SystemActionName        string                 `bun:"system_action_name" json:"systemActionName"`
	SystemActionDescription string                 `bun:"system_action_description" json:"systemActionDescription"`
	SystemActionDisabled    bool                   `bun:"system_action_disabled" json:"systemActionDisabled"`
	SystemActionWindowsCmd  map[string]interface{} `bun:"system_action_windows_command" json:"systemActionWindowsCommand"`
	SystemActionLinuxCmd    map[string]interface{} `bun:"system_action_linux_command" json:"systemActionLinuxCommand"`
	SystemActionMacCmd      map[string]interface{} `bun:"system_action_mac_command" json:"systemActionMacCommand"`
}
