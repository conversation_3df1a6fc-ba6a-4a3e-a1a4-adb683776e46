package view

import "github.com/uptrace/bun"

type AssetPatchCveRelation struct {
	bun.BaseModel    `bun:"deployment.view_asset_patch_cve_relation"`
	PatchName        string
	KbId             string
	SoftwareId       int
	Cve              string
	Severity         string
	Name             string
	Version          string
	Score            string
	Cvss3BaseScore   string
	Cvss2BaseScore   string
	CveContext       string
	EpssProbability  string
	CisaKnownExploit string
	DiscoveredTime   int64
	AssetId          string
	Archived         int
	HasPatch         string
}

type AssetPatchCveRelationRest struct {
	PatchName        string                 `json:"patchName"`
	KbId             string                 `json:"kbId"`
	PatchState       string                 `json:"patchState"`
	SoftwareId       int                    `json:"softwareId"`
	Cve              string                 `json:"cve"`
	Severity         string                 `json:"severity"`
	Name             string                 `json:"name"`
	Version          string                 `json:"version"`
	Score            string                 `json:"Score"`
	Cvss3BaseScore   string                 `json:"cvss3BaseScore"`
	Cvss2BaseScore   string                 `json:"cvss2BaseScore"`
	CveContext       map[string]interface{} `json:"cveContext"`
	EpssProbability  string                 `json:"epss_probability"`
	CisaKnownExploit string                 `json:"cisa_known_exploit"`
	DiscoveredTime   int64                  `json:"discoveredTime"`
	AssetId          string                 `json:"assetId"`
	Archived         int                    `json:"archived"`
	HasPatch         string                 `json:"hasPatch"`
	Id               int64                  `json:"id"`
}
