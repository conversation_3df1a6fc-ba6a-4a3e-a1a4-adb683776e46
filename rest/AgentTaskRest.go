package rest

type AgentTaskRest struct {
	BaseEntityRefModelRest
	DisplayName          string                 `json:"displayName"`
	AgentId              int64                  `json:"agentId"`
	DeploymentId         int64                  `json:"deploymentId"`
	TaskType             string                 `json:"taskType"`
	CustomTaskDetails    map[string]interface{} `json:"customTaskDetails"`
	TaskStatus           string                 `json:"taskStatus"`
	TaskResult           string                 `json:"taskResult"`
	IconFile             FileMetaDataRest       `json:"iconFile"`
	HasMultipleExecution bool                   `json:"hasMultipleExecution"`
	AssetName            string                 `json:"assetName"`
}
