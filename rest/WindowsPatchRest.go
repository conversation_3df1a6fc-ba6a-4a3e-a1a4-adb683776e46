package rest

import (
	"deployment/model"
)

type WindowsPatchRest struct {
	BaseEntityRest
	TempData                                bool                     `json:"tempData"`
	ReplaceById                             int64                    `json:"replaceById"`
	ReplaceByUuid                           string                   `json:"replaceByUuid"`
	RevisionId                              int64                    `json:"revisionId"`
	UUID                                    string                   `json:"uuid"`
	Title                                   string                   `json:"title"`
	KbId                                    string                   `json:"kbId"`
	Description                             string                   `json:"description"`
	HasReplacement                          bool                     `json:"hasReplacement"`
	Leaf                                    bool                     `json:"leaf"`
	Declined                                bool                     `json:"declined"`
	PublishState                            string                   `json:"publishState"`
	UpdateType                              string                   `json:"updateType"`
	BulletinId                              string                   `json:"bulletinId"`
	InstallationCanRequestUserInput         bool                     `json:"installationCanRequestUserInput"`
	InstallationRequiresNetworkConnectivity bool                     `json:"installationRequiresNetworkConnectivity"`
	CanUninstall                            bool                     `json:"canUninstall"`
	InstallationImpact                      string                   `json:"installationImpact"`
	RestartBehaviour                        string                   `json:"restartBehaviour"`
	SupportedLanguage                       string                   `json:"supportedLanguage"`
	LastUpdatedTime                         int64                    `json:"lastUpdatedTime"`
	WsusLastUpdatedTime                     int64                    `json:"wsusLastUpdatedTime"`
	FileDetails                             []model.PatchFileData    `json:"fileDetails"`
	SupersedesString                        string                   `json:"supersedesString"`
	Company                                 string                   `json:"company"`
	ProductFamily                           string                   `json:"productFamily"`
	Products                                string                   `json:"products"`
	ProductsUuid                            []string                 `json:"productsUuid"`
	Classification                          string                   `json:"classification"`
	CveNumber                               string                   `json:"cveNumber"`
	Severity                                string                   `json:"severity"`
	MoreInfoUrl                             string                   `json:"moreInfoUrl"`
	DownloadUrl                             []string                 `json:"downloadUrl"`
	ReleaseDate                             int64                    `json:"releaseDate"`
	Arch                                    string                   `json:"arch"`
	OsName                                  string                   `json:"osName"`
	OsArch                                  string                   `json:"osArch"`
	ProductId                               int64                    `json:"productId"`
	MsrcPatchId                             int64                    `json:"msrcPatchId"`
	AffectedProduct                         []string                 `json:"affectedProduct"`
	Rules                                   []map[string]interface{} `json:"rules"`
	KbIdToBeInstalled                       string                   `json:"kbIdToBeInstalled"`
	CabExist                                bool                     `json:"cabExist"`
	AtLeastOneFileInstallation              bool                     `json:"atLeastOneFileInstallation"`
	PackageNames                            []string                 `json:"packageNames"`
}
