package rest

import (
	"deployment/model"
)

type ThirdPartyPackageRest struct {
	BaseEntityRest
	Description      string                `json:"description"`
	Version          string                `json:"version"`
	Os               string                `json:"os"`
	Arch             string                `json:"arch"`
	LanguageCode     string                `json:"languageCode"`
	PkgFileData      []model.PatchFileData `json:"pkgFileData"`
	LatestPackageUrl string                `json:"latestPackageUrl"`
	Publisher        string                `json:"publisher"`
	SupportUrl       string                `json:"supportUrl"`
	ReleaseNote      string                `json:"releaseNote"`
	ReleaseDate      int64                 `json:"releaseDate"`
	Application      string                `json:"application"`
	CveDetails       []model.CveDetails    `json:"cveDetails"`
	Uuid             string                `json:"uuid"`
	OsVersion        string                `json:"osVersion"`
	ProductCode      string                `json:"productCode"`
	InstallCommand   string                `json:"installCommand"`
	UnInstallCommand string                `json:"unInstallCommand"`
	DetectionScript  string                `json:"detectionScript"`
}
