package rest

import "deployment/model"

type UbuntuPatchRest struct {
	BaseEntityRest
	UUID              string `json:"uuid"`
	OsVersion         string `json:"osVersion"`
	Channel           string `json:"channel"`
	Repo              string `json:"repo"`
	PackageName       string `json:"packageName"`
	Arch              string `json:"arch"`
	Version           string `json:"version"`
	Priority          string `json:"priority"`
	Section           string `json:"section"`
	Origin            string `json:"origin"`
	Depends           string `json:"depends"`
	Breaks            string `json:"breaks"`
	FileName          string `json:"fileName"`
	DownloadUrl       string `json:"downloadUrl"`
	Size              int64  `json:"size"`
	Sha1              string `json:"sha1"`
	PkgAndVersion     string `json:"pkgAndVersion"`
	PkgNameWithDistro string `json:"pkgNameWithDistro"`
	Downloadable      bool   `json:"downloadable"`
	ReleaseDate       int64  `json:"releaseDate"`
}

type CPUbuntuPatchRest struct {
	model.BaseEntityModel
	UUID              string `json:"uuid"`
	OsVersion         string `json:"os_version"`
	Channel           string `json:"channel"`
	Repo              string `json:"repo"`
	PackageName       string `json:"Package"`
	Arch              string `json:"Architecture"`
	Version           string `json:"Version"`
	Priority          string `json:"Priority"`
	Section           string `json:"Section"`
	Origin            string `json:"Origin"`
	Depends           string `json:"Depends"`
	Breaks            string `json:"Breaks"`
	FileName          string `json:"FileName"`
	DownloadUrl       string `json:"DownloadUrl"`
	Size              int64  `json:"Size"`
	Sha1              string `json:"Sha1"`
	PkgAndVersion     string `json:"PkgAndVersion"`
	PkgNameWithDistro string `json:"PkgNameWithDistro"`
	Downloadable      bool   `json:"Downloadable"`
	ReleaseDate       int64  `json:"ReleaseDate"`
}
