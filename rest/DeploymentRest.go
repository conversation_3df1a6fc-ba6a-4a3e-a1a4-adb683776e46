package rest

type DeploymentRest struct {
	BaseEntityRefModelRest
	AgentScopeFilterRest
	DisplayName     string `json:"displayName"`
	Description     string `json:"description"`
	DeploymentType  string `json:"deploymentType" validate:"omitempty,oneof=install uninstall upgrade"`
	DeploymentStage string `json:"deploymentStage" validate:"omitempty,oneof=initiated in_progress completed draft cancelled idle"`
	StartTime       int64  `json:"startTime"`
	// RefIds Will be package or patch id
	RefIds                  []int64  `json:"refIds"`
	ComputerIds             []int64  `json:"computerIds"`
	ComputerGroupIds        []int64  `json:"computerGroupIds"`
	DeploymentPolicyId      int64    `json:"deploymentPolicyId"`
	NotifyEmailIds          []string `json:"notifyEmailIds"`
	RetryCount              int      `json:"retryCount"`
	TotalTaskCount          int      `json:"totalTaskCount"`
	CompletedTaskCount      int      `json:"completedTaskCount"`
	PendingTaskCount        int      `json:"pendingTaskCount"`
	SuccessTaskCount        int      `json:"successTaskCount"`
	FailedTaskCount         int      `json:"failedTaskCount"`
	IsPkgSelectAsBundle     bool     `json:"isPkgSelectAsBundle"`
	IsSelfServiceDeployment bool     `json:"isSelfServiceDeployment"`
	NextExecutionTime       int64    `json:"nextExecutionTime"`
	LastExecutionTime       int64    `json:"lastExecutionTime"`
	Origin                  string   `json:"origin"`
	IsRecurringDeployment   bool     `json:"isRecurringDeployment"`
}
