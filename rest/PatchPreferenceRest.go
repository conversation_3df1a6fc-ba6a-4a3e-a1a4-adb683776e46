package rest

import "deployment/model"

type PatchPreferenceRest struct {
	BaseEntityRest
	PatchApprovalPolicy string `json:"patchApprovalPolicy"`
	//Patch setting
	EnablePatching           bool              `json:"enablePatching"`
	EnableThirdPartyPatching bool              `json:"enableThirdPartyPatching"`
	EnableScheduling         bool              `json:"enableScheduling"`
	Schedule                 PatchScheduleRest `json:"scheduleTime"`
	PatchApprovalSchedule    PatchScheduleRest `json:"patchApprovalScheduleTime"`
	ZeroTouchSchedule        PatchScheduleRest `json:"zeroTouchDeploymentScheduleTime"`
	EnabledCategoryList      []string          `json:"enabledCategoryList"`
	EnabledPatchOs           []string          `json:"enabledPatchOs"`
	//System Health Related fields
	//Criteria for tagging system as "Highly Vulnerable"
	HighlyVulnerableCriticalPatch    int `json:"highlyVulnerableCriticalPatch"`
	HighlyVulnerableImportantPatches int `json:"highlyVulnerableImportantPatches"`
	HighlyVulnerableModeratePatch    int `json:"highlyVulnerableModeratePatch"`
	HighlyVulnerableLowPatch         int `json:"highlyVulnerableLowPatch"`
	//Criteria for tagging system as "Vulnerable"
	VulnerableCriticalPatch    int `json:"vulnerableCriticalPatch"`
	VulnerableImportantPatches int `json:"vulnerableImportantPatches"`
	VulnerableModeratePatch    int `json:"vulnerableModeratePatch"`
	VulnerableLowPatch         int `json:"vulnerableLowPatch"`
	//Consider Only Approved Patch For system health
	OnlyApprovedPatch bool `json:"onlyApprovedPatch"`
	//Patch Sync Status
	IsPatchSyncRunning bool  `json:"isPatchSyncRunning"`
	LastPatchSyncTime  int64 `json:"lastPatchSyncTime"`
}

type PatchScheduleRest struct {
	Type  model.ScheduleType `json:"type"`  // "INTERVAL" or "DAILY"
	Value int64              `json:"value"` // Used only when type == "INTERVAL"
}
