package rest

import (
	"deployment/common"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type AssetPatchRelationRest struct {
	BaseEntityRest
	AgentScopeFilterRest
	PatchId         int64                  `json:"patchId"`
	PatchIds        []int64                `json:"patchIds"`
	AssetId         int64                  `json:"assetId"`
	PatchState      string                 `json:"patchState"`
	IsOld           bool                   `json:"isOld"`
	IsDeclined      bool                   `json:"isDeclined"`
	Endpoint        map[string]interface{} `json:"endpoint"`
	Patch           PatchRest              `json:"patch"`
	ExceptionType   string                 `json:"exceptionType"`
	ExceptionReason string                 `json:"exceptionReason"`
}

func ConvertJsonToAgentPatchRest(w http.ResponseWriter, r *http.Request, restModel AssetPatchRelationRest) (AssetPatchRelationRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &restModel)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid JSON format", http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			return restModel, err
		}
		return restModel, err
	}

	v := validator.New()
	err = v.Struct(restModel)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err := fmt.Fprintf(w, jsonData)
			if err != nil {
				return restModel, err
			}
			break
		}
	}

	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		return restModel, err
	}
	restModel.PatchMap = patchMap

	return restModel, err
}
