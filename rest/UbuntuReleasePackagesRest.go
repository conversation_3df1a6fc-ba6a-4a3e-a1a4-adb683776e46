package rest

type UbuntuReleasePackageRest struct {
	BaseEntityRest
	NoticeID                string `json:"noticeId"`
	OSVersion               string `json:"osVersion"`
	Description             string `json:"description"`
	IsSource                bool   `json:"isSource"`
	IsVisible               bool   `json:"isVisible"`
	Pocket                  string `json:"pocket"`
	SourceLink              string `json:"sourceLink"`
	Version                 string `json:"version"`
	VersionLink             string `json:"versionLink"`
	NameAndVersion          string `json:"nameAndVersion"`
	NameAndVersionUniqueKey string `json:"nameAndVersionUniqueKey"`
}
