package rest

import "deployment/model"

type FileDataRest struct {
	BaseEntityRest
	RealName  string `json:"realName"`
	RefName   string `json:"refName"`
	CheckSum  string `json:"checkSum"`
	Size      int64  `json:"size"`
	SignedUrl string `json:"signedUrl"`
}

type FileMetaDataRest struct {
	RealName   string `json:"realName"`
	RefName    string `json:"refName"`
	Url        string `json:"url"`
	LocationId int64  `json:"locationId"`
}

func ConvertToFileMetaData(fileDataRest FileMetaDataRest) model.FileMetaData {
	return model.FileMetaData{
		RealName:   fileDataRest.RealName,
		RefName:    fileDataRest.RefName,
		Url:        fileDataRest.Url,
		LocationId: fileDataRest.LocationId,
	}
}

func ConvertToFileMetaDataList(fileDataRest []FileMetaDataRest) []model.FileMetaData {
	var fileMetaDataList []model.FileMetaData
	for _, dataRest := range fileDataRest {
		fileMetaDataList = append(fileMetaDataList, ConvertToFileMetaData(dataRest))
	}
	return fileMetaDataList
}

func ConvertToFileMetaDataRest(fileData model.FileMetaData) FileMetaDataRest {
	return FileMetaDataRest{
		RealName:   fileData.RealName,
		RefName:    fileData.RefName,
		Url:        fileData.Url,
		LocationId: fileData.LocationId,
	}
}
