package rest

import (
	"deployment/common"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type ScannedPatchData struct {
	PatchData           map[string]interface{} `json:"patchData"`
	InstalledList       []PatchDataRest        `json:"installedPatchList"`
	MissingList         []PatchDataRest        `json:"missingPatchList"`
	LinuxMissingPatches []int64                `json:"linuxMissingPatches"`
	MacMissingPatches   []string               `json:"macMissingPatches"`
}

type PatchDataRest struct {
	UUID         string `json:"uuid"`
	KbId         string `json:"kbId"`
	IsThirdParty bool   `json:"isThirdParty"`
}

func ConvertJsonToScannedPatchData(w http.ResponseWriter, r *http.Request, patchData ScannedPatchData) (ScannedPatchData, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &patchData)
	v := validator.New()
	err = v.Struct(patchData)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprintf(w, jsonData)
			break
		}
	}
	return patchData, err
}
