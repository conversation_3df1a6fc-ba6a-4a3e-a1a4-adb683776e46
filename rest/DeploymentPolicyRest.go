package rest

type DeploymentPolicyRest struct {
	BaseEntityRefModelRest
	DisplayName          string   `json:"displayName"`
	Description          string   `json:"description"`
	Type                 string   `json:"type" validate:"omitempty,oneof=instant schedule "`
	InitiateDeploymentOn string   `json:"InitiateDeploymentOn" validate:"omitempty,oneof=on_next_cycle on_system_start_up recurring"`
	DeploymentDays       []string `json:"deploymentDays"`
	DeploymentTimeFrom   int64    `json:"deploymentTimeFrom"`
	DeploymentTimeTo     int64    `json:"deploymentTimeTo"`
	AfterEveryTimeUnit   string   `json:"afterEveryTimeUnit" validate:"omitempty,oneof=hour day month year"`
	AfterEveryTime       int64    `json:"afterEveryTime"`
	RestartType          string   `json:"restartType"`
}
