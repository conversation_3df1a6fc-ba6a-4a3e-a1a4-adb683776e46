package rest

type PackageRest struct {
	BaseEntityRest
	DisplayName           string             `json:"displayName"`
	Description           string             `json:"description"`
	Os                    string             `json:"os" validate:"omitempty,oneof=windows linux mac"`
	OsArch                string             `json:"osArch" validate:"omitempty,oneof=x64 x86 amd64"`
	Version               string             `json:"version"`
	PkgType               string             `json:"pkgType" validate:"omitempty,oneof=exe msi zip script application"`
	PkgLocation           string             `json:"pkgLocation" validate:"omitempty,oneof=local_dir shared_dir public_url"`
	PkgFilePath           FileMetaDataRest   `json:"pkgFilePath"`
	PkgFilePathList       []FileMetaDataRest `json:"pkgFilePathList"`
	InstallCommand        string             `json:"installCommand,omitempty"`
	UninstallCommand      string             `json:"uninstallCommand,omitempty"`
	UpgradeCommand        string             `json:"upgradeCommand,omitempty"`
	IconFile              FileMetaDataRest   `json:"iconFile"`
	SelfServiceSupported  bool               `json:"selfServiceSupported"`
	UseUserDefinedCommand bool               `json:"useUserDefinedCommand"`
	Tags                  []string           `json:"tags"`
}
