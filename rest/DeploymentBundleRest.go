package rest

type DeploymentBundleRest struct {
	BaseEntityRest
	Description    string           `json:"description"`
	ReferenceIds   []int64          `json:"referenceIds"`
	Os             string           `json:"os"`
	RefModel       string           `json:"refModel"`
	IconFile       FileMetaDataRest `json:"iconFile"`
	ComplianceType string           `json:"complianceType" validate:"omitempty,oneof=cis hippa fima iso pci rbi soc_2""`
}
