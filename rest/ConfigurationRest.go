package rest

import "deployment/model"

type ConfigurationActionRest struct {
	OrderId     int              `json:"orderId"`
	CommandType string           `json:"commandType"`
	Command     string           `json:"command"`
	ScriptFile  FileMetaDataRest `json:"scriptFile"`
}

type ConfigurationRest struct {
	BaseEntityRest
	DisplayName          string                    `json:"displayName"`
	Description          string                    `json:"description"`
	Os                   string                    `json:"os"`
	Arch                 string                    `json:"arch"`
	ExecutionType        string                    `json:"configurationType"`
	ConfigurationActions []ConfigurationActionRest `json:"ConfigurationActions"`
	SelfServiceSupported bool                      `json:"selfServiceSupported"`
	IsRemediation        bool                      `json:"isRemediation"`
	Tags                 []string                  `json:"tags"`
}

func ToConfigurationActionRest(c model.ConfigurationAction) ConfigurationActionRest {
	return ConfigurationActionRest{
		OrderId:     c.OrderId,
		CommandType: c.CommandType.String(),
		Command:     c.Command,
		ScriptFile:  ConvertToFileMetaDataRest(c.ScriptFile),
	}
}
func ToConfigurationActionRestList(actions []model.ConfigurationAction) []ConfigurationActionRest {
	var actionRestList []ConfigurationActionRest
	if len(actions) > 0 {
		for _, action := range actions {
			actionRest := ToConfigurationActionRest(action)
			actionRestList = append(actionRestList, actionRest)
		}
	}
	return actionRestList
}

func ToConfiguration(c ConfigurationActionRest) model.ConfigurationAction {
	var commandType model.CommandType
	commandType, _ = commandType.ToCommandType(c.CommandType)
	return model.ConfigurationAction{
		OrderId:     c.OrderId,
		CommandType: commandType,
		Command:     c.Command,
		ScriptFile:  ConvertToFileMetaData(c.ScriptFile),
	}
}

func ToConfigurationList(actions []ConfigurationActionRest) []model.ConfigurationAction {
	var configurationList []model.ConfigurationAction
	if len(actions) > 0 {
		for _, actionRest := range actions {
			configuration := ToConfiguration(actionRest)
			configurationList = append(configurationList, configuration)
		}
	}
	return configurationList
}
