package rest

import "deployment/model"

type SchedulerConfigRest struct {
	StartAt                 int64   `json:"startAt"`
	ScheduleType            string  `json:"scheduleType"`
	RecurringStartingHour   int64   `json:"recurringStartingHour"`
	RecurringStartingMinute int64   `json:"recurringStartingMinute"`
	SpecificInterval        int64   `json:"specificInterval"`
	DaysOfMonth             []int64 `json:"daysOfMonth"`
	DaysOfWeek              []int64 `json:"daysOfWeek"`
}

func ToSchedulerConfigRest(schedulerConfig model.SchedulerConfig) SchedulerConfigRest {
	return SchedulerConfigRest{
		StartAt:                 schedulerConfig.StartAt,
		ScheduleType:            schedulerConfig.ScheduleType.String(),
		RecurringStartingHour:   schedulerConfig.RecurringStartingHour,
		RecurringStartingMinute: schedulerConfig.RecurringStartingMinute,
		SpecificInterval:        schedulerConfig.SpecificInterval,
		DaysOfMonth:             schedulerConfig.DaysOfMonth,
		DaysOfWeek:              schedulerConfig.DaysOfWeek,
	}
}

func ToSchedulerConfig(schedulerConfig SchedulerConfigRest) model.SchedulerConfig {
	var schedulerType model.ScheduleType
	schedulerType = schedulerType.ToSchedulerType(schedulerConfig.ScheduleType)
	return model.SchedulerConfig{
		StartAt:                 schedulerConfig.StartAt,
		ScheduleType:            schedulerType,
		RecurringStartingHour:   schedulerConfig.RecurringStartingHour,
		RecurringStartingMinute: schedulerConfig.RecurringStartingMinute,
		SpecificInterval:        schedulerConfig.SpecificInterval,
		DaysOfMonth:             schedulerConfig.DaysOfMonth,
		DaysOfWeek:              schedulerConfig.DaysOfWeek,
	}
}
