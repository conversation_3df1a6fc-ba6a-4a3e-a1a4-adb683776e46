package rest

import "deployment/model"

type MacPatchRest struct {
	BaseEntityRest
	OsVersion            string                `json:"osVersion"`
	ProductKey           string                `json:"productKey"`
	ReleaseDate          int64                 `json:"releaseDate"`
	Description          string                `json:"description"`
	Version              string                `json:"version"`
	DistributionFileName string                `json:"distributionFileName"`
	ProductType          string                `json:"productType"`
	Packages             []model.PatchFileData `json:"packages"`
}
