package rest

import "deployment/model"

type MsrcVulnerabilityRest struct {
	Id              int64                  `json:"id"`
	Name            string                 `json:"name"`
	CreatedById     int64                  `json:"createdById"`
	CreatedTime     int64                  `json:"createdTime"`
	UpdatedById     int64                  `json:"updatedById"`
	UpdatedTime     int64                  `json:"updatedTime"`
	Removed         bool                   `json:"removed"`
	OOB             bool                   `json:"oob"`
	CVE             string                 `json:"cve"`
	Description     string                 `json:"description"`
	FAQs            []string               `json:"faqs"`
	Tag             string                 `json:"tag"`
	CNA             string                 `json:"cna"`
	ExploitStatus   string                 `json:"exploitStatus"`
	Mitigation      string                 `json:"mitigation"`
	Workaround      string                 `json:"workaround"`
	Products        []model.Product        `json:"products"`
	URL             string                 `json:"url"`
	Acknowledgments []model.Acknowledgment `json:"acknowledgments"`
	Revisions       []model.Revision       `json:"revisions"`
}
